 # IP/Patents/patent_db.py
"""
Patent Database Operations Module

This module provides functions to interact with the PostgreSQL database
for patent grant and CPC data.
"""

import os
import psycopg2
import psycopg2.extras
import logging
import json # For handling complex fields like lists of inventors/applicants if needed
import pandas as pd
from tqdm import tqdm
from concurrent.futures import ProcessPoolExecutor
import multiprocessing
import numpy as np
from IP.Patents.patent_db_grant import get_db_connection
import atexit # Import atexit for pool shutdown registration


# Configure logging
log_file_path = os.path.join(os.path.dirname(__file__), '..', '..', 'logs', 'patent_db.log')
os.makedirs(os.path.dirname(log_file_path), exist_ok=True)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file_path),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Global multiprocessing pool for CPC/IPC assignment processing
_cpc_executor_pool = None

def get_cpc_executor_pool():
    """Initializes and returns the global ProcessPoolExecutor."""
    global _cpc_executor_pool
    if _cpc_executor_pool is None:
        num_workers = multiprocessing.cpu_count()
        logger.info(f"Initializing global CPC executor pool with {num_workers} workers.")
        _cpc_executor_pool = ProcessPoolExecutor(max_workers=num_workers)
        logger.info("Global CPC executor pool initialized.")
    return _cpc_executor_pool

def shutdown_cpc_executor_pool():
    """Shuts down the global ProcessPoolExecutor."""
    global _cpc_executor_pool
    if _cpc_executor_pool:
        logger.info("Shutting down global CPC executor pool...")
        _cpc_executor_pool.shutdown(wait=True)
        _cpc_executor_pool = None
        logger.info("Global CPC executor pool shut down.")

# Register shutdown function to be called on script exitF
atexit.register(shutdown_cpc_executor_pool)

def process_patent_records_chunk(patent_records_chunk):
    """
    Process a chunk of patent records for CPC/IPC assignments in a separate process.

    Args:
        patent_records_chunk (list): A chunk of patent records.
                                     The global caches (_cpc_ipc_definitions_df, _available_cpc_dates, _available_ipc_dates)
                                     are expected to be loaded via the initializer function for the pool.

    Returns:
        list: assignments_to_insert
    """
    assignments_to_insert = []

    for record in patent_records_chunk:
        patents_id = record.get('id') # Directly use the 'id' from the record
        if not patents_id:
            logger.warning(f"Record missing 'id' field: {record.get('document_id')}")
            continue

        for clean_cpc_ipc_entry in record.get('clean_cpc_ipc_entries', []):
            definition_id = clean_cpc_ipc_entry.get('id')
            if definition_id:
                assignments_to_insert.append((patents_id, definition_id))
        
    return assignments_to_insert


def upsert_patent_cpc_ipc_assignments(patent_records: list, mode: str) -> int:
    """
    Upserts CPC/IPC assignments into the patents_cpc_assignments table
    by looking up definitions in patents_cpc_ipc_definitions based on
    closest publish date and fallback rules.

    Args:
        patent_records (list): A list of dictionaries, where each dictionary
                                represents a patent record. Expected to have
                                'document_id', 'publication_date', 'cpc' (list),
                                and 'ipc' (list) keys.

    Returns:
        int: The number of classification assignment entries successfully inserted.
    """
    if not patent_records:
        print("No patent records provided for classification assignment upsert.")
        return 0

    conn = None
    cursor = None
    inserted_assignments_count = 0
    all_assignments_to_insert = []

    table_name = "patents_cpc_ipc_assignments"
    if mode == "all":
        table_name += "_all"

    sql_insert_assignment = f"""
        INSERT INTO {table_name} (patents_id, cpc_ipc_id)
        VALUES (%s, %s)
        ON CONFLICT (patents_id, cpc_ipc_id) DO NOTHING;
    """

    try:
        conn = get_db_connection()
        if conn is None:
            print("Failed to get database connection for classification assignment upsert. Aborting.")
            return 0

        cursor = conn.cursor()

        # Phase 1: No longer needed as 'id' is now part of patent_records directly.
        # Phase 2: Process records and build assignment list with parallel processing
        logger.info(f"Processing {len(patent_records)} patent records for CPC/IPC assignments...")

        # Determine if we should use parallel processing
        CHUNK_SIZE = max(100, len(patent_records) // (multiprocessing.cpu_count() * 2))  # Dynamic chunk size

        logger.info(f"Using parallel processing with {multiprocessing.cpu_count()} processes and chunk size {CHUNK_SIZE}")

        # Split patent_records into chunks for parallel processing
        chunks = [patent_records[i:i + CHUNK_SIZE] for i in range(0, len(patent_records), CHUNK_SIZE)]

        # Get the global executor pool, ensuring caches are loaded in workers
        executor = get_cpc_executor_pool()

        # Process chunks in parallel using the global pool
        results = list(tqdm(
            executor.map(process_patent_records_chunk, chunks),
            total=len(chunks),
            desc="Processing CPC assignments chunks",
            unit="chunk"
        ))

        # Combine results from all processes
        for chunk_assignments in results:
            all_assignments_to_insert.extend(chunk_assignments)

        if not all_assignments_to_insert:
            print("No classification assignments prepared for insertion after processing all records.")
        else:
            print(f"Attempting to insert {len(all_assignments_to_insert)} classification assignment entries.")
            psycopg2.extras.execute_batch(cursor, sql_insert_assignment, all_assignments_to_insert, page_size=1000)
            conn.commit()
            inserted_assignments_count = len(all_assignments_to_insert)
            print(f"Successfully processed {inserted_assignments_count} classification assignments for insertion (some may have been skipped due to conflict).")


    except psycopg2.DatabaseError as db_err:
        print(f"Database error during classification assignment upsert: {db_err}")
        if conn:
            conn.rollback()
        inserted_assignments_count = 0
    except Exception as e:
        print(f"Unexpected error during classification assignment upsert: {e}")
        if conn:
            conn.rollback()
        inserted_assignments_count = 0
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()
            # print("Database connection closed after classification assignment attempt.")

    return inserted_assignments_count