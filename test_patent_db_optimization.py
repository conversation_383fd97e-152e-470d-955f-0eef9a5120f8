#!/usr/bin/env python3
"""
Test script to demonstrate the patent database optimizations.

This script shows how to use the optimized functions and demonstrates
the performance improvements.
"""

import sys
import os
import time
from datetime import datetime

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from IP.Patents.patent_db_cpc import (
    load_cpc_ipc_definitions_cache,
    find_definition_id,
    upsert_patent_cpc_ipc_assignments
)

def test_cache_loading():
    """Test the optimized cache loading with memory reporting."""
    print("=" * 60)
    print("TESTING OPTIMIZED CACHE LOADING")
    print("=" * 60)
    
    start_time = time.time()
    success = load_cpc_ipc_definitions_cache()
    end_time = time.time()
    
    if success:
        print(f"✅ Cache loaded successfully in {end_time - start_time:.2f} seconds")
        print("📊 Check the logs above for detailed memory usage information")
    else:
        print("❌ Cache loading failed")
    
    return success

def test_lookup_performance():
    """Test the optimized lookup performance."""
    print("\n" + "=" * 60)
    print("TESTING OPTIMIZED LOOKUP PERFORMANCE")
    print("=" * 60)
    
    # Sample classification data for testing
    test_cases = [
        {
            'classification_type': 'CPC',
            'data': {
                'section': 'A',
                'class': '01',
                'subclass': 'B',
                'main_group': '1',
                'subgroup': '00'
            }
        },
        {
            'classification_type': 'IPC',
            'data': {
                'section': 'H',
                'class': '04',
                'subclass': 'L',
                'main_group': '27',
                'subgroup': '15'
            }
        }
    ]
    
    # Use a recent date for testing
    test_date = datetime(2023, 1, 1).date()
    
    print(f"Testing {len(test_cases)} lookup cases...")
    
    total_time = 0
    successful_lookups = 0
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🔍 Test Case {i}: {test_case['classification_type']} lookup")
        print(f"   Data: {test_case['data']}")
        
        start_time = time.time()
        definition_id, status = find_definition_id(
            None, None,  # conn and cursor not needed in optimized version
            test_case['classification_type'],
            test_case['data'],
            test_date
        )
        end_time = time.time()
        
        lookup_time = (end_time - start_time) * 1000  # Convert to milliseconds
        total_time += lookup_time
        
        if definition_id:
            print(f"   ✅ Found ID: {definition_id} (Status: {status})")
            print(f"   ⚡ Lookup time: {lookup_time:.3f} ms")
            successful_lookups += 1
        else:
            print(f"   ❌ Not found (Status: {status})")
            print(f"   ⚡ Lookup time: {lookup_time:.3f} ms")
    
    avg_time = total_time / len(test_cases)
    print(f"\n📈 PERFORMANCE SUMMARY:")
    print(f"   • Total lookups: {len(test_cases)}")
    print(f"   • Successful: {successful_lookups}")
    print(f"   • Average lookup time: {avg_time:.3f} ms")
    print(f"   • Total time: {total_time:.3f} ms")

def test_parallel_processing_demo():
    """Demonstrate parallel processing capabilities."""
    print("\n" + "=" * 60)
    print("PARALLEL PROCESSING DEMONSTRATION")
    print("=" * 60)
    
    # Create sample patent records for testing
    sample_records = []
    for i in range(5):  # Small sample for demo
        sample_records.append({
            'document_id': f'US{10000000 + i}',
            'publication_date': '2023-01-01',
            'cpc': [
                {
                    'section': 'A',
                    'class': '01',
                    'subclass': 'B',
                    'main_group': '1',
                    'subgroup': '00'
                }
            ],
            'ipc': []
        })
    
    print(f"📦 Created {len(sample_records)} sample patent records")
    print("🔧 Note: This is a small demo. Parallel processing activates for >1000 records")
    print("💡 The function will use sequential processing for this small dataset")
    
    # Note: This would require a database connection to actually run
    print("\n⚠️  To test parallel processing:")
    print("   1. Ensure database connection is available")
    print("   2. Use a dataset with >1000 patent records")
    print("   3. Monitor CPU usage during processing")
    print("   4. Check logs for parallel processing messages")

def main():
    """Main test function."""
    print("🚀 PATENT DATABASE OPTIMIZATION TEST")
    print("=" * 60)
    print("This script demonstrates the optimizations made to patent_db.py")
    print("including memory optimization, fast lookups, and parallel processing.")
    print()
    
    try:
        # Test 1: Cache loading with memory optimization
        cache_success = test_cache_loading()
        
        if cache_success:
            # Test 2: Lookup performance
            test_lookup_performance()
        else:
            print("⚠️  Skipping lookup tests due to cache loading failure")
            print("💡 This might be due to missing database connection")
        
        # Test 3: Parallel processing demo
        test_parallel_processing_demo()
        
        print("\n" + "=" * 60)
        print("✅ OPTIMIZATION TEST COMPLETED")
        print("=" * 60)
        print("Key optimizations demonstrated:")
        print("• Memory-efficient cache loading with categorical data types")
        print("• Fast lookup using concatenation column for initial filtering")
        print("• Three-stage filtering strategy for optimal performance")
        print("• Parallel processing capability for large datasets")
        print("• Comprehensive memory usage reporting")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        print("💡 This might be due to missing database connection or environment setup")
        print("🔧 Ensure your .env file is configured with database credentials")

if __name__ == "__main__":
    main()
