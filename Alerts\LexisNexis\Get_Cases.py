from selenium.webdriver.common.by import By
from selenium.webdriver.common.action_chains import Action<PERSON>hains
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
from selenium.common.exceptions import NoSuchElementException
import pandas as pd
from Common.Constants import court_mapping
import traceback
from ..Chrome_Driver import move_mouse_to, random_delay
from .Search_Cases import  wait_for_results_to_stabilize
from .Get_Case_Details import get_case_details
from .Get_Case_Steps import get_case_steps_and_files
from logdata import log_message
from ..Save_Browser import save_browser_state
from FileManagement.NAS import NASConnection


def lexis_get_cases(driver, case_info, cases_df, steps_df, df_lexis_stats, nas: NASConnection, file_type, refresh_days_threshold, redo):
    # Loop through all pages
    try:
        page_number = 1
        while True:
            # Process the current page
            # Wait for the elements to be present and stable
            results = wait_for_results_to_stabilize(driver)
            print(f"Search results obtained for {case_info}, page {page_number}")

            if len(results) == 0:
                no_results = driver.find_elements(By.CLASS_NAME, 'results-list-container')
                if "No documents found in" in no_results[0].text:
                    log_message(f"No results found for {case_info}")
                    return cases_df, steps_df, df_lexis_stats, True
                
                error_heading = driver.find_elements(By.CLASS_NAME, 'errorHeading')
                if error_heading:
                    if 'Search Limit Exceeded' in error_heading.text:
                        log_message(f"Search Limit Exceeded.")
                        exit()
                    else:
                        log_message(f"Error lexis_get_cases: {error_heading.text}")
                        exit()
            else:
                log_message(f"{len(results)} results on Page {page_number}")

            for i, _ in enumerate(results):
                random_delay()
                # Re-acquire the elements to avoid StaleElementReferenceException
                results = wait_for_results_to_stabilize(driver)
                element = results[i]
                link_element = element.find_element(By.CSS_SELECTOR, 'h2.doc-title a')
                driver.execute_script("arguments[0].scrollIntoView(); window.scrollBy(0, -150);", link_element) # puts the link_element in view
                element_metadata = element.find_element(By.CLASS_NAME, 'metadata')
                spans = element_metadata.find_elements(By.TAG_NAME, 'span')
                court = spans[0].text.strip()
                court = court_mapping.get(court, court) # If not found, keep the original court name
                date_filed = pd.to_datetime(spans[1].text.strip(), errors='coerce').date()
                docket_number = spans[2].text.strip().replace("CV", "cv").replace("cv", "-cv-") # add backt the missing -
                if "cv" not in docket_number.lower():
                    log_message(f"  {i+1} Skipping case {docket_number} because it does not contain 'cv'")
                    continue
                if "/" in docket_number:
                    docket_number = docket_number.split("/")[1]
                while len(docket_number) < 13: # add missing 0 if any
                    docket_number = docket_number.replace("-cv-", "-cv-0")
                title = link_element.text

                index = cases_df[(cases_df['docket'] == docket_number) & (cases_df['date_filed'] == date_filed)].index
                if len(index) == 0:
                    new_row = pd.DataFrame({
                        'date_filed': [date_filed],
                        'docket': [docket_number],
                        'court': [court],
                        'title': [title], 
                        'file_status': [None], 
                        'images': ['{}'],
                        'validation_status': [''],
                        'images_status': ['{}']
                    })
                    cases_df = pd.concat([cases_df, new_row], ignore_index=True)
                    index = cases_df.index[-1]
                else:
                    index = index[0]
                    cases_df.loc[index, 'court'] = court
                    cases_df.loc[index, 'title'] = title

                # Case is already in database and not failed and file_status (of case in database) is more than file_type (that we want now)
                if not redo and cases_df.loc[index, 'file_status'] != None and "Failed" not in cases_df.loc[index, 'file_status'] and (cases_df.loc[index, 'file_status'] == "all" or \
                                                                                                                                        file_type in cases_df.loc[index, 'file_status'] or \
                                                                                                                                        (file_type == "0" and "1" in cases_df.loc[index, 'file_status'])):
                    log_message(f'  {i+1} Case {docket_number} - Already downloaded. Skipping...')
                    continue

                move_mouse_to(driver, link_element)
                actions = ActionChains(driver)
                actions.key_down(Keys.CONTROL).click(link_element).key_up(Keys.CONTROL).perform()  # on a new tab
                
                cases_df, steps_df, df_lexis_stats = lexis_get_a_case(i, driver, cases_df, steps_df, df_lexis_stats, index, docket_number, court, date_filed, file_type, nas, refresh_days_threshold)

            # Try to go to the next page
            has_next_page = go_to_next_page(driver)
            if not has_next_page and page_number < 30:
                # No more pages; exit the loop
                break

            page_number += 1
    except Exception as e:
        error_msg = str(e)
        try:
            error_dir = save_browser_state(driver, error_msg, traceback.format_exc())
            log_message(f"Error state saved to: {error_dir}", level='ERROR')
        except Exception as save_error:
            log_message(f"Failed to save error state: {save_error}", level='ERROR')
        
        # log_message(f"Error lexis_get_cases: {e}", level='ERROR')
        log_message(f"Error lexis_get_cases: {error_msg}", level='ERROR')
        log_message(f"Traceback:\n{traceback.format_exc()}", level='ERROR')
        return cases_df, steps_df, df_lexis_stats, False
    
    return cases_df, steps_df, df_lexis_stats, True



def lexis_get_a_case(i, driver, cases_df, steps_df, df_lexis_stats, index, docket_number, court, date_filed, file_type, nas: NASConnection, refresh_days_threshold, progress_callback=print):
    # Added progress_callback argument, defaulting to standard print
    random_delay()
    driver.switch_to.window(driver.window_handles[-1])  # Switch to the last tab

    # Get case details
    refresh = get_case_details(cases_df, index, driver, docket_number, court, refresh_days_threshold)


    log_message(f'  {i+1}.1 Case {docket_number} - Case found and details obtained. Refreshed: {refresh}')

    # Get case steps and files
    steps_df, download_status, pacer_case_count, files_downloaded = get_case_steps_and_files(driver, docket_number, court, date_filed, steps_df, file_type, nas)
    
    if download_status == False:
        cases_df.loc[index, 'file_status'] = "Failed: " + file_type
    else:
        cases_df.loc[index, 'file_status'] = file_type

    log_message(f'  {i+1}.2 Case {docket_number} - Download status: {download_status} with {pacer_case_count} pacer files downloaded.')
    df_lexis_stats = pd.concat([df_lexis_stats, pd.DataFrame({'date_filed': [date_filed], 'docket': [docket_number], 'court': [court], 'pacer_refresh': [refresh], 'pacer_file': [pacer_case_count], 'total_file': [files_downloaded]})], ignore_index=True)

    # Go back to search results, i.e. close the new tab
    random_delay()
    if len(driver.window_handles) > 1:
        driver.close()
    driver.switch_to.window(driver.window_handles[0])   # Switch back to the first tab
    random_delay()

    return cases_df, steps_df, df_lexis_stats

def go_to_next_page(driver):
    # Wait for the pagination navigation to be present
    wait = WebDriverWait(driver, 10)
    pagination_nav = wait.until(EC.presence_of_element_located((By.XPATH, "//nav[@role='navigation' and contains(@class, 'pagination')]")))

    try:
        # Try to find the active "Next" button and click it
        next_button = pagination_nav.find_element(By.XPATH, ".//a[@data-action='nextpage' and contains(@class, 'action')]")
        move_mouse_to(driver, next_button)
        next_button.click()
        random_delay()
        return True 

    except NoSuchElementException:
        # "Next" button is not found; we've reached the last page
        return False