from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException
import pandas as pd
import json
from datetime import date, datetime
import traceback
from Alerts.Chrome_Driver import move_mouse_to, random_delay
import re
from logdata import log_message


def get_case_details(df, index, driver, docket_number, court, refresh_days_threshold):
    refresh = False
    ### Get case details
    try:
        # Locate the table within the div with id "Header"
        header_div = WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "Header")))
        refresh_date_str = driver.find_element(By.CSS_SELECTOR, 'p.SS_DocumentInfo span.SS_bf').text.split(' ')[-1]
        refresh_date = pd.to_datetime(refresh_date_str)

        print(f"type(refresh_days_threshold): {type(refresh_days_threshold)}")

        if 'Class Code: Open' in header_div.text and (date.today() - refresh_date.date()).days >= int(refresh_days_threshold):
            log_message(f"Case {docket_number} is open and needs to be refreshed")
            # Get current number of steps in the table
            steps_table = WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.CLASS_NAME, "SS_DataTable")))
            current_steps_count = len(steps_table.find_elements(By.TAG_NAME, 'tr'))
            
            try: 
                # refresh_button = driver.find_element(By.CLASS_NAME, 'SS_DocketUpdateNow')
                refresh_button = WebDriverWait(driver, 10).until(EC.element_to_be_clickable((By.CLASS_NAME, 'SS_DocketUpdateNow')))
                move_mouse_to(driver, refresh_button)
                refresh_button.click()
                log_message(f"Refreshing {docket_number} ...")

                # I need to wait for the refresh_button to desapear
                WebDriverWait(driver, 180).until(EC.invisibility_of_element_located((By.CLASS_NAME, 'SS_DocketUpdateNow')))
                header_div = WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "Header")))
                steps_table = WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.CLASS_NAME, "SS_DataTable")))
                # Do we need to wait for the steps table to be loaded?
                new_steps_count = len(steps_table.find_elements(By.TAG_NAME, 'tr'))
                log_message(f"Refresh button desappeared => Case refreshed with {new_steps_count - current_steps_count} new steps")
                random_delay()
                log_message(f"After waiting 2 seconds => Case refreshed with {new_steps_count - current_steps_count} new steps")
                
                if new_steps_count > current_steps_count:
                    refresh = True

                df.loc[index, 'date_updated'] = datetime.now()
            
            except TimeoutException:
                df.loc[index, 'date_updated'] = refresh_date
                log_message("Refresh button still visible after 120 seconds. Proceeding anyway.")
    
            except:
                df.loc[index, 'date_updated'] = refresh_date
                log_message("Case Open and already refreshed")

        else:
            log_message(f"Case {docket_number} is closed or not needs to be refreshed: Refreshed Date is {refresh_date} and threshold is {refresh_days_threshold}")
            df.loc[index, 'date_updated'] = refresh_date

        df.loc[index, 'date_checked'] = datetime.now()
        
        table = header_div.find_element(By.TAG_NAME, "table")
        td_elements = table.find_elements(By.TAG_NAME, "td")

        # Process the first TD
        full_text_1 = td_elements[0].text
        lines_1 = full_text_1.split('\n')
        for line in lines_1:
            if 'Assigned To:' in line: df.loc[index, 'assigned_to'] = line.replace('Assigned To:', '').strip()
            if 'Nature of Suit:' in line: df.loc[index, 'nature_of_suit'] = line.replace('Nature of Suit:', '').strip()
            if 'Cause:' in line: df.loc[index, 'cause'] = line.replace('Cause:', '').strip()

        # Process the second TD
        full_text_2 = td_elements[1].text
        lines_2 = full_text_2.split('\n')
        for line in lines_2:
            if 'Statute:' in line: df.loc[index, 'statute'] = line.replace('Statute:', '').strip()
            if 'Demand Amount:' in line: df.loc[index, 'demand_amount'] = line.replace('Demand Amount:', '').strip()
            if 'NOS Description:' in line: df.loc[index, 'nos_description'] = line.replace('NOS Description:', '').strip()
            if 'Class Code:' in line: df.loc[index, 'class_code'] = line.replace('Class Code:', '').strip()
            if 'Closed:' in line: df.loc[index, 'closed'] = pd.to_datetime(line.replace('Closed:', '').strip()).date()

    except Exception as e:
        print(f"Error processing Header in {docket_number} from {court}: {e}")
        print(f"Traceback:\n{traceback.format_exc()}")

        
    # Extract plaintiff and defendant lawyers and names
    try:
        plaintiffNames = []
        plaintiffLawyers = []
        defendentNames = []
        defendentLawyers = []
        
        # Locate the litigants table within the div with id="Participants"
        participants_div = driver.find_element(By.ID, "Participants")
        litigants_table = participants_div.find_element(By.XPATH, ".//table[@data-housestyle='Table']")

        for table_row in litigants_table.find_elements(By.TAG_NAME, 'tr'):
            cells = table_row.find_elements(By.TAG_NAME, 'td')
            if len(cells) == 2:
                role = cells[0].text
                if "Plaintiff" in role:
                    plaintiffNames.append(cells[0].text.strip().replace("\nPlaintiff", ""))
                    plaintiffLawyers.append(get_plaintiff_law_firms(cells[1].text.strip()))
                elif "Defendant" in role:
                    defendentNames.append(cells[0].text.strip().replace("\nDefendant", ""))
                    defendentLawyers.append(cells[1].text.strip())

        df.loc[index, 'plaintiff_names'] = json.dumps([name for name in plaintiffNames])
        df.loc[index, 'plaintiff_lawyers'] = json.dumps([lawyer for lawyer in plaintiffLawyers])
        df.loc[index, 'defendant_names'] = json.dumps([name for name in defendentNames])
        df.loc[index, 'defendant_lawyers'] = json.dumps([lawyer for lawyer in defendentLawyers])

        # Get AI summary if there is one
        try:
            summary_div = driver.find_element(By.ID, "AIDocSummary")
            df.loc[index, 'aisummary'] = summary_div.find_elements(By.TAG_NAME, "div")[1].text.strip()
            driver.execute_script("arguments[0].scrollIntoView(true);", summary_div)  # Scroll into view
        except:
            df.loc[index, 'aisummary'] = None
            driver.execute_script("arguments[0].scrollIntoView(true);", participants_div)  # Scroll into view

    except Exception as e:
        print(f"Error processing Participants in {docket_number} from {court}: {e}")
        print(f"Traceback:\n{traceback.format_exc()}")

    return refresh

def get_plaintiff_law_firms(plaintiffLawyer):
    match = re.search(r"TO BE NOTICED(?:\s+|<br>)(.+?)(?:\s+|<br>.+?)\d+", plaintiffLawyer)
    if match:
        return match.group(1).strip()  # Return the captured text after "TO BE NOTICED"
    else:
        return plaintiffLawyer
    
def fix_plaintiff_law_firms():
    from DatabaseManagement.ImportExport import get_table_from_GZ, insert_and_update_df_to_GZ_batch
    case_df = get_table_from_GZ("tb_case")
    for index, row in case_df.iterrows():
        if "TO BE NOTICED" in row['plaintiff_lawyers']:
            list_of_law_firms = json.loads(row['plaintiff_lawyers'])
            for law_firm in list_of_law_firms:
                match = re.search(r"TO BE NOTICED(?:\s+|\n)(.+?)(?:\s+|\n).+?\d+", law_firm)
                if match:
                    list_of_law_firms[list_of_law_firms.index(law_firm)] = match.group(1).strip()
            
            case_df.loc[index, 'plaintiff_lawyers'] = json.dumps(list_of_law_firms)

        elif "<br>" in row['plaintiff_lawyers'] or "\n" in row['plaintiff_lawyers']:
            print("what to do?")

    insert_and_update_df_to_GZ_batch(case_df, "tb_case", "id")
    
if __name__ == "__main__":
    # text = "Sofia Quezada Hastings<br>ATTORNEY TO BE NOTICED<br>Aronberg Goldgehn Davis &amp; Garmisa<br>Aronberg Goldgehn Davis &amp; Garmisa 225 W. Washington St. Suite 2800 60606<br>Chicago, IL  60606<br>USA<br>312-755-3139 Email:<EMAIL>"
    # text2= "John Wilson<br>ATTORNEY TO BE NOTICED<br>Hughes Socol Piers Resnick &amp; Dym, Ltd.<br>70 W Madison St 4000<br>Chicago, IL  60602<br>USA<br>312-604-2668 Fax: Not A Member Email:<EMAIL><br><br>Robert Payton Mcmurray<br>ATTORNEY TO BE NOTICED<br>Hughes Socol Piers Resnick &amp; Dym, Ltd.<br>70 W Madison St. Ste 4000<br>Chicago, IL  60602<br>USA<br>(312) 604-2696 Fax: Not A Member Email:<EMAIL><br><br>William Benjamin Kalbac<br>ATTORNEY TO BE NOTICED<br>Hughes Socol Piers Resnick &amp; Dym, Ltd.<br>70 W. Madison St. Suite#4000<br>Chicago, IL  60602<br>USA<br>(312)580-0100 Fax: Not A Member Email:<EMAIL><br><br>Michael A. Hierl<br>ATTORNEY TO BE NOTICED<br>Hughes Socol PIers Resnick &amp; Dym Ltd<br>Three First National Plaza 70 West Madison Street Suite 4000<br>Chicago, IL  60602<br>USA<br>312 580 0100 Fax: Active Email:<EMAIL>"
    # print(get_plaintiff_law_firms(text))
    fix_plaintiff_law_firms()
