import asyncio, re, json, aiohttp, os, sys, mimetypes
from PIL import Image # Pillow imports (ExifTags not needed)
import piexif # Added piexif import for EXIF manipulation
sys.path.append(os.getcwd())
from bs4 import BeautifulSoup
from markdownify import markdownify
from AI.GC_VertexAI import vertex_genai_multi_async, vertex_genai_image_gen_async
from AI.LLM_shared import get_json
from Check.Do_Check_Download import download_from_url # Keep existing correct import
from Common.Constants import sanitize_name


# --- Website Configuration ---
WEBSITE_CONFIG = [
    {
        "name": "10100",
        "search_url_template": "https://www.10100.com/search/xxxx",
        "exclude_words": [], # Add specific words if needed for this site during URL finding
        "watermark_description": "blue and white diagonal retangle watermark" # To be used later if needed
    },
    {
        "name": "SellerDefense",
        "search_url_template": "https://sellerdefense.cn/?s=xxxx",
        "exclude_words": ["allcase"], # Used in find_target_urls and find_target_urls_using_markdown
        "watermark_description": "yellow diagonal SellerDefense watermark"
    },
    {
        "name": "Maijiazhichi",
        "search_url_template": "https://maijiazhichi.com/?s=xxxx",
        "exclude_words": ["tro"], # Used in find_target_urls and find_target_urls_using_markdown
        "watermark_description": "grey-blue diagonal watermark"
    },
    # Add more site configurations as needed
]
# --------------------------


def format_case_number(case_number: str) -> list[str]:
    """
    Generates different formats for the case number.
    Input: e.g., "1:25-cv-00097"
    Output: ["25-cv-00097", "25-cv-97", "25cv97"]
    """
    match = re.match(r'\d+:(\d+)-([a-zA-Z]+)-(\d+)', case_number)
    if not match:
        # Handle cases where the input format might be different or invalid
        # For now, return empty list or raise error? Let's return empty for robustness.
        print(f"Warning: Could not parse case number format: {case_number}")
        return []

    part1 = match.group(1)
    part2 = match.group(2)
    part3 = match.group(3)
    part3_short = str(int(part3)) # Remove leading zeros

    formats = [
        f"{part1}-{part2}-{part3}",
        f"{part1}-{part2}-{part3_short}",
        f"{part1}{part2}{part3_short}"
    ]
    return formats

async def fetch_url(session: aiohttp.ClientSession, url: str) -> str | None:
    """Fetches content from a given URL."""
    try:
        async with session.get(url, timeout=40) as response:
            response.raise_for_status() # Raise an exception for bad status codes
            return await response.text()
    except aiohttp.ClientError as e:
        print(f"Error fetching {url}: {e}")
        return None
    except asyncio.TimeoutError:
        print(f"Timeout fetching {url}")
        return None

async def find_target_urls(session: aiohttp.ClientSession, search_url_template: str, case_formats: list[str]) -> list[str]:
    """Searches case formats on a website and extracts target article URLs."""
    target_urls = set() # Use a set to avoid duplicates

    for case_format in case_formats:
        search_url = search_url_template.replace("xxxx", case_format)
        print(f"Searching: {search_url}")
        content = await fetch_url(session, search_url)
        if not content:
            continue

        soup = BeautifulSoup(content, 'html.parser')

        # Apply website-specific URL patterns using more direct selectors
        if "10100.com" in search_url_template:
            # Find links directly matching the pattern /article/\d+
            links = soup.find_all('a', href=re.compile(r'^/article/\d+'))
            for link in links:
                # Construct absolute URL if needed (assuming relative links)
                base_url = "https://www.10100.com" # Define base URL
                absolute_url = base_url + link['href'] if link['href'].startswith('/') else link['href']
                target_urls.add(absolute_url)
        elif "maijiazhichi.com" in search_url_template:
            # Find h2 tags with class 'item-title', then get the 'a' tag inside
            h2_tags = soup.find_all('h2', class_='item-title')
            for h2 in h2_tags:
                link = h2.find('a', href=True)
                if link:
                    href = link['href']
                    # Exclude URLs containing 'allcase'
                    if "tro" not in href:
                        base_url = "https://maijiazhichi.com"
                        href = base_url + href if href.startswith('/') else href
                        target_urls.add(href) # Assuming these are absolute URLs already
        elif "sellerdefense.cn" in search_url_template:
            # Find h3 tags with class 'entry-title', then get the 'a' tag inside
            h3_tags = soup.find_all('h3', class_='entry-title')
            for h3 in h3_tags:
                link = h3.find('a', href=True)
                if link:
                    href = link['href']
                    # Exclude URLs containing 'allcase'
                    if "allcase" not in href:
                        base_url = "https://sellerdefense.cn"
                        href = base_url + href if href.startswith('/') else href
                        target_urls.add(href) # Assuming these are absolute URLs already

    print(target_urls)
    return list(target_urls)


async def find_target_urls_using_markdown(session: aiohttp.ClientSession, search_url_template: str, case_formats: list[str], exclude_words: list[str]) -> list[str]:
    """Searches case formats on a website, converts to Markdown, and extracts target article URLs via regex."""
    target_urls = set() # Use a set to avoid duplicates

    # Determine base URL for resolving relative links found in markdown
    base_domain_match = re.match(r'(https?://[^/]+)', search_url_template)
    base_url = base_domain_match.group(1) if base_domain_match else None

    for case_format in case_formats:
        search_url = search_url_template.replace("xxxx", case_format)
        print(f"Searching (Markdown method): {search_url}")
        content = await fetch_url(session, search_url)
        if not content:
            continue

        # Convert HTML to Markdown
        try:
            # Use body_width=0 to prevent line wrapping that might break URLs
            markdown_content = markdownify(content, heading_style="ATX", bullets='*', body_width=0)
        except Exception as e:
            print(f"Error converting HTML to Markdown for {search_url}: {e}")
            continue

        prompt = f'{markdown_content} \n\n This page shows seach results. What are the results (I need the name and URL)? Return your answer as {{"name_result_1": "url_1", "name_result_2": "url_2", etc... }}'
        response = await vertex_genai_multi_async([("text", prompt)], model_name="gemini-2.5-pro-exp-03-25")
        json_response = get_json(response)
        for name, url in json_response.items():
            # Check if the URL contains any of the exclude words
            if any(word in url for word in exclude_words):
                continue
            else:
                url = base_url + url if url.startswith('/') else url
                target_urls.add(url)

    print(target_urls)
    return list(target_urls)


async def extract_data_from_url(session: aiohttp.ClientSession, url: str, site_name: str, date_filed: str, docket: str) -> dict | None:
    """Fetches a target URL, converts to Markdown, sends to LLM, and returns extracted data along with source info."""
    print(f"Fetching data from: {url} (Site: {site_name})")
    content = await fetch_url(session, url)
    if not content:
        return None

    soup = BeautifulSoup(content, 'html.parser')

    # Attempt to find a main content area - this often improves Markdown quality
    # These selectors are guesses and might need adjustment per site
    main_content = soup.find('article') or soup.find('main') or soup.find('div', class_=re.compile(r'(content|main|post|article)'))
    if not main_content:
        main_content = soup.body # Fallback to the whole body

    # Convert the relevant HTML part to Markdown
    markdown_content = markdownify(str(main_content), heading_style="ATX", bullets='*')

    # Prepare the prompt for the LLM
    prompt = f"""
    Analyze the following Markdown content extracted from the webpage {url}.
    Identify and extract all intellectual property registration numbers mentioned.
    Specifically look for:
    1.  Trademark Registration Numbers (usually start with digits or contain specific keywords like "Trademark Reg. No.")
    2.  Patent Registration Numbers (often start with "US" followed by digits, or contain keywords like "Patent No.")
    3.  Copyright Registration Numbers (often start with "VA", "TX", "SR", or contain keywords like "Copyright Reg. No.")

    Also, extract the URLs of any images that appear to be related to Copyright registrations. Try to associate each image URL with a specific Copyright Registration Number if possible. If an image is present but no clear registration number is nearby, list the image URL with a placeholder key like "no_reg_X". If a registration number is found with no associated image, list it with a null value for the image URL.
    Additionally, check the page (especially near copyright information) for a single URL pointing to the artist's main website or portfolio (artist_url). If found, include it as a top-level key in the JSON.

    Return the extracted information ONLY as a JSON object with the following structure:
    {{
      "trademarks": ["list of strings"],
      "patents": ["list of strings"],
      "copyrights": {{
        "reg_no_1": "image_url_1",
        "reg_no_2": null,
        "no_reg_1": "image_url_2"
      }},
      "artist_url": "url_string_or_null"
    }}
    where
    - "copyrights": Maps registration numbers (or "no_reg_X" placeholders) to associated image URLs (or null).
    - "artist_url": Contains the single artist website URL found on the page, or null if none was found.

    Do not include any introductory text, explanations, or markdown formatting in your response. Just the JSON object.

    Markdown Content:
    ---
    {markdown_content}
    ---
    """

    response = await vertex_genai_multi_async([("text", prompt)], model_name="gemini-2.5-pro-exp-03-25")
    
    try:
        json_response = get_json(response)
        # Add source information to the result
        if isinstance(json_response, dict):
            json_response["source_page_url"] = url
            json_response["source_site"] = site_name
        return json_response
    except json.JSONDecodeError:
        print(f"Error decoding LLM response for {url}: {response}")
        # Return source info even on LLM error? Maybe not useful.
        return None


async def scrape_case_data(date_filed: str, docket: str) -> dict:
    """
    Main function to scrape data for a given case number from multiple websites.
    """
    case_formats = format_case_number(docket)
    if not case_formats:
        return {"error": f"Invalid case number format: {docket}"}

    all_trademarks = set()
    all_patents = set()
    # Store { reg: { "img_url": url, "source_page_url": page_url, "source_site": site_name, "local_path": path } }
    # Initialize local_path later after download/genai
    all_copyrights = {}
    all_artist_urls = set() # To store unique artist URLs found across pages

    async with aiohttp.ClientSession() as session:
        # Create tasks to extract data from each unique target URL found
        target_url_tasks_with_site = []
        for site_config in WEBSITE_CONFIG:
            target_url_tasks_with_site.append(find_target_urls_using_markdown(session, site_config["search_url_template"], case_formats, site_config["exclude_words"])) # Pass exclude_words if needed
        url_results_per_site = await asyncio.gather(*target_url_tasks_with_site)

        # Create data extraction tasks, passing the site name
        extraction_tasks = []
        processed_urls = set() # Keep track of URLs already scheduled for extraction
        for i, site_config in enumerate(WEBSITE_CONFIG):
            site_name = site_config["name"]
            urls_from_this_site = url_results_per_site[i]
            for url in urls_from_this_site:
                if url not in processed_urls:
                    extraction_tasks.append(extract_data_from_url(session, url, site_name, date_filed, docket))
                    processed_urls.add(url)

        print(f"Scheduled {len(extraction_tasks)} unique URLs for data extraction.")

        # Run all data extraction tasks concurrently
        results = await asyncio.gather(*extraction_tasks)

        # Aggregate results
        copyright_no_reg_counter = 1
        for data in results: # data is the result from extract_data_from_url
            if data and isinstance(data, dict):
                source_page_url = data.get("source_page_url", "Unknown") # Get source page URL
                source_site = data.get("source_site", "Unknown") # Get source site name

                if data.get("trademarks"):
                    all_trademarks.update(data["trademarks"])
                if data.get("patents"):
                    all_patents.update(data["patents"])
                # Aggregate Copyrights { reg: { "img_url": ..., "source_page_url": ..., "source_site": ... } }
                if data.get("copyrights") and isinstance(data["copyrights"], dict):
                    for reg, img_url in data["copyrights"].items():
                        # Ensure img_url is a string or None
                        if not (isinstance(img_url, str) or img_url is None):
                            print(f"Warning: Skipping invalid copyright image URL format for reg '{reg}': {img_url}")
                            continue

                        # Standardize key for placeholders
                        if reg.startswith("no_reg_"):
                            current_key = f"no_reg_{copyright_no_reg_counter}"
                            copyright_no_reg_counter += 1
                        else:
                            current_key = reg

                        # Store details including source info
                        if current_key not in all_copyrights:
                             all_copyrights[current_key] = {
                                 "img_url": img_url,
                                 "source_page_url": source_page_url,
                                 "source_site": source_site,
                                 "local_path": None # Will be updated after download/genai
                             }
                        elif img_url and not all_copyrights[current_key].get("img_url"):
                            # Update if we found an image URL where previously there was none
                            all_copyrights[current_key]["img_url"] = img_url
                            # Optionally update source info if considered more reliable? For now, keep original.
                        # If key exists and has an image_url, don't overwrite unless specific logic is needed

                # Aggregate Artist URL
                artist_url = data.get("artist_url")
                if artist_url and isinstance(artist_url, str):
                    all_artist_urls.add(artist_url)


    # Process all copyright pictures: download, remove watermark, add metadata
    case_folder = sanitize_name(f"{date_filed.strftime('%Y-%m-%d')} - {docket}")
    process_copyrights_pictures_tasks = []
    # Create a quick lookup for watermark descriptions by site name
    watermark_desc_map = {site["name"]: site["watermark_description"] for site in WEBSITE_CONFIG}

    for reg, details in all_copyrights.items():
        if details.get("img_url"): # Only process if there's an image URL
            source_site = details.get("source_site", "Unknown")
            watermark_description = watermark_desc_map.get(source_site, "the watermark") # Get description or default
            process_copyrights_pictures_tasks.append(
                copyright_download_and_process(
                    reg_no=reg,
                    img_url=details["img_url"],
                    case_folder=case_folder,
                    source_page_url=details["source_page_url"],
                    source_site=source_site,
                    watermark_description=watermark_description # Pass the description
                )
            )

    processed_results = await asyncio.gather(*process_copyrights_pictures_tasks)

    # Update all_copyrights with the final local path from processed results
    for result in processed_results:
        if result and result.get("reg") in all_copyrights:
            all_copyrights[result["reg"]]["local_path"] = result["image_path"] # Store final path
        elif result:
             print(f"Warning: Processed result for unknown reg '{result.get('reg')}'")

    # Prepare final output, only include local_path if it exists
    final_copyrights = {
        reg: details["local_path"]
        for reg, details in all_copyrights.items()
        if details.get("local_path")
    }
        
    return {
        "trademarks": sorted(list(all_trademarks)),
        "patents": sorted(list(all_patents)),
        "copyrights": final_copyrights, # Use the final dict with local paths
        "artist_urls": sorted(list(all_artist_urls)) # Added artist URLs list
    }


# Helper function to find EXIF tag ID is no longer needed with piexif directly
# Instead, we use the numerical tag ID directly, e.g., piexif.ExifIFD.UserComment (which is 37510)
# -----------------------------------------


async def copyright_download_and_process(reg_no, img_url, case_folder, source_page_url, source_site, watermark_description):
    """Downloads original, adds metadata to it, removes watermark using GenAI, saves clean version."""
    # Dynamically create the watermark removal prompt
    watermark_removal_prompt = f"Your job is to generate a picture exactly the same but remove the {watermark_description}"
    print(f"Using watermark removal prompt for {reg_no}: '{watermark_removal_prompt}'")

    # Define directories
    watermarked_dir = os.path.join(os.getcwd(), "Documents", "IP", "Copyrights", case_folder, "copyright_watermarked")
    final_dir = os.path.join(os.getcwd(), "Documents", "IP", "Copyrights", case_folder) # Directory for watermark-removed files
    os.makedirs(watermarked_dir, exist_ok=True) # Ensure watermarked directory exists
    os.makedirs(final_dir, exist_ok=True) # Ensure final directory exists

    # Define paths
    # os.makedirs(temp_download_dir, exist_ok=True) # download_from_url might do this
    try:
        # Guess extension or use a default if split fails
        url_parts = img_url.split(".")
        file_extension = url_parts[-1] if len(url_parts) > 1 else "jpg" # Default extension
    except Exception:
        file_extension = "jpg" # Fallback extension

    original_local_path = os.path.join(watermarked_dir, f"{sanitize_name(reg_no)}_original.{file_extension}") # Path for original with metadata

    try:
        # 1. Download Original
        await download_from_url(img_url, original_local_path)
        print(f"Downloaded original image for {reg_no} to {original_local_path}")

        # -- Check image format and convert if necessary before adding metadata --
        try:
            with Image.open(original_local_path) as img:
                img_format = img.format.upper() # Get format (JPEG, PNG, etc.)
                if img_format not in ['JPEG', 'TIFF']:
                    print(f"Original format {img_format} not supported by piexif. Converting {reg_no} to JPEG.")
                    # Ensure image is in RGB mode for JPEG saving
                    if img.mode != 'RGB':
                        img = img.convert('RGB')
                    
                    # Create new path for JPEG version
                    base, _ = os.path.splitext(original_local_path)
                    new_jpeg_path = f"{base}.jpg"
                    
                    # Save as JPEG
                    img.save(new_jpeg_path, "JPEG")
                    print(f"Saved converted JPEG to: {new_jpeg_path}")
                    
                    # Remove original non-JPEG/TIFF file
                    try:
                        os.remove(original_local_path)
                        print(f"Removed original file: {original_local_path}")
                    except OSError as rm_err:
                        print(f"Warning: Could not remove original file {original_local_path} after conversion: {rm_err}")
                        
                    # Update path to point to the new JPEG file
                    original_local_path = new_jpeg_path
                    
        except FileNotFoundError:
             print(f"Error: Downloaded file not found at {original_local_path} before format check.")
             raise # Re-raise the error to be caught by the outer try-except
        except Exception as img_err:
            print(f"Error during image format check/conversion for {reg_no}: {img_err}")
            # Decide how to handle: skip metadata? raise error? For now, skip metadata attempt.
            raise # Re-raise to be caught by outer try-except

        # 2. Add Metadata to Original File (now guaranteed to be JPEG/TIFF or skipped)
        metadata = {
            "source_site": source_site,
            "source_page_url": source_page_url,
            "original_image_url": img_url,
            "processing_step": "original_download" # Indicate this is the original
        }
        metadata_json = json.dumps(metadata)
        try:
            # Load original image's EXIF, add comment, insert back into file
            exif_dict = piexif.load(original_local_path)
            comment_bytes = metadata_json.encode('utf-8')
            exif_dict["Exif"][piexif.ExifIFD.UserComment] = comment_bytes
            exif_bytes = piexif.dump(exif_dict)
            piexif.insert(exif_bytes, original_local_path) # Insert EXIF into the original file
            print(f"Added metadata to original file: {original_local_path}")
        except Exception as meta_err:
            # If EXIF loading/insertion fails (e.g., non-JPEG), we skip adding metadata but continue
            print(f"Warning: Could not add metadata to original file {original_local_path}: {meta_err}")

        # 3. Watermark Removal using GenAI (using the original file path)
        final_image_path = None # Path of the watermark-removed image
        inline_data = await vertex_genai_image_gen_async(
            [("text", watermark_removal_prompt), ("image_path", original_local_path)] # Pass the original path
        )

        if inline_data and not isinstance(inline_data, str): # Check if we got image data
            processed_image_bytes = inline_data.data # Assign bytes here
            mime_type = inline_data.mime_type
            genai_extension = mimetypes.guess_extension(mime_type) or f".{mime_type.split('/')[-1]}" # Guess extension

            # Define final path for the watermark-removed image
            final_image_path_base = os.path.join(final_dir, f"{sanitize_name(reg_no)}_genai") # Save in the main case folder
            final_image_path = f"{final_image_path_base}{genai_extension}"

            # 4. Save GenAI Result (WITHOUT metadata)
            try:
                with open(final_image_path, "wb") as f:
                    f.write(processed_image_bytes)
                print(f"Saved watermark-removed image for {reg_no} to {final_image_path}")
            except Exception as save_err:
                 print(f"Error saving watermark-removed image for {reg_no}: {save_err}")
                 final_image_path = None # Indicate save failure

        else:
            print(f"GenAI did not return image data for {reg_no}. Skipping watermark removal save.")
            final_image_path = None # No watermark-removed image generated

    except Exception as e:
        print(f"Error during download/metadata/genai process for {reg_no}: {e}")
        # Clean up potentially incomplete original download if it exists and an error occurred
        if 'original_local_path' in locals() and os.path.exists(original_local_path) and final_image_path is None: # Only delete if processing failed
             try:
                 os.remove(original_local_path)
                 print(f"Removed incomplete original file: {original_local_path}")
             except OSError as rm_err:
                 print(f"Error removing incomplete original file {original_local_path}: {rm_err}")

        final_image_path = None # Ensure final path is None if any step fails
    # 5. Return Path to the watermark-removed file (final_image_path)
    # The original file with metadata remains in the 'copyright_watermarked' subfolder

    # No finally block needed here as we want to keep the original file if metadata was added

    return {"reg": reg_no, "image_path": final_image_path} # Return the final path (or None)
# --- End of copyright_download_and_process function ---





# Example Usage (can be run with `python -m Scraper.scraper`)
if __name__ == "__main__":
    async def main():
        import pandas as pd
        date = pd.to_datetime("2024-05-03")
        test_case = "1:25-cv-00097"
        print(f"Scraping data for case: {test_case}")
        data = await scrape_case_data(date, test_case)
        print("\n--- Final Result ---")
        print(json.dumps(data, indent=2))

    asyncio.run(main())
