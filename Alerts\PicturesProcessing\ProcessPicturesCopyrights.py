import os
import sys
sys.path.append(os.getcwd())
import cv2
import numpy as np
import os
from PIL import Image
import shutil
import re
from logdata import log_message
from Alerts.PicturesProcessing.ProcessPicturesShared import crop_white_space,  keywords, assess_similarity_to_template_single_folder, convert_page_number_to_image
from Alerts.PicturesProcessing.ExtractPictures import  extract_images_from_a_pdf_page
from AI.GC_VertexAI import vertex_genai_multi
from AI.GCV_GetImageParts import get_image_parts
from AI.LLM_shared import get_json
import fitz
from langfuse.decorators import observe
from IP.Copyright_UCO import get_info_using_reg_no
import asyncio
from IP.Copyright_Get_Image_Google import get_copyright_images_from_google_api
from Common.Constants import sanitize_name
from langfuse.decorators import observe, langfuse_context
import pandas as pd

# VA == Visual Art
# TX == Computer File
# PA == Performance Art (Motion Picture)


def process_copyright_exhibit(df, index, ref_hash, size, case_images_directory, case_directory_1, pdf_document, pdf_file, pages_text_json, page_ocr_data, page_images):
    log_message(f"        - Extracting images form the pdf.")
    # extract_images_from_a_pdf(case_directory_1, pdf_file)
    pdf_file_no_ext = os.path.splitext(pdf_file)[0]
    similar_images_in_pdf = []
    
    # 1. Image template approach
    folder_path = os.path.join(case_directory_1, pdf_file_no_ext)
    log_message(f"        - Assessing similarity of images to copyright template")
    if os.path.isdir(folder_path):
        similar_images_in_pdf, differences, total_similar_images_folder = assess_similarity_to_template_single_folder(folder_path, ref_hash, size, 20)

    # 2. Keywords approach
    page_numbers_with_keywords = []
    if similar_images_in_pdf: # if it is the right document, we be easy on keywords
        for page_number in range(pdf_document.page_count):
            if any(sum(keyword.lower() in pages_text_json[page_number+1].lower() for keyword in keyword_set) >= 3 for keyword_set in keywords["Copyright"]):
                page_numbers_with_keywords.append(page_number + 1)
    else:
        for page_number in range(pdf_document.page_count): # This will also pick up the catalog format (often used for photographs)
            if any(all(keyword.lower() in pages_text_json[page_number+1].lower() for keyword in keyword_set) for keyword_set in keywords["Copyright"]):
                # we found one, now we can look for any word rather than all words
                for page_number in range(pdf_document.page_count):
                    if any(sum(keyword.lower() in pages_text_json[page_number+1].lower() for keyword in keyword_set) >= 3 for keyword_set in keywords["Copyright"]):
                        page_numbers_with_keywords.append(page_number + 1)
                break
    

    if os.path.isdir(folder_path):
        # 4. Merge both lists
        for page_number in page_numbers_with_keywords:
            image_on_page = [file for file in os.listdir(os.path.join(case_directory_1, pdf_file_no_ext)) if file.startswith(f"{pdf_file_no_ext}_page{page_number}_0")]
            if os.path.join(case_directory_1, pdf_file_no_ext, image_on_page[0]) not in similar_images_in_pdf:
                similar_images_in_pdf.append(os.path.join(case_directory_1, pdf_file_no_ext, image_on_page[0]))

        # 4. Merge both lists the other way arround to see if we really need the assess_similarity_to_template_single_folder
        # 1 time and similar_images_in_pdf was wrong
        for file in similar_images_in_pdf:
            page_number = int(os.path.basename(file).split("page")[1].split("_")[0])
            if page_number not in page_numbers_with_keywords:
                page_numbers_with_keywords.append(page_number)
                print(f"\033[91mPage {page_number} was found by the template but not by the keywords. Full text is: {pages_text_json[page_number]} \033[0m")
    

    # 5. Process the list
    if similar_images_in_pdf:
        log_message(f"        - Extracting copyright information from IP pages: {len(similar_images_in_pdf)} IP pages")
        page_numbers = [int(file.split("page")[1].split("_")[0]) for file in similar_images_in_pdf]
        sorted_page_numbers = sorted(list(set(page_numbers)))
        image_pages = 0

        copyright_images_folder = os.path.join(folder_path, 'copyright')
        shutil.rmtree(copyright_images_folder) if os.path.exists(copyright_images_folder) else None
        os.makedirs(copyright_images_folder, exist_ok=True)
        
        for source_certificate_path in similar_images_in_pdf:
            dest_certificate_filename = os.path.splitext(os.path.basename(source_certificate_path))[0] + "_full.webp"
            dest_certificate_path = os.path.join(case_images_directory, dest_certificate_filename)

            # get the image page
            copyright_page_number = int(dest_certificate_filename.split("page")[1].split("_")[0])
            registration_number = get_copyright_registration_number(pages_text_json[copyright_page_number], source_certificate_path)
            if registration_number is None:
                continue
            
            next_copyright_page_number = sorted_page_numbers[sorted_page_numbers.index(copyright_page_number) + 1] if sorted_page_numbers.index(copyright_page_number) + 1 < len(sorted_page_numbers) else (pdf_document.page_count+1)
            if next_copyright_page_number - copyright_page_number == 1:
                print(f"The next copyright page is consecutive => it is not a image page")
                save_copyright_without_image(df, index, source_certificate_path, case_images_directory, dest_certificate_path, registration_number)   
                continue               
        
            # files_with_next_page = [f for f in os.listdir(folder_path) if int(f.split("page")[1].split("_")[0]) in range(copyright_page_number + 1, next_copyright_page_number)]
            # # if they are multuple ones we must find the one with the most similar image to the current one
            # if len(files_with_next_page) == 1:
            #     save_copyright_with_image(df, index, source_certificate_path, folder_path, case_images_directory, files_with_next_page[0], dest_certificate_path)                
            # elif len(files_with_next_page) > 1:
            #     best_image = get_copyright_image_best_scores(folder_path, files_with_next_page)
            #     save_copyright_with_image(df, index, file_path, folder_path, case_images_directory, best_image, dest_certificate_path)
            # else: 
            #     print(f"No next page found for {os.path.basename(file_path)}")

            for page_number in range(copyright_page_number + 1, next_copyright_page_number):
                extract_images_from_a_pdf_page(pdf_file, pdf_document, page_number - 1, copyright_images_folder, 5) # only image that take more than 20% of the page
            pages_with_images = [page_number for page_number in range(copyright_page_number + 1, next_copyright_page_number) if any(f"{pdf_file_no_ext}_page{page_number}" in filename for filename in os.listdir(copyright_images_folder))]
            # if "public catalog" in pages_text_json[copyright_page_number].lower() and "photograph" in pages_text_json[copyright_page_number].lower():   # if not the format from public catalog for the photograph
            pages_with_images = pages_with_images # take all pages
            # else:                
                # pages_with_images = min(pages_with_images, key=lambda x: len(pages_text_json[x]))   # only take the best page: the one with the least text

            for image_page in pages_with_images:
                if not any(keyword.lower() in pages_text_json[image_page].lower() for keyword in ["author", "copyright claimant", "certification", "correspondence", "name", "date", "registration", "service", "request"]):   # len(page_text[best_page]) < 200:
                    files_on_best_page = [f for f in os.listdir(copyright_images_folder) if int(f.split("page")[1].split("_")[0]) == image_page]
                    if len(files_on_best_page) == 1 and not any(keyword.lower() in pages_text_json[image_page].lower() for keyword in ["library of congress", "certificate of registration", "certificate issued", "under the seal", "title 17", "author", "copyright claimant"]): # the certificate could be on 2 pages
                        save_copyright_with_image(df, index, source_certificate_path, copyright_images_folder, case_images_directory, files_on_best_page[0], dest_certificate_path, registration_number)
                        image_pages += 1
                    elif len(files_on_best_page) > 1:
                        best_image = get_copyright_image_best_scores(copyright_images_folder, files_on_best_page)
                        save_copyright_with_image(df, index, source_certificate_path, copyright_images_folder, case_images_directory, best_image, dest_certificate_path, registration_number)
                        image_pages += 1
                else:
                    save_copyright_without_image(df, index, source_certificate_path, case_images_directory, dest_certificate_path, registration_number)

        if image_pages < 0.5 * len(similar_images_in_pdf): # less than 50% of the certificates have images        # not_image_pages / len(similar_images_in_pdf) > 0.5
            image_pages = 0
            print(f"More than half of the pages are not image pages => there are no images in this document")
            # Use the copyright page as the image
            for item in df.at[index, 'images']['copyrights']:
                shutil.copy(os.path.join(case_images_directory, df.at[index, 'images']['copyrights'][item]["full_filename"][0]), os.path.join(case_images_directory, item))
                
        # Update images_status field with exhibit metadata if images were found
        if image_pages > 0:
            df.at[index, 'images_status']['copyright_status']['exhibit']['count'] += image_pages
            df.at[index, 'images_status']['copyright_status']['exhibit']['steps'].append(os.path.dirname(pdf_file).split("\\")[-1])

        return True, (image_pages>0)
    
    return False, False

def save_copyright_with_image(df, index, source_certificate_path, copyright_images_folder, case_images_directory, copyright_image_filename, dest_certificate_path, registration_number):
    # 1. The Certificate: Check if width is more than length, and if so rotate by 90 degrees
    img = cv2.imread(source_certificate_path)
    if img.shape[1] > img.shape[0]:  # if width > height
        img = cv2.rotate(img, cv2.ROTATE_90_CLOCKWISE)
    cv2.imwrite(dest_certificate_path, img, [cv2.IMWRITE_WEBP_QUALITY, 80])

    # 2. The image: Remove all the white space around the image
    img = cv2.imread(os.path.join(copyright_images_folder, copyright_image_filename))
    cropped_image, (x_offset, y_offset) = crop_white_space(img)
    copyright_image_filename_webp = f"{os.path.splitext(copyright_image_filename)[0]}.webp"
    params = [cv2.IMWRITE_WEBP_QUALITY, 80]
    cv2.imwrite(os.path.join(case_images_directory, copyright_image_filename_webp), cropped_image, params)
    # print(f"Copyright image {os.path.basename(file_path)} and next page {next_page_filename}")
    # Ensure reg_no is always a list
    df.at[index, 'images']['copyrights'][copyright_image_filename_webp] = {'full_filename': [os.path.basename(dest_certificate_path)], 'reg_no': [registration_number]}


def save_copyright_without_image(df, index, source_certificate_path, case_images_directory, dest_certificate_path, registration_number):
    # 1. The Certificate: Check if width is more than length, and if so rotate by 90 degrees
    img = cv2.imread(source_certificate_path)
    if img.shape[1] > img.shape[0]:
        img = cv2.rotate(img, cv2.ROTATE_90_CLOCKWISE)
    cv2.imwrite(dest_certificate_path, img, [cv2.IMWRITE_WEBP_QUALITY, 80])
    
    copyright_image_filename_webp = os.path.basename(dest_certificate_path).replace("_full.webp", ".webp")
    shutil.copy(dest_certificate_path, os.path.join(case_images_directory, copyright_image_filename_webp))
    # img = cv2.imread(full_filepath)
    # cv2.imwrite(os.path.join(case_images_directory, copyrighted_image_filename), img, [cv2.IMWRITE_WEBP_QUALITY, 80])

    # print(f"Copyright image {os.path.basename(source_certificate_path)} without image")
    # Ensure reg_no is always a list
    df.at[index, 'images']['copyrights'][copyright_image_filename_webp] = {'full_filename': [os.path.basename(dest_certificate_path)], 'reg_no': [registration_number]}

def get_copyright_registration_number(page_text, source_certificate_path):
    # Get the pattern in page_text: VA d-ddd-ddd
    pattern = r"V\s*A\s*\d[\s\-\.\,]\s*\d{3}\s*[\s\-\.\,]\s*\d{3}"  # VA 2-458-745   VA 2.458.745  VA 2458 745
    match = re.search(pattern, page_text)
    if not match:
        pattern = r"\d[\s\-\.]\s*\d{3}\s*[\s\-\.]\s*\d{3}"   # Vv A 2-458-745
        match = re.search(pattern, page_text)
    if not match:
        pattern = r"\d[\s\-\.\,]*\s*\d\s*\d\s*\d\s*[\s\-\.\,]*\s*\d\s*\d\s*\d"   # V A 23 3 5-303
        match = re.search(pattern, page_text)
    if not match:
        pattern = r"\d[\s\-\.\,]*\s*\d\s*\d\s*\d\s*[\s\-\.\,]*\s*\d\s*\d\s*\d"   # V A 23 3 5-303
        match = re.search(pattern, page_text)


    if match:
        # Standardize the format to VA d-ddd-ddd
        matched_string = match.group(0)
        matched_string = matched_string.replace(" ", "").replace(".", "-").replace(",", "-").replace("--", "-")
        if "VA" not in matched_string:
            matched_string = "VA" + matched_string
        matched_string = matched_string.replace("VA", "VA ")
        if matched_string[4] != "-": 
            matched_string = matched_string[:4] + "-" + matched_string[4:]
        if matched_string[8] != "-":
            matched_string = matched_string[:8] + "-" + matched_string[8:]
        print(f"OCR Registration number: {matched_string}")
        return matched_string
    else:
        # create the number: MD-d-ddd-ddd where every number is randomly generated
        print(f"\033[91mCopyright registration text not found: asking LLM \033[0m")
        prompt = 'Is this a copyright registration certificate? If so, what is the registration number? The registration number always starts with VA and 3 groups of digits separated by dashes. You answer following the format: {"is_copyright_registration": "yes", "registration_number": "VA d-ddd-ddd"} or {"is_copyright_registration": "no"}'

        ai_answer = vertex_genai_multi([("text", prompt), ("image_path", source_certificate_path)])
        ai_answer = get_json(ai_answer)
        if ai_answer and ai_answer["is_copyright_registration"] == "yes":
            print(f"LLM Registration number: {ai_answer['registration_number']}")
            return ai_answer["registration_number"]
        else:
            print(f"\033[91mCopyright registration text not found by asking LLM. This is not a registration certificate. \033[0m")
            return None


def get_copyright_image_best_scores(folder_path, files_with_next_page):
    scores = {}
    all_filepaths = [os.path.join(folder_path, f) for f in files_with_next_page]
    
    # Calculate aspect ratios
    aspect_ratios = {}
    for filepath in all_filepaths:
        with Image.open(filepath) as img:
            width, height = img.size
            aspect_ratios[filepath] = width / height
    
    # Calculate file sizes
    file_sizes = {filepath: os.path.getsize(filepath) / (1024 * 1024) for filepath in all_filepaths}
    
    # Calculate colorfulness
    saturations = {}
    for filepath in all_filepaths:
        with Image.open(filepath) as img:
            if img.mode == 'RGB':
                img_array = np.array(img)
                saturations[filepath] = np.sum(np.std(img_array, axis=2))
            else:
                saturations[filepath] = 0
    
    for filepath in all_filepaths:
        points = 0
        points += min(max(0, 1 - max(0, aspect_ratios[filepath] - 1) / 10), aspect_ratios[filepath]) # Aspect ratio point close to 1: 1. For x = 1, the output is 1, For x = 0, the output is 0, For x = 10, the output is 0
        points += file_sizes[filepath] / max(file_sizes.values()) # File size point
        points += saturations[filepath] / max(1, max(saturations.values())) # Colorfulness point
        scores[os.path.basename(filepath)] = points

    best_image = max(scores, key=scores.get)
    
    return best_image


@observe(capture_input=False, capture_output=False)
def process_copyright_regno(df, index, case_images_directory, pdfs_to_consider, pdf_ocr_data, reg_nos, reg_numbers_pictures_not_found):
    # reg_numbers_pictures_not_found is to add data to it! not to process it!
    
    reg_numbers_pictures_not_found_list = []
    processed_count = 0  # Track successfully processed copyright images
    
    prompt = """
    You are an expert in copyright registration numbers.
    You are given a list of copyright registration numbers and documents related to court filings. For each number, you need to find the corresponding copyrighted picture in the documents and return the page number and the pdf file name.
    You return your answer in a JSON format with the following keys: {
    """
                                                                      
    for reg_no in reg_nos:
        prompt += f'"{reg_no}": [page_number, pdf_file_name], '
    prompt += "}. Do not include REDACTED registration numbers in your answer. If a registration number has no associated copyrighted picture (we are looking for a picture of the artwork!!!), leave its list empty []! Do not list there page of the registration number if the picture of the artwork is not present."

    prompt_list = [("text", prompt)]
    for i, pdf_file_path in enumerate(pdfs_to_consider):
        prompt_list.append(("text", f"\n\nThis is pdf file 'File_{i+1}': "))
        prompt_list.append(("pdf_path", pdf_file_path))

    ai_answer = vertex_genai_multi(prompt_list, model_name="gemini-2.0-flash-thinking-exp-01-21")
    ai_answer_json = get_json(ai_answer)

    for reg_no in reg_nos:
        if reg_no not in ai_answer_json:
            reg_numbers_pictures_not_found_list.append(reg_no)

    for reg_no, item in ai_answer_json.items():
        print(f"{reg_no}: {item}")

        try:
            pdf_file_path = pdfs_to_consider[int(item[1].split("File_")[1])-1]
            page_number = int(item[0])
            page_ocr_data, pages_text_json, page_images = pdf_ocr_data[pdf_file_path]
            image_page = page_images[page_number]
        except:
            reg_numbers_pictures_not_found_list.append(reg_no)
            continue
        
        prompt = f"Indentify a single (only 1!) bounding box for the Copyrighted Picture with registration number {reg_no}."
        # Pass image_format='webp' to ensure correct saving format
        parts_details = get_image_parts(prompt, image_page, image_format='webp') 
        if len(parts_details) == 0:
            print(f"\033[91m 🔥 No bounding box found for {reg_no} on page {page_number} in {pdf_file_path}\033[0m")
            reg_numbers_pictures_not_found_list.append(reg_no)
            continue
        elif len(parts_details) > 1:
            print(f"\033[91m 🔥 More than 1 bounding box found for {reg_no} on page {page_number} in {pdf_file_path}\033[0m")
            continue
        else:
            copyright_image_path = parts_details[0]["path"]
            copyright_image_filename = os.path.basename(copyright_image_path)
            shutil.copy(copyright_image_path, os.path.join(case_images_directory, copyright_image_filename))
            copyright_image_full_filename = os.path.splitext(copyright_image_filename)[0] + "_full.webp"
            shutil.copy(copyright_image_path, os.path.join(case_images_directory, copyright_image_full_filename))
            # !!! We dont have the certificate image we will use the image as the certificate
            df.at[index, 'images']['copyrights'][copyright_image_filename] = {'reg_no': [reg_no],'full_filename': [copyright_image_full_filename]}
            processed_count += 1
    

    # Update byregno information
    df.at[index, 'images_status']['copyright_status']['byregno']['count'] += processed_count
    
    # Process registration numbers that weren't found in the exhibits using google search
    if len(reg_numbers_pictures_not_found_list) > 0:
        prompt = """
        You are an expert in copyright registration numbers.
        You are given a list of copyright registration numbers and documents related to court filings. For each number, you need to find the artist name (often called "Author") and the artwork name (often called "Title of work") in the documents.
        You return your answer in a JSON format with the following keys: {
        """
                                                                        
        for reg_no in reg_nos:
            prompt += f'"{reg_no}": [artist_name, artwork_name], '
        prompt += "}. Do not include REDACTED registration numbers in your answer. If a registration number has no associated copyrighted picture, leave its list empty []. These are the documents: "

        prompt_list = [("text", prompt)]
        for i, pdf_file_path in enumerate(pdfs_to_consider):
            prompt_list.append(("pdf_path", pdf_file_path))

        ai_answer = vertex_genai_multi(prompt_list, model_name="gemini-2.0-flash-thinking-exp-01-21")
        ai_answer_json = get_json(ai_answer)

        if isinstance(ai_answer_json, dict):
            for reg_no, item in ai_answer_json.items():
                print(f"{reg_no}: {item}")
                if len(item) == 2:
                    reg_numbers_pictures_not_found["copyright"].append((reg_no, item[0], item[1]))
                else:
                    # check on USCO
                    info = get_info_using_reg_no(reg_no)
                    if info:
                        reg_numbers_pictures_not_found["copyright"].append((reg_no, info["Copyright Claimant"], info["Title"]))
                    # else:
                    #     reg_numbers_pictures_not_found["copyright"].append((reg_no, "", ""))

        reg_no_not_in_ai_answer = [reg_no for reg_no in reg_numbers_pictures_not_found_list if (reg_no not in ai_answer_json.keys() or ai_answer_json[reg_no] == [])]
        for reg_no in reg_no_not_in_ai_answer: 
            info = get_info_using_reg_no(reg_no)
            if info:
                reg_numbers_pictures_not_found["copyright"].append((reg_no, info["Copyright Claimant"], info["Title"]))
            # else:
            #     reg_numbers_pictures_not_found["copyright"].append((reg_no, "", ""))



# New helper function to process copyright images from Google search
@observe(capture_input=False, capture_output=False)
def process_copyright_images_from_google(df, index, reg_data_df, case_images_directory):
    folder_for_all_google_results = os.path.join(case_images_directory, "google_copyright_results")
    
    # Run Google image search
    reg_data_df = asyncio.run(get_copyright_images_from_google_api(reg_data_df, folder_for_all_google_results, case_images_directory))
    
    # Process the search results
    processed_count = 0
    for _, reg_row in reg_data_df.iterrows():
        if reg_row['best_google_image'] is not None:
            try: 
                img = cv2.imread(reg_row['best_google_image'])
                new_name_full = sanitize_name(df.at[index, "docket"]) + "_regno_" + str(reg_row['reg_no']) + "_full.webp"
                new_name = new_name_full.replace("_full", "")
                cv2.imwrite(os.path.join(case_images_directory, new_name_full), img, [cv2.IMWRITE_WEBP_QUALITY, 80])
                os.remove(reg_row['best_google_image'])
                shutil.copy(os.path.join(case_images_directory, new_name_full), os.path.join(case_images_directory, new_name))
                # Ensure reg_no is always a list of strings
                df.at[index, 'images']['copyrights'][new_name] = {'full_filename': [new_name_full], 'reg_no': [str(reg_row['reg_no'])]}
                processed_count += 1
            except:
                print(f"\033[91m 🔥 Error processing {reg_row['best_google_image']}. Image not added to the case.\033[0m")
    
    # If we successfully processed any images, mark for validation
    if processed_count > 0:
        df.at[index, 'validation_status'] = 'review_required'
        df.at[index, 'images_status']['copyright_status']['bygoogle']['count'] += processed_count

    langfuse_context.update_current_observation(
        input=f"{len(reg_data_df)} copyright registration numbers to look for: {reg_data_df['reg_no'].to_string(index=False)}",
        output=f"{len(df.at[index, 'images']['copyrights'])} copyrights found"
    )
    
    return processed_count
