import time
import random
import os
import urllib.parse
import undetected_chromedriver as uc
import pandas as pd
import re

from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import StaleElementReferenceException
from selenium.webdriver.chrome.options import Options

from Alerts.Chrome_Driver import get_driver, move_mouse_to, random_delay

# ❌⚠️📥🔥✅


def close_feedback_modal(driver):
    """
    Checks if the feedback modal is present and, if so, clicks the close button.
    """
    try:
        modal_close_button = WebDriverWait(driver, 3).until(
            EC.element_to_be_clickable((
                By.XPATH, 
                "//div[contains(@class, 'modal-content')]//button[contains(@class, 'close-modal')]"
            ))
        )
        move_mouse_to(driver, modal_close_button)
        modal_close_button.click()
        print("⚠️ Feedback modal closed.")
        time.sleep(1)
        return True
    except Exception:
        # Modal not present; do nothing.
        return False

def parse_record_details(driver):
    """
    Scrapes details from a detailed record page.
    Returns a dictionary mapping field names to their extracted values.
    It looks for the <cd-record> element and then:
      - For each <li> element inside, it grabs the label from the <strong> tag.
      - If an inner <ul> exists, it concatenates the child <li> texts.
      - Otherwise, it removes the label text from the overall text.
    """
    
    details_retries = 0
    while details_retries < 3:
        details = {}
        try:
            cd_record = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "cd-record"))
            )
            # Explicitly wait for the first li element to be present
            WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.XPATH, "//cd-record//li"))
            )
            li_elements = cd_record.find_elements(By.TAG_NAME, "li")
            for li in li_elements:
                label_elems = li.find_elements(By.TAG_NAME, "strong")
                if len(label_elems) == 0:
                    continue  # if not label on that row, continue to the next line
                label_elem = label_elems[0]

                label = label_elem.text.strip().rstrip(":")
                
                # Try to see if there is a <ul> inside (for multi-value fields)
                ul_elems = li.find_elements(By.TAG_NAME, "ul")
                if len(ul_elems) == 0: # Single value field
                    # Remove the label text from the full text
                    full_text = li.text.strip()
                    value = full_text.replace(label_elem.text, "").strip()
                else: # Multi-value field
                    li_texts = [item.text.strip() for item in ul_elems if item.text.strip()]
                    value = ", ".join(li_texts)
                details[label] = value
            break
        except Exception as e:
            found = close_feedback_modal(driver) 
            
            print(f"Error processing details page, pop up was found: {found}. Exception is: ", e)

            details_retries += 1
            if details_retries == 3:
                print(f"Failed to process detail page after 3 retries.")
            else:
                print(f"Retrying detail page (Attempt {details_retries} of 3)...")

                # Ensure we are on the correct tab by checking the URL or title
                if "publicrecords.copyright.gov/detailed-record" in driver.current_url:
                    print("We are on the correct tab.")
                else: 
                    print("We are not on the correct tab.")
                    return None

                time.sleep(2)  # Wait for 2 seconds before retrying
    return details

def format_record(details):
    """
    Maps the scraped details to the target structure.
    If any field is missing, an empty string is set.
    """
    return {
        "Registration Number / Date": details.get("Registration Number / Date", ""),
        "Type of Work": details.get("Type of Work", ""),
        "Title": details.get("Title", ""),
        "Application Title": details.get("Application Title", ""),
        "Date of Creation": details.get("Date of Creation", ""),
        "Date of Publication": details.get("Date of Publication", ""),
        "Copyright Claimant": details.get("Copyright Claimant", ""),
        "Authorship on Application": details.get("Authorship on Application", ""),
        "Rights and Permissions": details.get("Rights and Permissions", ""),
        "Description": details.get("Description", ""),
        "Nation of First Publication": details.get("Nation of First Publication", ""),
        "Names": details.get("Names", "")
    }


def get_info_using_reg_no(reg_no):
    """
    Navigates to the detailed record page of the Copyright Public Records site using the given registration number.
    Returns a dictionary containing the scraped details.
    """
    base_url = "https://publicrecords.copyright.gov"

    search_url = (
        f"{base_url}/advanced-search?page_number=1"
        f"&parent_query=%5B%7B%22operator_type%22:%22AND%22,%22column_name%22:%22registration_numbers%22,%22type_of_query%22:%22phrase%22,%22query%22:%22{reg_no}%22%7D%5D"
        f"&records_per_page=10&sort_order=%22asc%22"
        f"&highlight=true&model=%22%22"
    )
    
    # Initialize Chrome driver with options
    driver = get_driver()
    
    try:
        driver.get(search_url)
        
        try:
            # Wait for the search results table to be present
            WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "table-list-of-search-results")))
    
            # Wait for the first row to be present
            WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.XPATH, "//table[@id='table-list-of-search-results']//div[contains(@class,'search-result-pane') and not(contains(@class, 'footer'))]")))
            
            # Wait for the first row to have some text content before proceeding
            WebDriverWait(driver, 10).until(
                lambda d: driver.find_elements(By.XPATH, "//table[@id='table-list-of-search-results']//div[contains(@class,'search-result-pane') and not(contains(@class, 'footer'))]")[0].text.strip() != ""
            )
            
            # Fetch the rows
            rows = driver.find_elements(By.XPATH, "//table[@id='table-list-of-search-results']//div[contains(@class,'search-result-pane') and not(contains(@class, 'footer'))]")
            
            if len(rows) == 0:
                print(f"❌ No results found for registration number: {reg_no}")
                return None
            
            # Locate the 'Full Title' link in the first row
            a_link = rows[0].find_element(By.XPATH, ".//a[contains(@class, 'link')]")
            
            # Click on the link to navigate to the detail page
            a_link.click()
            random_delay(1, 2)
            
            # Close modal in the detail page if it appears
            close_feedback_modal(driver)
            
            # Parse record details and map the fields
            details = parse_record_details(driver)
            
            # Check if details is empty, wait a bit and retry once
            if not details or not details.get("Registration Number / Date"):
                print("Details were empty, waiting a bit and retrying...")
                time.sleep(1)
                details = parse_record_details(driver)
                if not details:
                    print("Failed to get details after retry.")
                    return None
            
            formatted_record = format_record(details)
            return formatted_record
            
        except Exception as e:
            print(f"❌ No results found for registration number: {reg_no}. Error: {str(e)}")
            return None
        
    except Exception as e:
        print(f"❌❌❌ Error retrieving information for registration number {reg_no}: {str(e)}")
        return None
    finally:
        driver.quit()

def scrape_USCO(plaintiff_name=None, start_date=None, end_date=None, df_existing=None):
    """
    Navigates to the advanced search page of the Copyright Public Records site with
    custom start_date and end_date (e.g. "2025-01-01 00:00:00"). It loops over every search 
    result page, opens each record in a new tab via Control+Click, extracts the required 
    fields, closes the tab, and finally assembles all records into a pandas DataFrame.
    """
    base_url = "https://publicrecords.copyright.gov"
    # Wrap the start_date and end_date in quotes and URL-encode them.
    if start_date is not None and end_date is not None:
        start_date_enc = urllib.parse.quote(f'"{start_date}"')
        end_date_enc = urllib.parse.quote(f'"{end_date}"')
        date_range = f"&date_field=%22representative_date%22&start_date={start_date_enc}&end_date={end_date_enc}"
        name = "2-D%20Artwork"
    else:
        date_range = ""

    if plaintiff_name is not None:
        name = urllib.parse.quote(plaintiff_name)
       
    records = []
    page_number = 1
    record_per_page = 50
    
    driver = None  # Initialize driver to None
    last_page = True # In case we crash before testing if we are on the last page

    while True:
        page_retries = 0
        while page_retries < 3:  # Retry the page up to 3 times
            try:
                # Check if the driver is still "alive" and recreate it if necessary
                if driver is None:
                    print("Driver is None.")
                    driver_alive = False
                else:
                    try:
                        driver.title  # Simple command to check if the driver is responsive
                        driver_alive = True
                    except Exception as e:
                        print("Error while trying to access driver.title:", e)
                        driver_alive = False

                if not driver_alive:
                    if driver is not None:
                        try:
                            driver.quit()  # Try to quit the old driver
                        except Exception:
                            pass  # Ignore any errors during quitting
                    print("Creating a new driver instance...")
                    driver = get_driver()

                # Build the advanced search URL for the current page.
                # %5B == [      %7B == {       %22 == "  "     %5D == ]        %7D == }     %20 == space
                search_url = (
                    f"{base_url}/advanced-search?page_number={page_number}"
                    f"&parent_query=%5B%7B%22operator_type%22:%22AND%22,%22column_name%22:%22all_names%22,%22type_of_query%22:%22phrase%22,%22query%22:%22{name}%22%7D%5D"
                    f"&records_per_page={record_per_page}"
                    f"&type_of_record=%5B%22registration%22%5D"
                    f"&sort_field=%22representative_date%22"
                    f"&sort_order=%22asc%22"
                    f"&highlight=true"
                    f"&registration_status=%5B%22published%22%5D"
                    f"&type_of_work=%5B%22visual_material%22%5D"
                    f"&model=%22%22{date_range}"
                )


                driver.get(search_url)

                # Wait for the search results table to be present.
                WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "table-list-of-search-results")))
    
                # Wait for the first row to be present.
                WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.XPATH, "//table[@id='table-list-of-search-results']/tbody/div[contains(@class, 'd-flex')]")))
                # Close any feedback modal if it appears.
                found = close_feedback_modal(driver)

                # Wait for the first row to have some text content before proceeding
                WebDriverWait(driver, 10).until(
                    lambda d: driver.find_elements(By.XPATH, "//table[@id='table-list-of-search-results']/tbody/div[contains(@class, 'd-flex')]")[0].text.strip() != ""
                )
                
                # Fetch the rows
                rows = driver.find_elements(By.XPATH, "//table[@id='table-list-of-search-results']/tbody/div[contains(@class, 'd-flex')]")
                
                print(f"Found {len(rows)} result rows on page {page_number}")
                
                if len(rows) < record_per_page:
                    print(f"Page {page_number} has less than {record_per_page} rows. Assuming it is the last page.")
                    last_page = True
                else:
                    last_page = False

                
                row_index = 0
                while row_index < len(rows):
                    row_retries = 0
                    row_processed = False  # Flag to indicate if the row has been processed or skipped
                    while row_retries < 3 and not row_processed:  # Retry the row up to 3 times
                        try:
                            # Locate the 'Full Title' link in the row.
                            a_link = rows[row_index].find_element(By.XPATH, ".//a[contains(@class, 'link')]")

                            reg_no_match = re.search(r"VA\d{10}", rows[row_index].text)
                            if df_existing is not None and reg_no_match:  # If a DF was provided, check if the reg_no is already in the DF
                                reg_no = reg_no_match.group(0)
                                if any(reg_no in item_reg_no for item_reg_no in df_existing["Registration Number / Date"]):
                                    print("Registration number already scraped on row: ", row_index)
                                    row_processed = True  # Set the flag to True
                                    continue  # Skip to the next row

                        
                            # Scroll the link into view before clicking.
                            driver.execute_script("arguments[0].scrollIntoView(); window.scrollBy(0, -150);", a_link)

                            
                            # Use ActionChains to move to the element before clicking
                            actions = ActionChains(driver)
                            actions.move_to_element(a_link).perform()
                            time.sleep(0.5)  # Fixed delay of 0.5 seconds
                            actions.key_down(Keys.CONTROL).click(a_link).key_up(Keys.CONTROL).perform()
                            time.sleep(1)
                            
                            # Switch to the newly opened tab.
                            driver.switch_to.window(driver.window_handles[-1])
                            
                            # Close modal in the detail page if it appears.
                            # found = close_feedback_modal(driver)
                            # if found: # need to reaquire the row
                            #     rows = driver.find_elements(By.XPATH, "//table[@id='table-list-of-search-results']//div[contains(@class,'search-result-pane') and not(contains(@class, 'footer'))]")

                            
                            # Parse record details and map the fields.
                            details = parse_record_details(driver)
                            
                            # Check if details is empty, wait a bit and retry once
                            if not details or not details.get("Registration Number / Date"):
                                print("Details were empty, waiting a bit and retrying...")
                                time.sleep(1)
                                raise Exception("Details were empty.")
                            
                            record = format_record(details)
                            records.append(record)
                            
                            # Close detail tab and switch back to main search results.
                            driver.close()
                            driver.switch_to.window(driver.window_handles[0])
                            
                            row_processed = True # Set the flag to True if row was processed
                        except Exception as e:
                            close_feedback_modal(driver)
                            print(f"❌ Error processing row {row_index} on page {page_number}:", e)

                            row_retries += 1
                            if row_retries == 3:
                                print(f"Failed to process row {row_index} after 3 retries.")
                            else:
                                print(f"Retrying row {row_index} (Attempt {row_retries} of 3)...")

                                # Ensure we are on the correct tab by checking the URL or title
                                if "publicrecords.copyright.gov/detailed-record" in driver.current_url:
                                    print("Not on the correct tab. Clsoing the details tab.")
                                    driver.close()
                                    driver.switch_to.window(driver.window_handles[0])
                                
                                # Re-fetch the rows only if a StaleElementReferenceException occurs
                                if isinstance(e, StaleElementReferenceException):
                                    rows = driver.find_elements(By.XPATH, "//table[@id='table-list-of-search-results']/tbody/div[contains(@class, 'd-flex')]")

                                time.sleep(2)  # Wait for 2 seconds before retrying
                        finally:
                            if not row_processed:
                                row_retries += 1
                    # Move to the next row only if the current one was processed or skipped
                    row_index += 1
                
                break # Exit the page retry loop

            except:
                print("❌❌ Something went wrong on page: ", page_number)
                page_retries += 1
                if page_retries == 3:
                    print("❌❌❌ Failed to scrape page after 3 retries.")
                    break # Exit the page retry loop
            
        if not last_page:
            page_number += 1
        else:
            break  # Exit the page loop if it's the last page

    driver.quit()
    df_new = pd.DataFrame(records)
    if df_existing is not None:
        df_new = pd.concat([df_existing, df_new])
    return df_new



if __name__ == "__main__":
    # result = get_info_using_reg_no("VA0002309397")
    # print(result)
    
    df_already_scraped = pd.read_csv("copyright_records.csv")
    df = scrape_USCO("2024-02-01 00:00:00", "2024-02-28 00:00:00", df_already_scraped)
    df_new = pd.concat([df_already_scraped, df])
    df_new.to_csv("copyright_records.csv", index=False)
