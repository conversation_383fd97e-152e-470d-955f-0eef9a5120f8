import os, sys
sys.path.append(os.getcwd())
# import oracledb
import mysql.connector
import time
import logging
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type
from logdata import log_message

"""
What I tried to make it faster:

1. SSH tunnel to the server: made it slower
2. Python pure connection: made it slower (but not sure)

"""

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def empty_table(table_name):
    connection = get_gz_connection()
    cursor = connection.cursor()
    cursor.execute(f"TRUNCATE TABLE {table_name}")
    connection.commit()
    cursor.close()
    connection.close()


# Connection pooling with lazy initialization
from mysql.connector import pooling
import threading

# Global connection pools dictionary to track and reuse pools
_custom_pools = {}

class LazyConnectionPool:
    def __init__(self, pool_name, pool_size, **db_config):
        self.pool_name = pool_name
        self.max_pool_size = pool_size
        self.db_config = db_config
        self.connections = []  # Available connections
        self.in_use = set()    # Currently in-use connections
        self.lock = threading.RLock()  # For thread safety
        self.initialize_in_progress = False
        
    @retry(
        stop=stop_after_attempt(3),  # Retry up to 3 times
        wait=wait_exponential(multiplier=1, min=2, max=10),  # Wait 2s, 4s, 8s between retries
        retry=retry_if_exception_type(mysql.connector.Error) # Only retry on mysql connector errors
    )
    def _create_connection(self):
        """Creates a new database connection with retry logic."""
        conn = mysql.connector.connect(**self.db_config)
        return conn

    def initialize_background(self):
        """Initialize remaining connections in background"""
        if self.initialize_in_progress:
            return
            
        def create_connections():
            self.initialize_in_progress = True
            try:
                with self.lock:
                    current_count = len(self.connections) + len(self.in_use)
                    to_create = self.max_pool_size - current_count
                
                log_message(f"Creating {to_create} additional connections in background")
                for _ in range(to_create):
                    try:
                        # Use the retry-enabled helper method
                        conn = self._create_connection()
                        with self.lock:
                            self.connections.append(conn)
                        log_message(f"Added background connection to pool (total: {len(self.connections) + len(self.in_use)})")
                    except Exception as e:
                        # Log error if retries ultimately fail
                        log_message(f"Error creating background connection after retries: {e}")
            finally:
                self.initialize_in_progress = False
                
        # Start background thread
        bg_thread = threading.Thread(target=create_connections)
        bg_thread.daemon = True  # Allow program to exit even if thread is running
        bg_thread.start()
    
    def get_connection(self):
        """Get a connection from the pool or create a new one if needed"""
        with self.lock:
            # If we have available connections, use one
            if self.connections:
                conn = self.connections.pop()
                try:
                    # Verify connection still works (it might have timed out)
                    if not is_connection_alive(conn):
                        log_message("Stale connection detected, creating new one.")
                        conn = self._create_connection() # Use helper
                except Exception as e:
                    # If verification fails or creating new fails after retries
                    log_message(f"Error verifying/replacing connection: {e}. Creating new one.")
                    try:
                        conn = self._create_connection() # Use helper
                    except Exception as final_e:
                         log_message(f"Failed to create connection even after retries: {final_e}")
                         raise final_e # Re-raise if all retries fail

                self.in_use.add(conn)
                return conn
                
            # If no available connections but we haven't reached max, create new one
            current_count = len(self.connections) + len(self.in_use)
            if current_count < self.max_pool_size:
                try:
                    conn = self._create_connection() # Use helper
                    self.in_use.add(conn)

                    # Start background initialization if this is the first connection
                    if current_count == 0 and not self.initialize_in_progress:
                        bg_thread = threading.Thread(target=self.initialize_background)
                        bg_thread.daemon = True
                        bg_thread.start()

                    return conn
                except Exception as e:
                    log_message(f"Failed to create initial/new connection after retries: {e}")
                    raise # Re-raise if all retries fail
                
            # If we're at max pool size, wait for a connection to be returned
            # Consider adding a timeout wait here instead of immediate exception
            log_message(f"Connection pool {self.pool_name} exhausted (max size: {self.max_pool_size}). Waiting might be needed.", level='warning')
            raise Exception(f"Connection pool exhausted (max size: {self.max_pool_size})")
    
    def return_connection(self, connection):
        """Return a connection to the pool"""
        with self.lock:
            if connection in self.in_use:
                self.in_use.remove(connection)
                if is_connection_alive(connection):
                    # Only add back if pool is not full (shouldn't happen often, but safety)
                    if len(self.connections) < self.max_pool_size - len(self.in_use):
                         self.connections.append(connection)
                    else:
                         try:
                              connection.close() # Close surplus connection
                         except: pass # Ignore errors closing
                else:
                    log_message("Returned connection is dead. Attempting to replace.")
                    # If connection is dead, try to create a replacement if pool below max
                    if len(self.connections) + len(self.in_use) < self.max_pool_size:
                        try:
                            # Use a separate thread to avoid blocking the return call
                            def replace_conn():
                                try:
                                    new_conn = self._create_connection() # Use helper
                                    with self.lock:
                                         # Check again in case state changed
                                         if len(self.connections) + len(self.in_use) < self.max_pool_size:
                                              self.connections.append(new_conn)
                                              log_message("Successfully replaced dead connection.")
                                         else:
                                              try: new_conn.close()
                                              except: pass
                                except Exception as e:
                                    log_message(f"Failed to replace dead connection after retries: {e}")

                            replace_thread = threading.Thread(target=replace_conn)
                            replace_thread.daemon = True
                            replace_thread.start()

                        except Exception as e:
                            # This catch is mainly for thread creation errors, unlikely
                            log_message(f"Error initiating replacement for dead connection: {e}")
            # else: connection not part of 'in_use', maybe already returned or invalid handle



def get_gz_connection(host=os.getenv("MYSQL_HOST"), user=os.getenv("MYSQL_USER"), 
                       password=os.getenv("MYSQL_PASSWORD"), database=os.getenv("MYSQL_DATABASE"), 
                       port=3306):
    """
    Get a connection from a custom pool that lazily creates connections as needed.
    This creates just one connection initially and starts a background thread for others.
    """
    base_key = f"{host}_{user}_{database}_{port}"
    
    # Create the pool if it doesn't exist yet
    if base_key not in _custom_pools:
        try:
            # Generate a unique pool name
            pool_name = f"lazy_pool_{base_key}"
            dbconfig = {
                'host': host, 
                'port': port, 
                'user': user, 
                'password': password, 
                'database': database, 
                # 'use_pure': True,  # Not compatible with compression
                'autocommit': True, 
                # 'buffered': True, 
                'charset': 'utf8mb4', 
                'collation': 'utf8mb4_unicode_ci', 
                'consume_results': True, 
                'connect_timeout': 15, 
                'connection_timeout': 15,
                'ssl_disabled': True,  # Disable SSL to avoid SSL version mismatch issues
                'compress': True,       # Enable network compression, but leads to error: unpack requires a buffer of 4 bytes if used with 'use_pure'
            }
            
            # Create our custom pool
            start_time = time.time()
            pool = LazyConnectionPool(pool_name=pool_name, pool_size=10, **dbconfig)
            end_time = time.time()
            
            _custom_pools[base_key] = pool
            print(f"Created lazy connection pool manager: {pool_name} in {end_time - start_time:.1f} seconds")
        except Exception as e:
            log_message(f"Error creating connection pool: {e}")
            raise
    
    # Get connection from pool
    try:
        start_time = time.time()
        # Retrieve the pool object from the dictionary first
        pool = _custom_pools[base_key] 
        connection = pool.get_connection()
        end_time = time.time()
        log_message(f"Retrieved connection from pool {pool.pool_name} in {end_time - start_time:.1f} seconds")
        
        # Create a wrapper that returns the connection to the pool when closed
        original_close = connection.close
        def close_and_return():
            _custom_pools[base_key].return_connection(connection)
            
        connection.close = close_and_return
        
        return connection
    except Exception as e:
        log_message(f"Error getting connection from pool: {e}")
        raise


def is_connection_alive(connection):
    """Check if the database connection is still alive"""
    try:
        if connection is None:
            return False
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            cursor.fetchone()
        return True
    except Exception:
        return False


if __name__ == "__main__":
    # for i in range(10):
    #     connection = get_lazy_connection()
    import time
    connection = get_gz_connection()
    time.sleep(10)
    connection = get_gz_connection()
    time.sleep(10)
    connection = get_gz_connection()
    time.sleep(10)
    connection = get_gz_connection()
    time.sleep(10)
    connection = get_gz_connection()
    

    # pass
    # try:
    #     connection = get_us_connection()
    #     print("Connected to Oracle DB successfully!")
    #     connection.close()
    # except oracledb.DatabaseError as e:
    #     print(f"Error: {e}")