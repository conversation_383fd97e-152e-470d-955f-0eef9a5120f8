"""
Setup script for PostgreSQL database schema.
This script initializes the PostgreSQL database with the required tables and triggers
for the IP Infringement Detection system.
"""

import os
import psycopg2
from dotenv import load_dotenv
import logging

# Load environment variables from .env file
load_dotenv()

# Basic Logging Setup
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def setup_postgres():
    """
    Set up PostgreSQL database schema.
    """
    # Get PostgreSQL connection details from environment variables
    postgres_host = os.getenv("POSTGRES_HOST")
    postgres_port = os.getenv("POSTGRES_PORT")
    postgres_user = os.getenv("POSTGRES_USER")
    postgres_password = os.getenv("POSTGRES_PASSWORD")
    postgres_db = os.getenv("POSTGRES_DB")

    # Connect to PostgreSQL
    conn = psycopg2.connect(
        host=postgres_host,
        port=postgres_port,
        user=postgres_user,
        password=postgres_password,
        dbname=postgres_db
    )
    conn.autocommit = True
    cursor = conn.cursor()

    # Create UUID extension
    create_uuid_extension(cursor)

    # Create trigger function for automatic timestamp updates
    create_timestamp_trigger_function(cursor)

    # Create tables
    # create_trademarks_table(cursor)
    # create_patents_table(cursor)
    # create_copyrights_table(cursor)
    # create_tro_cpc_classifications_table(cursor) # Add this line

    # Create new tables as requested
    # create_new_patents_table(cursor)
    create_new_patents_cpc_ipc_assignments_table(cursor)
    create_new_patents_uspc_assignments_table(cursor)

    # Close connection
    cursor.close()
    conn.close()

def create_uuid_extension(cursor):
    """
    Create UUID extension in PostgreSQL.
    """
    cursor.execute("CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\";")

def create_timestamp_trigger_function(cursor):
    """
    Create trigger function for automatic timestamp updates.
    """
    cursor.execute("""
    CREATE OR REPLACE FUNCTION trigger_set_timestamp()
    RETURNS TRIGGER AS $$
    BEGIN
      NEW.update_time = NOW();
      RETURN NEW;
    END;
    $$ LANGUAGE plpgsql;
    """)

def create_trademarks_table(cursor):
    """
    Create trademarks table in PostgreSQL.
    """
    table_name = "trademarks"
    print(f"Checking if table '{table_name}' exists...")
    cursor.execute(f"SELECT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = '{table_name}');")
    table_exists = cursor.fetchone()[0]

    if table_exists:
        print(f"ℹ️ Table '{table_name}' already exists.")
    else:
        print(f"✨ Creating table '{table_name}'...")

    cursor.execute("""
    CREATE TABLE IF NOT EXISTS trademarks (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        reg_no TEXT,
        ser_no TEXT,
        TRO TEXT,
        applicant_name TEXT,
        mark_text TEXT,
        int_cls BIGINT[], -- Array of BigIntegers
        filing_date DATE, -- Renamed from 'date'
        plaintiff_ids BIGINT[], -- Array of BigIntegers
        nb_suits BIGINT,
        country_codes TEXT[], -- Array of TEXT
        associated_marks TEXT[], -- Array of TEXT
        info_source TEXT,
        image_source TEXT,
        certificate_source TEXT,
        mark_current_status_code INTEGER,
        mark_feature_code INTEGER,
        mark_standard_character_indicator BOOLEAN,
        mark_disclaimer_text TEXT[], -- Array of TEXT
        mark_disclaimer_text_daily TEXT[], -- For D0, D1. Store the <text> content
        mark_image_colour_claimed_text TEXT,
        mark_image_colour_part_claimed_text TEXT,
        mark_image_colour_statement_daily TEXT[], -- For CC, CD. Store the <text> content
        mark_translation_statement_daily TEXT, -- For TR. Store the <text> content
        name_portrait_statement_daily TEXT, -- For N0. Store the <text> content
        mark_description_statement_daily TEXT, -- For DM. Store the <text> content
        certification_mark_statement_daily TEXT, -- For CS. Store the <text> content
        lining_stippling_statement_daily TEXT, -- For LS. Store the <text> content
        section_2f_statement_daily TEXT, -- For TF. Store the <text> content
        national_design_code TEXT[], -- Array of TEXT
        goods_services JSONB, -- Store list of structured objects as JSONB
        goods_services_text_daily TEXT, -- For GS type codes. Stores raw text block
        case_file_statements_other JSONB, -- For other type codes (AF, A0, B0, CU, FN, IN, MD, MK, NR, OR, PM)
        mark_current_status_external_description_text TEXT,
        create_time TIMESTAMPTZ NOT NULL DEFAULT now(),
        update_time TIMESTAMPTZ NOT NULL DEFAULT now()
    );

    -- Indexes
    CREATE INDEX IF NOT EXISTS idx_trademarks_reg_no ON trademarks (reg_no);
    CREATE INDEX IF NOT EXISTS idx_trademarks_ser_no ON trademarks (ser_no);

    -- Trigger (Apply after table creation)
    DROP TRIGGER IF EXISTS set_timestamp ON trademarks; -- Ensure idempotency
    CREATE TRIGGER set_timestamp
    BEFORE UPDATE ON trademarks
    FOR EACH ROW
    EXECUTE FUNCTION trigger_set_timestamp();

    -- Add UNIQUE constraint to ser_no (idempotent)
    ALTER TABLE trademarks DROP CONSTRAINT IF EXISTS trademarks_ser_no_unique;
    ALTER TABLE trademarks ADD CONSTRAINT trademarks_ser_no_unique UNIQUE (ser_no);
    """)

    if not table_exists:
        print(f"✅ Table '{table_name}' created successfully.")

def create_patents_table(cursor):
    """
    Create patents_all table and associated classification tables in PostgreSQL.
    """
    table_names = ["patents_all", "patents_cpc_ipc_definitions", "patents_uspc_definitions", "patents_uspc_assignments", "patents_loc_definitions", "patents_cpc_ipc_assignments"]
    print("Checking and creating patents records and classification tables...")

    # Check existence for each table before attempting creation
    table_exists_status = {}
    for table_name in table_names:
        cursor.execute(f"SELECT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = '{table_name}');")
        table_exists_status[table_name] = cursor.fetchone()[0]
        if table_exists_status[table_name]:
            print(f"  ℹ️ Table '{table_name}' already exists.")
        else:
            print(f"  ✨ Creating table '{table_name}'...")

    cursor.execute("""
    -- =====================================================================
    -- Core Patent Table (Renamed from patents)
    -- =====================================================================
    CREATE TABLE IF NOT EXISTS patents_all (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(), -- Auto-generated UUID primary key
        reg_no TEXT,
        document_id TEXT UNIQUE,                        -- Document ID, used for FKs
        TRO BOOLEAN,
        inventors TEXT,
        assignee TEXT,
        applicant TEXT,
        patent_title TEXT,
        date_published DATE,
        plaintiff_ids BIGINT[],                         -- Array of BigIntegers
        patent_type TEXT,                               -- e.g., 'utility' or 'design'
        abstract TEXT,
        associated_patents TEXT[],                      -- Array of TEXT
        design_page_numbers INTEGER[],                  -- Array of Integers
        pdf_source TEXT,
        image_source TEXT,
        certificate_source TEXT,
        loc_code TEXT,
        loc_edition TEXT,
        uspc_class TEXT,
        uspc_subclass TEXT,
        create_time TIMESTAMPTZ NOT NULL DEFAULT now(), -- Timestamp of record creation
        update_time TIMESTAMPTZ NOT NULL DEFAULT now()  -- Timestamp of last record update
    );

    COMMENT ON COLUMN patents_all.TRO IS 'Indicates if a Temporary Restraining Order is associated with the patent record.';
    COMMENT ON COLUMN patents_all.loc_code IS 'Locarno classification code';
    COMMENT ON COLUMN patents_all.loc_edition IS 'Locarno classification edition';
    COMMENT ON COLUMN patents_all.uspc_class IS 'USPC classification class';
    COMMENT ON COLUMN patents_all.uspc_subclass IS 'USPC classification subclass';
    COMMENT ON COLUMN patents_all.document_id IS 'Document ID, used for foreign key references from assignment tables. Should be unique.';
    COMMENT ON COLUMN patents_all.plaintiff_ids IS 'Array storing BigInt IDs.';
    COMMENT ON COLUMN patents_all.associated_patents IS 'Array storing associated patent identifiers as text.';
    COMMENT ON COLUMN patents_all.design_page_numbers IS 'Array storing relevant page numbers for designs.';

    -- =====================================================================
    -- Classification Definition Tables (Structure Only)
    -- =====================================================================

    -- Table for CPC and IPC Definitions
    CREATE TABLE IF NOT EXISTS patents_cpc_ipc_definitions (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        classification_type TEXT,                 -- Type of classification (e.g., 'CPC', 'IPC')
        publish_date DATE,                        -- The publication date of the classification definition
        section TEXT,                             -- Classification section (e.g., A, B, C)
        class TEXT,                               -- Classification class
        subclass TEXT,                            -- Classification subclass
        main_group TEXT,                          -- Classification main group
        sub_group TEXT,                           -- Classification subgroup
        definition TEXT,                          -- The textual definition of the classification entry
        CONSTRAINT unique_classification_definition UNIQUE (classification_type, publish_date, section, class, subclass, main_group, sub_group)
    );

    COMMENT ON TABLE patents_cpc_ipc_definitions IS 'Stores CPC and IPC classification definitions, including their hierarchical structure and textual descriptions.';
    COMMENT ON COLUMN patents_cpc_ipc_definitions.classification_type IS 'Type of classification (e.g., ''CPC'', ''IPC'').';
    COMMENT ON COLUMN patents_cpc_ipc_definitions.publish_date IS 'The publication date of the classification definition.';
    COMMENT ON COLUMN patents_cpc_ipc_definitions.section IS 'Classification section (e.g., A, B, C).';
    COMMENT ON COLUMN patents_cpc_ipc_definitions.class IS 'Classification class.';
    COMMENT ON COLUMN patents_cpc_ipc_definitions.subclass IS 'Classification subclass.';
    COMMENT ON COLUMN patents_cpc_ipc_definitions.main_group IS 'Classification main group.';
    COMMENT ON COLUMN patents_cpc_ipc_definitions.sub_group IS 'Classification subgroup.';
    COMMENT ON COLUMN patents_cpc_ipc_definitions.definition IS 'The textual definition of the classification entry.';

    -- Table for USPC Definitions
    CREATE TABLE IF NOT EXISTS patents_uspc_definitions (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        class TEXT,
        subclass TEXT,
        definition TEXT,
        CONSTRAINT unique_uspc_definition UNIQUE (class, subclass)
    );

    COMMENT ON TABLE patents_uspc_definitions IS 'Stores USPC classification definitions, including their class, subclass, and textual descriptions.';
    COMMENT ON COLUMN patents_uspc_definitions.id IS 'Unique identifier for the USPC definition.';
    COMMENT ON COLUMN patents_uspc_definitions.class IS 'USPC classification class.';
    COMMENT ON COLUMN patents_uspc_definitions.subclass IS 'USPC classification subclass.';
    COMMENT ON COLUMN patents_uspc_definitions.definition IS 'The textual definition of the USPC classification entry.';

    -- Table for USPC Assignments
    CREATE TABLE IF NOT EXISTS patents_uspc_assignments_all (
        patents_id UUID NOT NULL,                     -- Foreign key to patents_all.id
        uspc_id UUID NOT NULL,                        -- Foreign key to patents_uspc_definitions.id
        type TEXT NOT NULL,                           -- 'main' or 'extra'

        PRIMARY KEY (patents_id, uspc_id, type),      -- Ensures each patent-USPC-type link is unique

        CONSTRAINT fk_uspc_assignment_patent_record
            FOREIGN KEY(patents_id)
            REFERENCES patents_all(id)
            ON DELETE CASCADE,

        CONSTRAINT fk_uspc_assignment_uspc_definition
            FOREIGN KEY(uspc_id)
            REFERENCES patents_uspc_definitions(id)
            ON DELETE CASCADE
    );

    COMMENT ON TABLE patents_uspc_assignments IS 'Stores USPC classification assignments for patents, linking patents_all to patents_uspc_definitions.';
    COMMENT ON COLUMN patents_uspc_assignments.patents_id IS 'References the id in the patents_all table.';
    COMMENT ON COLUMN patents_uspc_assignments.uspc_id IS 'References the id in the patents_uspc_definitions table.';
    COMMENT ON COLUMN patents_uspc_assignments.type IS 'Type of USPC assignment: main (primary classification) or extra (further classification).';

    -- Table for Locarno Definitions
    CREATE TABLE IF NOT EXISTS patents_loc_definitions (
        loc_edition TEXT,
        loc_code TEXT,
        class TEXT,
        class_definition TEXT,
        subclass TEXT,
        subclass_definition TEXT,
        PRIMARY KEY (loc_edition, loc_code)
    );

    COMMENT ON TABLE patents_loc_definitions IS 'Stores Locarno classification definitions, including their hierarchical structure and textual descriptions.';
    COMMENT ON COLUMN patents_loc_definitions.loc_edition IS 'The edition of the Locarno classification. Part of the composite primary key.';
    COMMENT ON COLUMN patents_loc_definitions.loc_code IS 'The Locarno classification code (e.g., 13-02). Part of the composite primary key.';
    COMMENT ON COLUMN patents_loc_definitions.class IS 'The Locarno class text/title.';
    COMMENT ON COLUMN patents_loc_definitions.class_definition IS 'The detailed definition or scope of the Locarno class.';
    COMMENT ON COLUMN patents_loc_definitions.subclass IS 'The Locarno subclass text/title.';
    COMMENT ON COLUMN patents_loc_definitions.subclass_definition IS 'The detailed definition or scope of the Locarno subclass.';

    -- =====================================================================
    -- Classification Assignment Tables
    -- =====================================================================

    -- Table for CPC and IPC Assignments
    CREATE TABLE IF NOT EXISTS patents_cpc_ipc_assignments_all (
        patents_id UUID NOT NULL,                     -- Foreign key to patents_all.id
        cpc_ipc_id UUID NOT NULL,                     -- Foreign key to patents_cpc_ipc_definitions.id

        PRIMARY KEY (patents_id, cpc_ipc_id),         -- Ensures each patent-classification link is unique

        CONSTRAINT fk_assignment_patent_record
            FOREIGN KEY(patents_id)
            REFERENCES patents_all(id)
            ON DELETE CASCADE,

        CONSTRAINT fk_assignment_classification_definition
            FOREIGN KEY(cpc_ipc_id)
            REFERENCES patents_cpc_ipc_definitions(id)
            ON DELETE CASCADE
    );

    COMMENT ON TABLE patents_cpc_ipc_assignments IS 'Stores CPC and IPC classification assignments for patents, linking patents_all to patents_cpc_ipc_definitions.';
    COMMENT ON COLUMN patents_cpc_ipc_assignments.patents_id IS 'References the id in the patents_all table.';
    COMMENT ON COLUMN patents_cpc_ipc_assignments.cpc_ipc_id IS 'References the id in the patents_cpc_ipc_definitions table.';

    -- =====================================================================
    -- Trigger for patents_all update_time
    -- =====================================================================
    DROP TRIGGER IF EXISTS set_timestamp ON patents_all; -- Ensure idempotency
    CREATE TRIGGER set_timestamp
    BEFORE UPDATE ON patents_all
    FOR EACH ROW
    EXECUTE FUNCTION trigger_set_timestamp();
    """)

    # Print final status for each table
    for table_name in table_names:
        if not table_exists_status[table_name]:
            print(f"  ✅ Table '{table_name}' created successfully.")


def create_copyrights_table(cursor):
    """
    Create copyrights table in PostgreSQL.
    """
    table_name = "copyrights"
    print(f"Checking if table '{table_name}' exists...")
    cursor.execute(f"SELECT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = '{table_name}');")
    table_exists = cursor.fetchone()[0]

    if table_exists:
        print(f"ℹ️ Table '{table_name}' already exists.")
    else:
        print(f"✨ Creating table '{table_name}'...")

    cursor.execute("""
    CREATE TABLE IF NOT EXISTS copyrights (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        TRO TEXT,
        registration_number TEXT,
        registration_date DATE,
        type_of_work TEXT,
        title TEXT,
        date_of_creation INTEGER, -- Storing year only as INTEGER
        date_of_publication DATE,
        copyright_claimant TEXT, -- Consider TEXT[] if multiple structured claimants
        authorship_on_application TEXT,
        rights_and_permissions TEXT,
        description TEXT,
        nation_of_first_publication TEXT,
        names TEXT, -- Consider TEXT[] if multiple structured names
        plaintiff_ids BIGINT[], -- Array of BigIntegers for plaintiff IDs, matching patents and trademarks
        create_time TIMESTAMPTZ NOT NULL DEFAULT now(),
        update_time TIMESTAMPTZ NOT NULL DEFAULT now()
    );

    -- Indexes
    CREATE INDEX IF NOT EXISTS idx_copyrights_reg_no ON copyrights (registration_number);

    -- Trigger (Apply after table creation)
    DROP TRIGGER IF EXISTS set_timestamp ON copyrights; -- Ensure idempotency
    CREATE TRIGGER set_timestamp
    BEFORE UPDATE ON copyrights
    FOR EACH ROW
    EXECUTE FUNCTION trigger_set_timestamp();
    """)

    if not table_exists:
        print(f"✅ Table '{table_name}' created successfully.")

def create_tro_cpc_classifications_table(cursor):
    """
    Create tro_cpc_classification table in PostgreSQL.
    """
    table_name = "tro_cpc_classifications"
    print(f"Checking if table '{table_name}' exists...")
    cursor.execute(f"SELECT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = '{table_name}');")
    table_exists = cursor.fetchone()[0]

    if table_exists:
        print(f"ℹ️ Table '{table_name}' already exists.")
    else:
        print(f"✨ Creating table '{table_name}'...")

    cursor.execute("""
    CREATE TABLE IF NOT EXISTS tro_cpc_classifications (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        section TEXT,
        class TEXT,
        subclass TEXT,
        CONSTRAINT unique_cpc_classifications UNIQUE (section, class, subclass)
    );
    """)

    if not table_exists:
        print(f"✅ Table '{table_name}' created successfully.")

def create_new_patents_table(cursor):
    """
    Create patents table with manually assigned UUID primary key.
    """
    table_name = "patents"
    print(f"Checking if table '{table_name}' exists...")
    cursor.execute(f"SELECT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = '{table_name}');")
    table_exists = cursor.fetchone()[0]

    if table_exists:
        print(f"ℹ️ Table '{table_name}' already exists.")
    else:
        print(f"✨ Creating table '{table_name}'...")

    cursor.execute("""
    CREATE TABLE IF NOT EXISTS patents (
        id UUID PRIMARY KEY, -- Manually assigned UUID primary key
        reg_no TEXT,
        document_id TEXT UNIQUE, -- Document ID, used for FKs
        TRO BOOLEAN,
        inventors TEXT,
        assignee TEXT,
        applicant TEXT,
        patent_title TEXT,
        date_published DATE,
        plaintiff_ids BIGINT[], -- Array of BigIntegers
        patent_type TEXT, -- e.g., 'utility' or 'design'
        abstract TEXT,
        associated_patents TEXT[], -- Array of TEXT
        design_page_numbers INTEGER[], -- Array of Integers
        pdf_source TEXT,
        image_source TEXT,
        certificate_source TEXT,
        loc_code TEXT,
        loc_edition TEXT,
        uspc_class TEXT,
        uspc_subclass TEXT,
        create_time TIMESTAMPTZ NOT NULL DEFAULT now(), -- Timestamp of record creation
        update_time TIMESTAMPTZ NOT NULL DEFAULT now() -- Timestamp of last record update
    );

    COMMENT ON COLUMN patents.TRO IS 'Indicates if a Temporary Restraining Order is associated with the patent record.';
    COMMENT ON COLUMN patents.loc_code IS 'Locarno classification code';
    COMMENT ON COLUMN patents.loc_edition IS 'Locarno classification edition';
    COMMENT ON COLUMN patents.uspc_class IS 'USPC classification class';
    COMMENT ON COLUMN patents.uspc_subclass IS 'USPC classification subclass';
    COMMENT ON COLUMN patents.document_id IS 'Document ID, used for foreign key references from assignment tables. Should be unique.';
    COMMENT ON COLUMN patents.plaintiff_ids IS 'Array storing BigInt IDs.';
    COMMENT ON COLUMN patents.associated_patents IS 'Array storing associated patent identifiers as text.';
    COMMENT ON COLUMN patents.design_page_numbers IS 'Array storing relevant page numbers for designs.';

    -- Trigger for patents update_time
    DROP TRIGGER IF EXISTS set_timestamp ON patents; -- Ensure idempotency
    CREATE TRIGGER set_timestamp
    BEFORE UPDATE ON patents
    FOR EACH ROW
    EXECUTE FUNCTION trigger_set_timestamp();
    """)

    if not table_exists:
        print(f"✅ Table '{table_name}' created successfully.")

def create_new_patents_cpc_ipc_assignments_table(cursor):
    """
    Create patents_cpc_ipc_assignments table referencing the new patents table.
    """
    table_name = "patents_cpc_ipc_assignments"
    print(f"Checking if table '{table_name}' exists...")
    cursor.execute(f"SELECT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = '{table_name}');")
    table_exists = cursor.fetchone()[0]

    if table_exists:
        print(f"ℹ️ Table '{table_name}' already exists.")
    else:
        print(f"✨ Creating table '{table_name}'...")

    cursor.execute("""
    CREATE TABLE IF NOT EXISTS patents_cpc_ipc_assignments (
        patents_id UUID NOT NULL, -- Foreign key to patents.id
        cpc_ipc_id UUID NOT NULL, -- Foreign key to patents_cpc_ipc_definitions.id

        PRIMARY KEY (patents_id, cpc_ipc_id), -- Ensures each patent-classification link is unique

        CONSTRAINT fk_assignment_patent_record_new
            FOREIGN KEY(patents_id)
            REFERENCES patents(id) -- Reference the new patents table
            ON DELETE CASCADE,

        CONSTRAINT fk_assignment_classification_definition_new -- Renamed constraint
            FOREIGN KEY(cpc_ipc_id)
            REFERENCES patents_cpc_ipc_definitions(id)
            ON DELETE CASCADE
    );

    COMMENT ON TABLE patents_cpc_ipc_assignments IS 'Stores CPC and IPC classification assignments for patents, linking patents to patents_cpc_ipc_definitions.';
    COMMENT ON COLUMN patents_cpc_ipc_assignments.patents_id IS 'References the id in the patents table.';
    COMMENT ON COLUMN patents_cpc_ipc_assignments.cpc_ipc_id IS 'References the id in the patents_cpc_ipc_definitions table.';
    """)

    if not table_exists:
        print(f"✅ Table '{table_name}' created successfully.")

def create_new_patents_uspc_assignments_table(cursor):
    """
    Create patents_uspc_assignments table referencing the new patents table.
    """
    table_name = "patents_uspc_assignments"
    print(f"Checking if table '{table_name}' exists...")
    cursor.execute(f"SELECT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = '{table_name}');")
    table_exists = cursor.fetchone()[0]

    if table_exists:
        print(f"ℹ️ Table '{table_name}' already exists.")
    else:
        print(f"✨ Creating table '{table_name}'...")

    cursor.execute("""
    CREATE TABLE IF NOT EXISTS patents_uspc_assignments (
        patents_id UUID NOT NULL, -- Foreign key to patents.id
        uspc_id UUID NOT NULL, -- Foreign key to patents_uspc_definitions.id
        type TEXT NOT NULL, -- 'main' or 'extra'

        PRIMARY KEY (patents_id, uspc_id, type), -- Ensures each patent-USPC-type link is unique

        CONSTRAINT fk_uspc_assignment_patent_record_new
            FOREIGN KEY(patents_id)
            REFERENCES patents(id) -- Reference the new patents table
            ON DELETE CASCADE,

        CONSTRAINT fk_uspc_assignment_uspc_definition_new -- Renamed constraint
            FOREIGN KEY(uspc_id)
            REFERENCES patents_uspc_definitions(id)
            ON DELETE CASCADE
    );

    COMMENT ON TABLE patents_uspc_assignments IS 'Stores USPC classification assignments for patents, linking patents to patents_uspc_definitions.';
    COMMENT ON COLUMN patents_uspc_assignments.patents_id IS 'References the id in the patents table.';
    COMMENT ON COLUMN patents_uspc_assignments.uspc_id IS 'References the id in the patents_uspc_definitions table.';
    COMMENT ON COLUMN patents_uspc_assignments.type IS 'Type of USPC assignment: main (primary classification) or extra (further classification).';
    """)

    if not table_exists:
        print(f"✅ Table '{table_name}' created successfully.")
if __name__ == "__main__":
    setup_postgres()
