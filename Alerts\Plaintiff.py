import re
import pandas as pd
import sys
import os
import json
sys.path.append(os.getcwd()) # Add the project root directory to the Python path
from DatabaseManagement.Connections import get_gz_connection
from DatabaseManagement.ImportExport import get_table_from_GZ, insert_and_update_df_to_GZ_batch
from AI.GC_VertexAI import vertex_genai_search
from logdata import log_message
from AI.LLM_shared import get_json

def add_plaintiff_id(cases_df, plaintiff_df):
    connection = get_gz_connection()
    cursor = connection.cursor()

    plaintiff_id_dict = {}
    plaintiff_names_dict = {}
    case_plaintiff_name_map = {} # Dictionary to store determined names

    for index, row in cases_df.iterrows():
        determined_plaintiff_name = None
        # Use approved name if available, otherwise clean from title
        if 'approved_plaintiff_name' in row and pd.notna(row['approved_plaintiff_name']):
            approved_name = row['approved_plaintiff_name']
            # Check if a case-insensitive match exists in plaintiff_df
            existing_plaintiff = plaintiff_df[plaintiff_df['plaintiff_name'].str.lower() == approved_name.lower()]
            if not existing_plaintiff.empty:
                determined_plaintiff_name = existing_plaintiff['plaintiff_name'].values[0] # Use existing name
            else:
                determined_plaintiff_name = approved_name # Use the approved name as is
        else:
            determined_plaintiff_name = clean_plaintiff_name(plaintiff_df, row['title'], row["plaintiff_names"])

        case_plaintiff_name_map[index] = determined_plaintiff_name # Store the name

        if determined_plaintiff_name not in plaintiff_df['plaintiff_name'].values:
            if determined_plaintiff_name == "":
                print(f"Plaintiff name is empty for {row['title']}")
            plaintiff_id_dict[determined_plaintiff_name] = None  # Initialize with None, will be updated with ID later
            # Ensure plaintiff_names are stored even for existing plaintiff names determined via approved_name
            if determined_plaintiff_name not in plaintiff_names_dict:
                 plaintiff_names_dict[determined_plaintiff_name] = row["plaintiff_names"]
        else: # If name already exists, pre-fill the plaintiff_id
            cases_df.loc[index, 'plaintiff_id'] = plaintiff_df.loc[plaintiff_df['plaintiff_name'] == determined_plaintiff_name, 'id'].iloc[0]

    print(f"Number of new unique plaintiffs: {len(plaintiff_id_dict)}")
    print(f"Sorted plaintiff names: {sorted(plaintiff_id_dict.keys())}")


    # Insert plaintiffs and retrieve their IDs
    for plaintiff_name in sorted(plaintiff_id_dict.keys()):
        try:
            overview = get_plaintif_overview_using_list(plaintiff_names_dict[plaintiff_name])
        except Exception as e:
            log_message(f"Error getting overview from AI / Google for {plaintiff_name}: {e}")
            overview = None
        # MySQL version of insert and get ID
        cursor.execute("""
            INSERT INTO tb_plaintiff (plaintiff_name, plaintiff_overview)
            VALUES (%s, %s)""",
            (plaintiff_name, overview)
        )
        plaintiff_id = cursor.lastrowid  # Get the auto-generated ID
        plaintiff_id_dict[plaintiff_name] = plaintiff_id
        log_message(f"Inserted new plaintiff '{plaintiff_name}' into plaintiff table. id: {plaintiff_id}")
        new_row = pd.DataFrame({
            'id': [plaintiff_id],
            'plaintiff_name': [plaintiff_name],
            'plaintiff_overview': [overview],
            'plaintiff_images': [None], 'status': [None], 'creator': [None],
            'create_time': [None], 'updater': [None], 'update_time': [None],
            'deleted': [None], 'tenant_id': [None]
        })
        plaintiff_df = pd.concat([plaintiff_df, new_row], ignore_index=True)


    connection.commit()
    print("Plaintiffs inserted into the database")

    # Update cases_df with plaintiff IDs
    for index, row in cases_df.iterrows():
        # Only update if plaintiff_id is still NaN
        if pd.isna(row['plaintiff_id']):
            cases_df.loc[index, "plaintiff_id"] = plaintiff_id_dict[case_plaintiff_name_map.get(index)]

    cursor.close()
    connection.close()
    print("Cases updated with plaintiff IDs")


def clean_plaintiff_name(plaintiff_df, title, plaintiff_names=[]):
    if isinstance(plaintiff_names, str):
        try:
            plaintiff_names = json.loads(plaintiff_names)
        except json.JSONDecodeError:
            plaintiff_names = [] # Handle potential malformed JSON string

    plaintiff_name = re.split(r'\s+v\.?\s+|\s+vs\.?\s+', title, 1, flags=re.IGNORECASE)[0]  # " v " or " vs ", with . optional

    if len(plaintiff_name) == len(title) and (plaintiff_name[:2].lower() == "v." or plaintiff_name[:2].lower() == "vs" or plaintiff_name[:2].lower() == "v "):
        # Handle cases where split didn't work because defendant starts with v/vs
        plaintiff_name = "abc" # Placeholder, will become "Unidentified Claimant"
    elif len(plaintiff_name) == len(title):
        # Try splitting without space before v/vs
        parts = re.split(r'v\.?\s|vs\.?\s', plaintiff_name, 1, flags=re.IGNORECASE)
        if len(parts) > 1:
             plaintiff_name = parts[0]
        # else: keep original plaintiff_name if split still fails

    if plaintiff_name.lower().startswith(("abc", "xyz", "claimant", "suppressed", "unknown")):
        plaintiff_name = "Unidentified Claimant"

    # Initial clean and acronym removal
    plaintiff_name = remove_acronyms(plaintiff_name)
    plaintiff_name = plaintiff_name.strip()
    plaintiff_name = re.sub(r',\s*$', '', plaintiff_name) # Remove trailing comma and whitespace
    plaintiff_name = plaintiff_name.strip()

    # Define common Chinese last names (lowercase)
    common_chinese_last_names = ["wang", "li", "tan", "xu", "ge", "fu", "zhang", "liang", "zhao", "liu", "chen", "yang", "zhou", "huang", "wu", "tang", "lin", "wan", "shen", "cao", "yu", "sheng"] # Add more if needed

    # Check for short names or common Chinese names and try to find a better match in plaintiff_names
    initial_cleaned_plaintiff = plaintiff_name
    potential_match_found = False

    if len(initial_cleaned_plaintiff) == 2 or initial_cleaned_plaintiff.lower() in common_chinese_last_names:
        # First pass: Check for inclusion
        for p_name_raw in plaintiff_names:
            if not isinstance(p_name_raw, str): continue # Skip non-string entries
            cleaned_p_name = remove_acronyms(p_name_raw).strip()
            if initial_cleaned_plaintiff.lower() in cleaned_p_name.lower():
                plaintiff_name = cleaned_p_name
                potential_match_found = True
                break

        # Second pass: Check for initials (only if length is 2 and no inclusion match found)
        if not potential_match_found and len(initial_cleaned_plaintiff) == 2:
            for p_name_raw in plaintiff_names:
                if not isinstance(p_name_raw, str): continue # Skip non-string entries
                cleaned_p_name = remove_acronyms(p_name_raw).strip()
                words = cleaned_p_name.split()
                # Check if first two words start with the two letters
                if len(words) >= 2 and \
                   words[0].lower().startswith(initial_cleaned_plaintiff[0].lower()) and \
                   words[1].lower().startswith(initial_cleaned_plaintiff[1].lower()):
                    plaintiff_name = cleaned_p_name
                    potential_match_found = True # Mark as found, though we break immediately
                    break

    # Final check against existing plaintiffs in the DataFrame
    existing_plaintiff = plaintiff_df[plaintiff_df['plaintiff_name'].str.lower() == plaintiff_name.lower()]
    if not existing_plaintiff.empty:
        return existing_plaintiff['plaintiff_name'].values[0]

    return plaintiff_name

# Used by clean_plaintiff_name (above) but also when getting artist name and company name from court records or USPTO or USCO
def remove_acronyms(company_name):
    # Define company/entity suffixes
    acronisms = ["llc", "ltd", "corp", "inc", "ag & co", "gmbh & co kg", "ag", "gmbh", "kg", "sa", "pty", "bv", "lp", "sl", "sarl", "sprl", "co", "limited", "et al", "srl", "pte", "spa", "nv"]
    
    # Sort suffixes by length (longest first) to ensure proper matching
    acronisms.sort(key=len, reverse=True)
    
    # Repeatedly remove company suffixes until no more are found
    changed = True
    while changed:
        changed = False
        orig_company_name = company_name
        
        for acronym in acronisms:
            # Handle spaces in acronyms like "ag & co"
            if ' ' in acronym:
                space_pattern = acronym.replace(' ', r'\s+')
                pattern = r',?\s+' + space_pattern + r'\.?(?:\s*,?\s*$)'
                # Apply the pattern
                company_name = re.sub(pattern, '', company_name, flags=re.IGNORECASE)
            else:
                # For regular acronyms like "llc" or "co"
                pattern = r',?\s+' + acronym + r'\.?(?:\s*,?\s*$)'
                
                # Also try with periods between letters (like L.L.C.)
                dotted_pattern = r',?\s+' + r'\.?'.join(acronym) + r'\.?(?:\s*,?\s*$)'
                
                # Try both patterns
                new_name1 = re.sub(pattern, '', company_name, flags=re.IGNORECASE)
                new_name2 = re.sub(dotted_pattern, '', company_name, flags=re.IGNORECASE)
                
                # Use whichever result is shorter (removed more)
                if len(new_name1) <= len(new_name2):
                    company_name = new_name1
                else:
                    company_name = new_name2
        
        # If we made any changes, continue the loop
        if company_name != orig_company_name:
            changed = True

    return company_name



def get_plaintif_overview(plaintiff):
    result = vertex_genai_search([("text", plaintiff)])
    return clean_plaintiff_overview_response(result)


def get_plaintif_overview_using_list(plaintiff_list, plaintiff_brands_list=[], case_summary=""):
    if isinstance(plaintiff_list, str):
        plaintiff_list = json.loads(plaintiff_list)

    if len(plaintiff_list) > 1:
        Plaintiff_also_known_as = plaintiff_list[0] + " can also be known as " + ", ".join(plaintiff_list[1:]) + ". "
    else:
        Plaintiff_also_known_as = plaintiff_list[0]

    prompt = (
       f'Return a json with an overview of what type of company or artist '
       f'"{Plaintiff_also_known_as}" is. The overview should include what '
       f'type of product it sells (or what type of art it produces), what '
       f'market it is active, and any other detail available. I need the '
       f'answer in both English and Chinese in the following format: '
       f'{{"English":"Overview in English", "Chinese": "Overview in Chinese"}}. '
       f'If you don\'t know, just answer {{"English":"Unknown", "Chinese": "Unknown"}}. '
       f'Keep your answer concise and to the point.'
   )
    
    if plaintiff_brands_list:
       prompt += (
           f' It might be helpfull to know that the following brands are associated with this '
           f'company or artist: {", ".join(plaintiff_brands_list)}'
       )
    if case_summary:
        prompt += (
            f' It might be helpfull to know that the company or artist in involved in a lawsuit for'
            f' which the summary is as follows: {case_summary}'
        )
    result = vertex_genai_search([("text", prompt)])

    return clean_plaintiff_overview_response(result)


def clean_plaintiff_overview_response(response):
    if response:
        overview = clean_plaintiff_overview_text(response)
        keywords = ["Unknown", "context", "more details", "additional details", "website", "unfortunately", "information", "please"]
        if any(keyword.lower() in overview.lower() for keyword in keywords):
            return '{"English":"Unknown", "Chinese": "未知"}'
        else:
            return overview
    else:
        return '{"English":"Unknown", "Chinese": "未知"}'
    
def clean_plaintiff_overview_text(json_str):
    try:
        # Look for content between curly braces
        json_answer = get_json(json_str)
        if json_answer and all(key in json_answer.keys() for key in ["English", "Chinese"]):
            return json.dumps(json_answer)
        
        # If no valid JSON found or missing required keys
        return '{"English":"Unknown", "Chinese": "未知"}'
    
    except Exception as e:
        print(f"Error parsing response: {e}")
        return '{"English":"Unknown", "Chinese": "未知"}'
    

def get_plaintiff_brand(cases_df, plaintiff_id):
    plaintiff_cases = cases_df[cases_df["plaintiff_id"] == plaintiff_id]
    plaintiff_brands_list = []
    for _, case in plaintiff_cases.iterrows():
        case_images = case["images"]        
        if isinstance(case_images, str):
            case_images = json.loads(case_images)

        if case_images is not None:
            if "trademarks" in case_images:
                for trademark_image in case_images['trademarks'].keys():
                    if "trademark_text" in case_images['trademarks'][trademark_image]:
                        if case_images['trademarks'][trademark_image]['trademark_text'][0] != "":
                            plaintiff_brands_list.append(case_images['trademarks'][trademark_image]['trademark_text'][0])

    # Remove duplicates where duplicates are defined by having the first 7 letters the same in small caps
    seen = set()
    unique_brands = []
    for name in plaintiff_brands_list:
        if name not in seen:
            unique_brands.append(name)
            seen.add(name)

    return unique_brands



def get_plaintiff_names(cases_df, id):
    plaintiff_cases = cases_df[cases_df["plaintiff_id"] == id]
    plaintiff_names_list = []
    for _, case in plaintiff_cases.iterrows():
        plaintiff_names = json.loads(case["plaintiff_names"])
        plaintiff_names_list.extend(plaintiff_names)
    # Remove duplicates where duplicates are defined by having the first 7 letters the same in small caps
    seen = set()
    unique_names = []
    for name in plaintiff_names_list:
        key = name[:7].lower()
        if key not in seen:
            unique_names.append(name)
            seen.add(key)

    return unique_names

def get_all_plaintiff_overview(force=False):
    plaintiff_df = get_table_from_GZ("tb_plaintiff")
    cases_df = get_table_from_GZ("tb_case")
    plaintiff_df = plaintiff_df[plaintiff_df["plaintiff_name"].str.contains("Dpg Usa")]
    get_plaintiff_overview(cases_df, plaintiff_df, force)


def get_plaintiff_overview(cases_df, plaintiff_df, force=False):
    log_message(f"Getting plaintiff overview for {len(plaintiff_df)} plaintiffs")
    ai_call_count = 0

    for index, row in plaintiff_df.iterrows():
        # if row["id"] < 1600:
        #     continue
        if row["plaintiff_overview"] == "Unknown":
            plaintiff_df.loc[index, "plaintiff_overview"] = None

        # If there is already an overview, skip
        if row["plaintiff_overview"] is not None and not force:
            continue

        # Disregard cases with ID = 9 (ABC, XYZ, etc....)
        title = cases_df[cases_df["plaintiff_id"] == row["id"]]["title"].iloc[0]
        if title.lower()[:3] == "abc" or title.lower()[:3] == "xyz" or title.lower()[:8] == "claimant" or title.lower()[:10] == "suppressed" or title.lower() == "unknown":
    
            plaintiff_df.loc[index, "plaintiff_overview"] = '{"English":"Unknown", "Chinese": "未知"}'
            continue

        # Get all the names of all the plaintiffs with the LLC, LTD, etc. as these are good info for the LLM
        plaintiff_names_list = get_plaintiff_names(cases_df, row["id"])
        plaintiff_brands_list = get_plaintiff_brand(cases_df, row["id"])
        case_summary = cases_df[cases_df["plaintiff_id"] == row["id"]]["aisummary"].iloc[-1]
        if len(plaintiff_names_list) > 0:  # and pd.isna(row["plaintiff_overview"])
            plaintiff_df.loc[index, "plaintiff_overview"] = get_plaintif_overview_using_list(plaintiff_names_list, plaintiff_brands_list=plaintiff_brands_list, case_summary=case_summary)
            print(f"Plaintiff '{plaintiff_names_list[0]}' overview: {plaintiff_df.loc[index, 'plaintiff_overview']}")
            ai_call_count += 1
        
    log_message(f"    Done: {ai_call_count} AI calls")
    log_message(f"Plaintiff overview now going into the database")
    insert_and_update_df_to_GZ_batch(plaintiff_df, "tb_plaintiff", "id")


if __name__ == "__main__":
    # get_all_plaintiff_overview()
    # get_all_plaintiff_images()
    # cases_df = get_table_from_US("CASES")
    # cases_df = cases_df[cases_df["plaintiff_id"].isin([1499, 1500, 1519, 1520, 1521])]
    # cases_df["plaintiff_id"] = None
    
    # plaintiff_df = get_table_from_US("PLAINTIFF")
    # cases_df, plaintiff_df = add_plaintiff_id(cases_df, plaintiff_df)
    # insert_and_update_df_to_US_batch(cases_df, "CASES", "id", "docket")
    get_all_plaintiff_overview(force=True)

    # print(search_google_uc('AAT, LLC, Anti-Age Technologies, LLC'), 5)