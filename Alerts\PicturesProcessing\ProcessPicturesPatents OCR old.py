import os
import sys
sys.path.append(os.getcwd())
import cv2
import os
import fitz  # PyMuPDF
from logdata import log_message
from .ProcessPicturesShared import find_complaint_pdf_path
from IP.Patent import get_patent_data_by_reg_no, get_patent_data_by_name
from multiprocessing import Pool
from Alerts.PicturesProcessing.OCRProcessor import OCRProcessor
import time
import asyncio
from AI.GC_VertexAI import vertex_genai_multi
from AI.LLM_shared import get_json, get_json_list, get_list
from AI.GC_VertexAI import vertex_genai_multi_async

pattent_pattern = r'US\sD?[\d]+(?:\s*,\s*[\d]+)*(?:\s*S)?(?:\s*B\d)?'

async def process_patent_exhibit(df, index, case_directory, pdf_file_path):
    prompt = """I am looking for the patent registration number(s) in this legal filing. Please help me find it (them).
    The patent registration number is on the 1st page of the patent, and in the format of US D? xxx,xxx S? Bx? usually with 6 to 8 numbers
    You return your answer as a list of registration numbers: [reg_no1, reg_no2, ...], where is reg_no is a unique Patent.
    Only include the unique patent registration number of each patent (i.e. do not include any references to other patents). It is very well possible that there is only one patent.
    """
    prompt_list = [("text", prompt), ("pdf_path", pdf_file_path)]
    ai_answer = vertex_genai_multi(prompt_list, model_name="gemini-2.0-flash-thinking-exp-01-21")
    patents_identified = get_list(ai_answer)

    if not patents_identified:
        print(f"\033[91m!!!!! NO PATENT REGISTRATION NUMBER FOUND: ai_answer = {ai_answer}\033[0m")
        return False
    else: 
        plaintiff_id = df.at[index, "plaintiff_id"]
        updated_patent_list = await get_patent_data_by_reg_no(patents_identified, plaintiff_id)
        updated_patent_list = await add_design_page_numbers(updated_patent_list)
        success = process_patents_pdfs_from_uspto(df, index, case_directory, updated_patent_list)
        return success

async def get_and_process_patents_from_uspto_using_regnos(df, index, case_directory, patents_identified_list):
    # Get patent using registration numbers: download patents, extract images, and update dataframe.
    # Get patent data asynchronously
    start_time = time.time()
    plaintiff_id = df.at[index, "plaintiff_id"]
    updated_patent_list = await get_patent_data_by_reg_no(patents_identified_list, plaintiff_id)
    print(f"\033[91m 🔥 USPTO Patent API time: {len(updated_patent_list)} in {format(time.time() - start_time, '.1f')} seconds\033[0m")
    
    # Process patents using the common helper function
    updated_patent_list = await add_design_page_numbers(updated_patent_list)
    success = process_patents_pdfs_from_uspto(df, index, case_directory, updated_patent_list)
    return success

async def get_and_process_patents_from_uspto_using_plaintiff_name(df, index, case_directory, plaintiff_df):
    # Get patents associated with a plaintiff: download patents, extract images, and update dataframe.

    # Get plaintiff name and id from the dataframe
    plaintiff_id = df.at[index, "plaintiff_id"]
    plaintiff_name = plaintiff_df["plaintiff_name"].loc[plaintiff_df["id"] == plaintiff_id].values[0]

    # Find complaint pdf path
    complaint_pdf_path = find_complaint_pdf_path(case_directory)
    for folder in os.listdir(case_directory): 
        for pdf_filename in os.listdir(os.path.join(case_directory, folder)): 
            if pdf_filename.endswith(".pdf") and ("complaint" in pdf_filename.lower() or ("sheet" not in pdf_filename.lower() and "exhibit" not in pdf_filename.lower() and "schedule" not in pdf_filename.lower())):
                complaint_pdf_path = os.path.join(case_directory, folder, pdf_filename)
                break
    
    # Get patent data asynchronously
    start_time = time.time()
    patent_list = await get_patent_data_by_name(plaintiff_name, nb_results=50, plaintiff_id=plaintiff_id)
    print(f"\033[91m 🔥 USPTO Patent API time: {len(patent_list)} in {format(time.time() - start_time, '.1f')} seconds\033[0m")

    relevant_patent_list = []
    prompt = "Based on the complaint filed with the court, you need to identify the most relevant patents that are related to the plaintiff's claim. The plaintiff is: " + plaintiff_name + ". This is the complaint filed with the court: \n\n" 
    prompt_list = [("text", prompt), ("pdf_path", complaint_pdf_path)]
    
    prompt = "\n\nHere is an overview of the patents: \n\n"
    for i, patent in enumerate(patent_list):
        prompt += f"Patent {i+1}: DocumentID: {patent['document_id']}, Title: {patent['text']}, Assignee: {patent['assignee']}, Applicant: {patent['applicant']}, Inventors: {patent['inventors']}, Abstract: {patent['abstract']}\n\n"

    prompt += f'Based on the above information, taking in consideration the name of the plaintiff ({plaintiff_name}), the title of the patents, and the abstract, you need to identify the most relevant patents (max 20) that are related to the plaintiff claim. You provide the DocumentID of the patents that you think are most relevant in a list: ["US-XXXXX", "US-XXXXX", "US-XXXXX"]. \n\n'
    prompt_list.append(("text", prompt))
    ai_answer = vertex_genai_multi(prompt_list, model_name="gemini-2.0-flash-thinking-exp-01-21")
    relevant_patent_document_id_list = get_list(ai_answer)
    relevant_patent_list = [patent for patent in patent_list if patent['document_id'] in relevant_patent_document_id_list]
    
    # Process patents using the common helper function
    relevant_patent_list = await add_design_page_numbers(relevant_patent_list)
    success = process_patents_pdfs_from_uspto(df, index, case_directory, relevant_patent_list)
    return success


async def add_design_page_numbers(patent_list):
    async def process_patent(patent):
        pdf_file_path = os.path.join(os.getcwd(), "Documents", "IP", "Patents", f"{patent['document_id']}.pdf")
        prompt = """I am looking for the list of pages with the Designs / Figures / Sheets in this US patent. Please help me find them.
        The Pages with the Designs / Figures / Sheets are usually the ones with the Fig. or Sheet. written on them, big pictures and little text.
        You return your answer as a list of page numbers: [3, 4, 5, ...]
        """
        prompt_list = [("text", prompt), ("pdf_path", pdf_file_path)]
        ai_answer = await vertex_genai_multi_async(prompt_list, model_name="gemini-2.0-flash-thinking-exp-01-21")
        design_page_numbers = get_list(ai_answer)
        patent["design_page_numbers"] = [page_number for page_number in design_page_numbers if page_number != 1 and page_number != "1"]
        return patent

    # Process all patents in parallel directly with asyncio.gather
    tasks = [process_patent(patent) for patent in patent_list]
    updated_patents = await asyncio.gather(*tasks)
    return updated_patents

def process_patents_pdfs_from_uspto(df, index, case_directory, patent_list):
    # Common helper function to process patents recevied from the USPTO API: extract images and update dataframe.
    success = False
    images_dir = os.path.join(case_directory, "images")
    os.makedirs(images_dir, exist_ok=True)
    
    # Prepare batch of image processing tasks
    save_pdf_page_tasks = []
    
    # Collect all image processing tasks first
    for patent in patent_list:
        # Extract images (certificate and design pages) from each page of a PDF, intelligently identifying design pages.
        pdf_path = os.path.join(os.getcwd(), "Documents", "IP", "Patents", f"{patent['document_id']}.pdf")

        certificate_filename = None
        
        # Open the PDF document
        pdf_document = fitz.open(pdf_path, filetype="pdf")

        # Extract the certificate (first page)
        if pdf_document.page_count > 0:
            # Save the certificate image (currently a jpg) to webp
            certificate_filename = f"{patent['document_id']}_full.webp"
            certificate_path = os.path.join(images_dir, certificate_filename)            
            # Add certificate to batch processing list
            save_pdf_page_tasks.append((pdf_path, 1, certificate_path, [cv2.IMWRITE_WEBP_QUALITY, 80]))
        
        # Extract only design pages (not all pages after the first one)
        for page_num in patent['design_page_numbers']:
            if isinstance(page_num, str):
                page_num = int(page_num)
            page_filename = f"{patent['document_id']}_page{page_num}.webp"
            page_path = os.path.join(images_dir, page_filename)
            
            # Add page to batch processing list
            save_pdf_page_tasks.append((pdf_path, page_num, page_path, [cv2.IMWRITE_WEBP_QUALITY, 80]))
            
            # Keep track of which patent this page belongs to
            df.at[index, 'images']['patents'][page_filename] = {
                'product_name': patent.get("text"),
                'applicant': patent.get("inventors"),
                'patent_number': patent.get("document_id"),
                'full_filename': certificate_filename
            }
            success = True
        
        pdf_document.close()
    
    # Process all images in parallel
    if save_pdf_page_tasks:
        log_message(f"Processing {len(save_pdf_page_tasks)} patent images in parallel")
        OCRProcessor.save_pdf_pages_batch(save_pdf_page_tasks)
    
    print(f"✅ process_patents_pdfs_from_uspto: Processed {len(patent_list)} patents with {len(save_pdf_page_tasks)} images")
    
    return success





# def process_patents_pdfs_from_uspto(df, index, case_directory, patent_list):
#     # Common helper function to process patents recevied from the USPTO API: extract images and update dataframe.
#     success = False

#     images_dir = os.path.join(case_directory, "images")
#     os.makedirs(images_dir, exist_ok=True)
    
#     # Process each patent
#     for patent in patent_list:
#         if patent["files"]:
#             # Extract images from the PDF
#             certificate_filename, page_images = extract_images_from_pdf(patent, images_dir)
            
#             # Update the dataframe
#             if certificate_filename and page_images:
#                 # For each page image, add an entry to the dataframe
#                 for page_image in page_images:
#                     df.at[index, 'images']['patents'][page_image] = {
#                         'product_name': patent.get("text"),
#                         'applicant': patent.get("inventors"),
#                         'patent_number': patent.get("document_id"),
#                         'full_filename': certificate_filename
#                     }
                
#                 success = True
#                 print(f"✅ Processed patent: {patent.get('reg_no')}")
#             else:
#                 print(f"\033[91m 🔥 Failed to extract images for patent: {patent.get('reg_no')}\033[0m")
#         else:
#             print(f"\033[91m 🔥 Failed to download patent: {patent.get('reg_no')}\033[0m")

#     return success



# def extract_images_from_pdf(patent_data, output_dir):
#     # Extract images (certificate and design pages) from each page of a PDF, intelligently identifying design pages.
#     # Returns: tuple: (certificate_filename, list of page image filenames)

#     pdf_path = os.path.join(os.getcwd(), "Documents", "IP", "Patents", f"{patent_data['document_id']}.pdf")

#     certificate_filename = None
    
#     # Open the PDF document
#     pdf_document = fitz.open(pdf_path, filetype="pdf")

#     # Extract the certificate (first page)
#     if pdf_document.page_count > 0:
#         # Save the certificate image (currently a jpg) to webp
#         certificate_filename = f"{patent_data['document_id']}_full.webp"
#         certificate_path = os.path.join(output_dir, certificate_filename)
#         certificate_img  = convert_page_number_to_image(pdf_document, 1)
#         cv2.imwrite(certificate_path, certificate_img, [cv2.IMWRITE_WEBP_QUALITY, 80])
    
#     # Extract only design pages (not all pages after the first one)
#     design_page_images = []
#     for page_num in patent_data['design_page_numbers']:
#         page_img = convert_page_number_to_image(pdf_document, page_num)
#         page_filename = f"{patent_data['document_id']}_page{page_num}.webp"
#         page_path = os.path.join(output_dir, page_filename)
#         cv2.imwrite(page_path, page_img, [cv2.IMWRITE_WEBP_QUALITY, 80])
#         design_page_images.append(page_filename)
    
#     pdf_document.close()
    
#     return certificate_filename, design_page_images


# def process_patent_images_using_ocr(df, index, case_images_directory, case_directory_1, pdf_document, pdf_file, pages_text, pages_ocr_data, page_images):
#     design_images = []
#     pdf_file_no_ext = os.path.splitext(pdf_file)[0]
#     folder_path = os.path.join(case_directory_1, pdf_file_no_ext)
#     os.makedirs(folder_path, exist_ok=True)

#     # 1. OCR approach
#     similar_images_in_pdf = []

#     for page_number in range(pdf_document.page_count):
#         if any(all(keyword.lower() in pages_text[page_number+1].lower() for keyword in keywordset) for keywordset in keywords["Patent"]):
#             similar_images_in_pdf.append((os.path.join(folder_path, page_images[page_number+1]), page_number+1))

#     # 2. Get the disign images
#     if similar_images_in_pdf:
#         log_message(f"        - Extracting patent information from images: {len(similar_images_in_pdf)} images")
#         all_pages = [page.number for page in pdf_document]
#         last_page_number = max(all_pages)+1 # Start count from 0. But need to align with the file names which start from 1

#         # 1. A whole patent is present, and it is in pdf as an image
#         # patent_page_numbers = [int(patent_image.split("page")[1].split("_")[0]) for patent_image in similar_images_in_pdf]
#         patent_page_numbers = [patent_image[1] for patent_image in similar_images_in_pdf]
#         for patent_image_path, patent_page_number in similar_images_in_pdf: # 61997 has 2 patents
#             # patent_page_number = int(patent_image_name.split("page")[1].split("_")[0])
#             get_all_design_images_for_patent(pdf_document, pdf_file_no_ext, patent_page_number, patent_page_numbers, last_page_number, folder_path, patent_image_path, design_images, pages_text)

#         process_design_images(df, index, case_images_directory, pages_text, pages_ocr_data, design_images)
        
#         return True
    
#     return False
                    
# def find_patent_in_complaint(df, index, case_images_directory, case_directory_1, pdf_ocr_data):
#     # A patent is not present, we will try to get images from the complaint: find the text "Patent Number", get all the pictures from that page  (e.g. )
#     design_images = []
#     complaint_patent_page_numbers = []
#     for pdf_filename in os.listdir(case_directory_1): 
#         if pdf_filename.endswith(".pdf") and ("complaint" in pdf_filename.lower() or ("exhibit" not in pdf_filename.lower() and "schedule" not in pdf_filename.lower())):
#             folder_path = os.path.join(case_directory_1, os.path.splitext(pdf_filename)[0])
#             if os.path.exists(folder_path):
#                 shutil.rmtree(folder_path)
#             os.makedirs(folder_path, exist_ok=True)
#             pdf_file_path = os.path.join(case_directory_1, pdf_filename)
#             pdf_document = fitz.open(pdf_file_path)
#             for page_num in range(len(pdf_document)):
#                 page = pdf_document.load_page(page_num)
#                 text = page.get_text("text")
#                 if "Patent Number" in text:
#                     complaint_patent_page_numbers.append(page_num+1)
#             pdf_document.close()
    
#             if len(complaint_patent_page_numbers) > 0:
#                 extract_images_from_a_pdf(case_directory_1, pdf_filename)
#                 if os.path.exists(folder_path):
#                     complaint_patent_page_numbers = sorted(list(set(complaint_patent_page_numbers)))
#                     for design_page_number in complaint_patent_page_numbers:
#                         design_image_filenames = [f for f in os.listdir(folder_path) if f.split("page")[1].split("_")[0] == str(design_page_number)]
#                         for design_image_filename in design_image_filenames:
#                             img = cv2.imread(os.path.join(folder_path, design_image_filename))
#                             if img.shape[1] < 1.5 * img.shape[0]: # width is not greater than 1.5 times the height
#                                 design_images.append((os.path.join(folder_path, design_image_filename), None))

#                 if pdf_file_path not in pdf_ocr_data:
#                     with fitz.open(pdf_file_path) as pdf_document:
#                         full_text, pages_text_json, pages_ocr_data, page_images = OCRProcessor.process_pdf(pdf_document, range(pdf_document.page_count))
#                     pdf_ocr_data[pdf_file_path] = [pages_ocr_data, pages_text_json, page_images]
#                 else: 
#                     pages_ocr_data, pages_text_json, page_images  = pdf_ocr_data[pdf_file_path]
#                 process_design_images(df, index, case_images_directory, pages_text_json, pages_ocr_data, design_images)
#                 return True
#     return False


# def process_design_images(df, index, case_images_directory, pages_text, pages_ocr_data, design_images):
#     # Process the results
#     design_images = sorted(list(set(design_images)))
#     if len(design_images) != 0:
#         field_values = {}
#         patent_image_paths = sorted(list(set([patent_image_path for design_image_path, patent_image_path in design_images if patent_image_path is not None])))
        
#         for patent_image_path in patent_image_paths: # 61997 has 2 patents
#             imgFull = cv2.imread(patent_image_path)
#             cv2.imwrite(os.path.join(case_images_directory, os.path.splitext(os.path.basename(patent_image_path))[0] + "_full.webp"), imgFull, [cv2.IMWRITE_WEBP_QUALITY, 80])
#             field_values[patent_image_path] = {}
#             field_values[patent_image_path]['product_name'] = get_product_name(patent_image_path, pages_text, pages_ocr_data)
#             field_values[patent_image_path]['applicant'] = get_field(patent_image_path, pages_text, "Applicant", r"\n|\)|D\d")
#             field_values[patent_image_path]['assignee'] = get_field(patent_image_path, pages_text, "Assignee", r"\n|\)|D\d")
#             field_values[patent_image_path]['patent_number'] = get_patent_number_using_ocr(patent_image_path, pages_text)

#         for design_image_path, patent_image_path in design_images:
#             img = cv2.imread(os.path.join(design_image_path))
#             cropped_image, (x_offset, y_offset) = crop_white_space(img, min_area=0)
#             design_image_filename = f"{os.path.splitext(os.path.basename(design_image_path))[0]}.webp"
#             cv2.imwrite(os.path.join(case_images_directory, design_image_filename), cropped_image, [cv2.IMWRITE_WEBP_QUALITY, 80])
#             if patent_image_path: 
#                 df.at[index, 'images']['patents'][design_image_filename] = {**field_values[patent_image_path], 'full_filename': [os.path.splitext(os.path.basename(patent_image_path))[0] + "_full.webp"]}
#             else:
#                 df.at[index, 'images']['patents'][design_image_filename] = {'product_name': None, 'applicant': None, 'assignee': None, 'patent_number': None, 'full_filename': None}


# def get_patent_number_using_ocr(patent_image_path, pages_text):
#     patent_page_number = int(patent_image_path.split("page")[1].split("_")[0])
#     patent_text = pages_text[patent_page_number]
#     pattern = r'US\sD?[\d]+(?:\s*,\s*[\d]+)*(?:\s*S)?(?:\s*B\d)?'
#     matches = re.findall(pattern, patent_text)
#     if len(matches) > 0:
#         # Remove spaces except the one after US and before B\d
#         patent_number = matches[0].replace(" ", "").replace("US", "US ").replace("B", " B")
#         print(f"Patent number: {patent_number}")
#         return patent_number
#     else:
#         return None

# def get_product_name_old(patent_image_path, pages_text):
#     product_name = get_field(patent_image_path, pages_text, "(54)", r"\n|\(|D\d")
#     if product_name is None:
#         patent_page_number = int(patent_image_path.split("page")[1].split("_")[0])
#         patent_text = pages_text[patent_page_number]
        
#         # Find the first word in all capital letters, allowing for spaces between capitalized words
#         words = re.findall(r'\b[A-Z]+(?:\s+[A-Z]+)*\b', patent_text)  # Match words that are entirely in uppercase, allowing spaces
#         words = [word for word in words if word not in ["CLAIM", "OTHER PUBLICATION", "DESCRIPTION", "LSS TAT"]]
#         words = [word for word in words if len(word) > 4]
#         product_name = words[0] if words else None  # Return the longest word or None if not found
#     return product_name


# def get_product_name(patent_image_path, pages_text, pages_ocr_data):
#     patent_page_number = int(patent_image_path.split("page")[1].split("_")[0])
#     patent_text = pages_text[patent_page_number]
#     patent_ocr_data = pages_ocr_data[patent_page_number]

#     pattern54 = r"\(54\)"
#     target_text = None

#     for i in range(len(patent_ocr_data['text'])):
#         if re.search(pattern54, patent_ocr_data['text'][i]):
#             target_left = patent_ocr_data['left'][i] + patent_ocr_data['width'][i]
#             target_top = patent_ocr_data['top'][i]

#             # Iterate to find the next non-empty block
#             min_distance = float('inf')
#             for j in range(len(patent_ocr_data['text'])):
#                 if i != j:
#                     if abs(patent_ocr_data['top'][j] - target_top) < patent_ocr_data['height'][j] and patent_ocr_data['left'][j] >= target_left:
#                         if patent_ocr_data['text'][j].strip():  # Check if non-empty
#                             distance = patent_ocr_data['left'][j] - target_left
#                             if distance < min_distance:
#                                 min_distance = distance
#                                 target_text = patent_ocr_data['text'][j]
#                                 break
#             break

#     product_name = None
#     if target_text is not None:
#         start_index = patent_text.find(target_text)
#         if start_index != -1:
#             # Extract the text after the first word
#             remaining_text = patent_text[start_index:]
#             escaped_target_text = re.escape(target_text)
#             pattern = rf"\b{escaped_target_text}(?:\s+[A-Z]+)*\b"
#             words = re.findall(pattern, remaining_text)  
#             product_name = words[0].replace("\n", " ").strip() if words else None

#     print(f"Product name: {product_name}")
#     return product_name



# def get_field(patent_image_path, pages_text, start_text, end_text):
#     patent_page_number = int(patent_image_path.split("page")[1].split("_")[0])
#     patent_text = pages_text[patent_page_number]
#     if start_text in patent_text:
#         field_start = patent_text.index(start_text)+len(start_text)
#         field = patent_text[field_start:].strip()
#         if field[0] == "\n": #\n is a single character
#             field = field[1:].strip()
#         if field[0] == ".": 
#             field = field[1:].strip()
#         if field[0] == ":": 
#             field = field[1:].strip()
        
#         match = re.search(end_text, field)
#         field_end = match.start() if match else len(field)
#         field = field[:field_end].strip()
#         if len(field) == 0:
#             field = None
#         elif field[-1] == ")" or field[-1] == ".": 
#             field = field[:-1].strip()

#         return field
#     else: 
#         return None



# def get_all_design_images_for_patent(patent_pdf, pdf_file_no_ext, patent_page_number, patent_page_numbers, last_page_number, folder_path, patent_image_path, design_images, pages_text):
#     next_patent_page_number = patent_page_numbers[patent_page_numbers.index(patent_page_number) + 1] if patent_page_numbers.index(patent_page_number) + 1 < len(patent_page_numbers) else last_page_number+1
#     for design_page_number in range(patent_page_number + 1, next_patent_page_number):
#         design_page_text = pages_text[design_page_number].lower()
#         if len(design_page_text) < 1000 and (("sheet" in design_page_text and not "cover sheet" in design_page_text) or "fig." in design_page_text):
#             design_image_filenames = [f for f in os.listdir(folder_path) if os.path.isfile(os.path.join(folder_path, f)) and f.split("page")[1].split("_")[0] == str(design_page_number)]
#             if len(design_image_filenames) != 1:
#                 print(f"Multiple design images found for page {design_page_number} in {patent_image_path}")
#                 design_image_filenames = [f"{pdf_file_no_ext}_page{design_page_number}_0.jpg"]
#                 save_page_number_to_image(patent_pdf, design_page_number, os.path.join(folder_path, design_image_filenames[0]))
                
#             design_images.append((os.path.join(folder_path, design_image_filenames[0]), patent_image_path))




# def extract_images_from_pdf(patent_data, output_dir):
#     # Extract images (certificate and design pages) from each page of a PDF, intelligently identifying design pages.
#     # Returns: tuple: (certificate_filename, list of page image filenames)

#     pdf_path = os.path.join(os.getcwd(), "Documents", "IP", "Patents", f"{patent_data['document_id']}.pdf")

#     certificate_filename = None
    
#     # Open the PDF document
#     pdf_document = fitz.open(pdf_path, filetype="pdf")
#     full_text, pages_text_json, page_ocr_data, page_images = OCRProcessor.process_pdf(pdf_document, range(pdf_document.page_count))
    
#     # Extract the certificate (first page)
#     if pdf_document.page_count > 0:
#         # Save the certificate image (currently a jpg) to webp
#         certificate_filename = f"{patent_data['document_id']}_full.webp"
#         certificate_path = os.path.join(output_dir, certificate_filename)
#         certificate_img = cv2.imread(page_images[1])
#         cv2.imwrite(certificate_path, certificate_img, [cv2.IMWRITE_WEBP_QUALITY, 80])
    
#     # Extract only design pages (not all pages after the first one)
#     design_page_images = []
#     for page_num in range(2, pdf_document.page_count + 1):
#         # Get page text to analyze if it's a design page
#         page_text = pages_text_json[page_num].lower()
        
#         # Check if this is a design page (similar logic to get_all_design_images_for_patent)
#         if len(page_text) < 1000 and (("sheet" in page_text and "cover sheet" not in page_text) or "fig." in page_text or "figure" in page_text or "drawing" in page_text ):
#             # This is likely a design/figure page => save image as webp
#             page_img = cv2.imread(page_images[page_num-1])
#             page_filename = f"{patent_data['document_id']}_page{page_num}.webp"
#             page_path = os.path.join(output_dir, page_filename)
#             cv2.imwrite(page_path, page_img, [cv2.IMWRITE_WEBP_QUALITY, 80])
#             design_page_images.append(page_filename)
    
#     pdf_document.close()
    
#     return certificate_filename, design_page_images



# def get_and_process_patents_from_uspto_using_regnos(df, index, case_images_directory, pdfs_to_consider, pdf_ocr_data, reg_nos):
#     # Get patent using registration numbers: download patents, extract images, and update dataframe.
    
#     patent_list = []
#     for reg_no in reg_nos:
#         # Find if reg_no is in the pdf_ocr_data
#         found = False
#         page_number = None
        
#         for pdf_file_path in pdfs_to_consider:
#             page_ocr_data, pages_text_json, page_images = pdf_ocr_data[pdf_file_path]
#             for pg_num, page_text in pages_text_json.items():
#                 if reg_no in page_text:
#                     found = True
#                     page_number = pg_num
#                     break
#             if found:
#                 break
                
#         if found:
#             filename_prefix = os.path.splitext(os.path.basename(pdf_file_path))[0] + "_page" + str(page_number) + "_patentno_" + sanitize_name(reg_no)
#         else:
#             filename_prefix = sanitize_name(df["docket"].loc[index]) + "_patentno_" + sanitize_name(reg_no)
            
#         patent_list.append({"reg_no": reg_no, "filename_prefix": filename_prefix})
    
#     # Get patent data asynchronously
#     start_time = time.time()
#     updated_patent_list = asyncio.run(get_patent_data_by_reg_no(patent_list))
#     print(f"\033[91m 🔥 USPTO Patent API time: {len(updated_patent_list)} in {format(time.time() - start_time, '.1f')} seconds\033[0m")
    
#     # Process patents using the common helper function
#     process_patents_from_uspto(df, index, case_images_directory, updated_patent_list)


