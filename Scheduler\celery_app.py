# To run celery on windows for testing: 
# wsl -d Ubuntu
# start redis: redis-server
# in a console start the worker: python -m celery -A Scheduler.celery_app worker --pool=solo --loglevel=info
# in another console start beat (the sceduler), from the Scheduler folder: python -m celery -A celery_app beat --loglevel=info

import os
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from celery import Celery
from celery.schedules import crontab
from celery.signals import after_setup_logger
import logging

celery = Celery(
    'Scheduler',
    broker='redis://localhost:6379/0',
    include=['Scheduler.tasks']
)

# More explicit worker configurations
celery.conf.update(
    worker_pool='prefork',
    worker_concurrency=1,
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=50,
    daemon_process_inheritance_fix=True,
    worker_force_execv=True,  # Add this
)

# Load the config directly
celery.conf.beat_schedule = {
    'task-email-report-schedule': {
        'task': 'Scheduler.tasks.task_test',
        'schedule': crontab(hour=9, minute=56),
        'options': {'expires': 3600} # expires after 1 hour
    },
    'task-weekly-patent-grants': {
        'task': 'Scheduler.tasks.task_run_weekly_patent_grants',
        'schedule': crontab(day_of_week='tuesday', hour=21, minute=00), # 9:00 PM Tuesday
        'options': {'expires': 7200} # expires after 2 hours
    },
}
 
celery.conf.timezone = 'America/New_York'
 
@after_setup_logger.connect
def setup_loggers(logger, *args, **kwargs):
    # Create a file handler
    fh = logging.FileHandler('logs/celery_detailed.log')
    fh.setLevel(logging.INFO)
 
    # Create a formatter
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    fh.setFormatter(formatter)
 
    # Add the handler to the logger
    logger.addHandler(fh)