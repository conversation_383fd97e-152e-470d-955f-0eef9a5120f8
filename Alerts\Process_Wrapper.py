import logging
import time
import multiprocessing
from logdata import log_message, create_new_run
from email_sender import send_email_report
import pytz
from datetime import datetime

# This File is used to wrap the Get_Alerts and Update_Cases processes into a separate process (spawned)
# The process wrapper are called from the API in app.py

def configure_logging_for_process(run_id):
    # Configure logging for this specific process
    logger = logging.getLogger()
    
    # Create a unique file handler for this process
    log_file = f"process_{run_id}.log"
    file_handler = logging.FileHandler(log_file)
    
    # Optional: Create a specific format for process logs
    formatter = logging.Formatter(
        f'Process {run_id} - %(asctime)s - %(levelname)s - %(message)s'
    )
    file_handler.setFormatter(formatter)
    
    logger.addHandler(file_handler)
    return file_handler  # Return for cleanup

def cleanup_process_resources(file_handler=None):
    # Close any open file handlers
    if file_handler:
        file_handler.close()
        logging.getLogger().removeHand<PERSON>(file_handler)


def monitor_process(process, run_id):
    """Monitor process status and update database accordingly"""
    max_wait = 30  # seconds to wait for process to start
    start_time = time.time()
    
    while time.time() - start_time < max_wait:
        if process.is_alive():
            print(f"Process {process.pid} is running for run_id {run_id}")
            return True
        time.sleep(0.5)
    
    print(f"Process for run_id {run_id} failed to start within {max_wait:.1f} seconds")
    return False


def process_wrapper_get_alerts(run_id, selected_date, loop_back_days, force_redo):
    file_handler = None
    try:
        multiprocessing.set_start_method('spawn', force=True)
        
        # Set up logging
        file_handler = configure_logging_for_process(run_id)
        
        start_time = time.time()
        logging.info(f"Starting process {run_id}")
        
        # Run the actual process
        from Alerts.Get_Alerts import get_alerts
        get_alerts(run_id, selected_date, loop_back_days, force_redo)
        
        duration = time.time() - start_time
        logging.info(f"Process {run_id} completed successfully in {duration:.2f} seconds")
        send_email_report(run_id)
        
    except Exception as e:
        log_message(f"Process {run_id} failed: {str(e)}", level='ERROR')
        raise
        
    finally:
        cleanup_process_resources(file_handler)
        pass

def run_fetch_task(selected_date=None, loop_back_days=None, force_redo=False):
    """Generic function that can be called both by scheduler and API endpoint"""
    try:
        print(f"Running task with selected_date: {selected_date} and loop_back_days: {loop_back_days}")
        # If no date provided, use NY timezone's current date
        if selected_date is None:
            selected_date = (datetime.now(pytz.timezone('America/New_York'))).strftime('%Y-%m-%d')
            loop_back_days = 1

        run_id = create_new_run()
        print(f"Main process creating new process for run_id {run_id}")
        
        process = multiprocessing.Process(target=process_wrapper_get_alerts, args=(run_id, selected_date, loop_back_days, force_redo))
        process.start()

        # Monitor process and return status
        if monitor_process(process, run_id):
            return {'run_id': run_id, 'process_id': process.pid, 'status': 'started'}
        else:
            return {'run_id': run_id, 'status': 'failed_to_start'}
            
    except Exception as e:
        print(f"Error in run_task: {str(e)}")
        return {'error': str(e)}
    


def process_wrapper_update_all_subscribed_cases(run_id, threshold_days):
    # print("Inside process_wrapper_update_all_subscribed_cases: Modules loaded:", sys.modules.keys())
    file_handler = None
    try:
        multiprocessing.set_start_method('spawn', force=True)
        
        # Set up logging
        file_handler = configure_logging_for_process(run_id)
        
        start_time = time.time()
        logging.info(f"Starting update process {run_id}")
        
        # Run the actual process
        from Alerts.Update_Cases import update_all_subscribed_cases
        update_all_subscribed_cases(run_id, threshold_days)
        
        duration = time.time() - start_time
        logging.info(f"Update process {run_id} completed successfully in {duration:.2f} seconds")
        send_email_report(run_id)
        
    except Exception as e:
        log_message(f"Update process {run_id} failed: {str(e)}", level='ERROR')
        raise
        
    finally:
        cleanup_process_resources(file_handler)

def run_update_task(threshold_days=2):
    """Similar to run_task but for updating subscribed cases"""
    try:
        run_id = create_new_run()
        print(f"Main process creating new process for update task run_id {run_id}, at {datetime.now()}")
        
        process = multiprocessing.Process(target=process_wrapper_update_all_subscribed_cases, args=(run_id, threshold_days))
        process.start()

        # Monitor process and return status
        if monitor_process(process, run_id):
            return {'run_id': run_id, 'process_id': process.pid, 'status': 'started'}
        else:
            return {'run_id': run_id, 'status': 'failed_to_start'}
            
    except Exception as e:
        print(f"Error in run_update_task: {str(e)}")
        return {'error': str(e)}
    


def process_wrapper_fix_cases_no_ip(run_id):
    # print("Inside process_wrapper_update_all_subscribed_cases: Modules loaded:", sys.modules.keys())
    file_handler = None
    try:
        multiprocessing.set_start_method('spawn', force=True)
        
        # Set up logging
        file_handler = configure_logging_for_process(run_id)
        
        start_time = time.time()
        logging.info(f"Starting update process {run_id}")
        
        # Run the actual process
        from Alerts.Fix_Cases import fix_cases_no_ip
        fix_cases_no_ip(run_id)
        
        duration = time.time() - start_time
        logging.info(f"Update process {run_id} completed successfully in {duration:.2f} seconds")
        send_email_report(run_id)
        
    except Exception as e:
        log_message(f"Update process {run_id} failed: {str(e)}", level='ERROR')
        raise
        
    finally:
        cleanup_process_resources(file_handler)

def run_fix_task():
    """Similar to run_task but for updating subscribed cases"""
    try:
        run_id = create_new_run()
        print(f"Main process creating new process for update task run_id {run_id}, at {datetime.now()}")
        
        process = multiprocessing.Process(target=process_wrapper_fix_cases_no_ip, args=(run_id))
        process.start()

        # Monitor process and return status
        if monitor_process(process, run_id):
            return {'run_id': run_id, 'process_id': process.pid, 'status': 'started'}
        else:
            return {'run_id': run_id, 'status': 'failed_to_start'}
            
    except Exception as e:
        print(f"Error in run_update_task: {str(e)}")
        return {'error': str(e)}
    








