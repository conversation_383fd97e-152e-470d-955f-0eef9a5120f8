import mysql.connector
import time
from datetime import datetime
import sys
import socket
import logging
import subprocess
import os

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('mysql_monitor_detailed.log'),
        logging.StreamHandler()
    ]
)

def check_tcp_connection(host, port):
    try:
        # Create socket with shorter timeout
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        
        # Record start time for SYN
        syn_start = time.time()
        result = sock.connect_ex((host, port))
        connect_time = time.time() - syn_start
        
        if result == 0:
            # Get socket details
            local_addr = sock.getsockname()
            remote_addr = sock.getpeername()
            logging.info(f"TCP Connection Details:")
            logging.info(f"  Local Address: {local_addr}")
            logging.info(f"  Remote Address: {remote_addr}")
            logging.info(f"  Connect Time: {connect_time:.3f}s")
            
            # Get TCP info if possible
            try:
                tcp_info = sock.getsockopt(socket.IPPROTO_TCP, socket.TCP_INFO)
                logging.info(f"  TCP Info: {tcp_info}")
            except:
                pass
            
        sock.close()
        return result, connect_time
    except Exception as e:
        logging.error(f"TCP connection check error: {e}")
        return -1, 0

def test_connection(host, user, password, database, port=3306):
    start_time = time.time()
    
    # First test TCP
    tcp_result, tcp_time = check_tcp_connection(host, port)
    if tcp_result != 0:
        logging.error(f"TCP connection failed with error code {tcp_result}")
        
        # Try ping when TCP fails
        try:
            ping_output = subprocess.check_output(
                ['ping', '-c', '1', '-W', '5', host], 
                stderr=subprocess.STDOUT,
                universal_newlines=True
            )
            logging.info(f"Ping result during TCP failure:\n{ping_output.strip()}")
        except subprocess.CalledProcessError as e:
            logging.error(f"Ping failed during TCP failure: {e.output.strip()}")
            
        return False, time.time() - start_time

    try:
        # Then try MySQL connection
        conn = mysql.connector.connect(
            host=host,
            user=user,
            password=password,
            database=database,
            connect_timeout=10
        )
        
        cursor = conn.cursor()
        cursor.execute("SELECT @@version")
        version = cursor.fetchone()
        cursor.close()
        conn.close()
        
        elapsed = time.time() - start_time
        logging.info(f"Connection successful - MySQL Version: {version[0]}")
        logging.info(f"Times - TCP: {tcp_time:.3f}s, Total: {elapsed:.3f}s")
        return True, elapsed
    
    except mysql.connector.Error as err:
        elapsed = time.time() - start_time
        logging.error(f"MySQL Error: {err}")
        logging.error(f"Times - TCP: {tcp_time:.3f}s, Total: {elapsed:.3f}s")
        return False, elapsed
    except Exception as err:
        elapsed = time.time() - start_time
        logging.error(f"Unexpected error: {err}")
        logging.error(f"Times - TCP: {tcp_time:.3f}s, Total: {elapsed:.3f}s")
        return False, elapsed

def main():
    HOST = os.getenv("MYSQL_HOST")
    DATABASE = os.getenv("MYSQL_DATABASE")
    USER = os.getenv("MYSQL_USER")
    PASSWORD = os.getenv("MYSQL_PASSWORD")
    
    total_attempts = 0
    failed_attempts = 0
    total_time = 0
    consecutive_failures = 0
    
    try:
        while True:
            total_attempts += 1
            success, elapsed = test_connection(HOST, USER, PASSWORD, DATABASE)
            total_time += elapsed
            
            if not success:
                failed_attempts += 1
                consecutive_failures += 1
                if consecutive_failures >= 3:
                    logging.warning(f"Three consecutive failures detected!")
                    # Try traceroute on consecutive failures
                    try:
                        traceroute = subprocess.check_output(
                            ['traceroute', '-n', HOST],
                            stderr=subprocess.STDOUT,
                            universal_newlines=True
                        )
                        logging.info(f"Traceroute during failures:\n{traceroute.strip()}")
                    except subprocess.CalledProcessError as e:
                        logging.error(f"Traceroute failed: {e.output.strip()}")
            else:
                consecutive_failures = 0
            
            if total_attempts % 10 == 0:
                success_rate = ((total_attempts - failed_attempts) / total_attempts) * 100
                avg_time = total_time / total_attempts
                logging.info(f"Statistics after {total_attempts} attempts:")
                logging.info(f"Success rate: {success_rate:.1f}%")
                logging.info(f"Average connection time: {avg_time:.3f}s")
            
            time.sleep(5)
            
    except KeyboardInterrupt:
        logging.info("\nMonitoring stopped by user")
        if total_attempts > 0:
            success_rate = ((total_attempts - failed_attempts) / total_attempts) * 100
            avg_time = total_time / total_attempts
            logging.info(f"\nFinal Statistics:")
            logging.info(f"Total attempts: {total_attempts}")
            logging.info(f"Failed attempts: {failed_attempts}")
            logging.info(f"Success rate: {success_rate:.1f}%")
            logging.info(f"Average connection time: {avg_time:.3f}s")

if __name__ == "__main__":
    main()