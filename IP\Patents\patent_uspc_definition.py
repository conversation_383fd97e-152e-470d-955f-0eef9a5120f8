import requests
from bs4 import BeautifulSoup
import csv
import time
import os
import logging
import re

# --- Configuration ---
BASE_URL_MAIN_CLASS_TEMPLATE = "https://www.uspto.gov/web/patents/classification/uspcd{XX}/schedd{XX}.htm"
BASE_URL_DEFINITION_TEMPLATE = "https://www.uspto.gov/web/patents/classification/shadowFiles/defsD{XX}sf.htm?D{XX}_{REF_CODE}"
OUTPUT_CSV_FILE = "uspc_definitions.csv"
# Class Numbers: 01 through 34, and 99
CLASS_NUMBERS_TO_SCRAPE = list(range(1, 35)) + [99]
REQUEST_DELAY_SECONDS = 0 # Delay between requests to be polite to the server
MAX_RETRIES = 3
RETRY_DELAY_SECONDS = 5

# Setup logging
log_dir = os.path.join(os.path.dirname(__file__), 'logs')
os.makedirs(log_dir, exist_ok=True)
log_file_path = os.path.join(log_dir, 'patent_uspc_definition.log')

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(module)s - %(funcName)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file_path),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# --- Helper Functions ---

def format_class_number(class_num):
    """Zero-pads class numbers less than 10."""
    return f"{class_num:02d}"

def fetch_html(url, retries=MAX_RETRIES, delay=RETRY_DELAY_SECONDS):
    """Fetches HTML content from a URL with retries."""
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    for attempt in range(retries):
        try:
            logger.debug(f"Fetching URL (Attempt {attempt + 1}/{retries}): {url}")
            response = requests.get(url, headers=headers, timeout=30)
            response.raise_for_status()  # Raises an HTTPError for bad responses (4XX or 5XX)
            logger.debug(f"Successfully fetched {url}")
            return response.text
        except requests.exceptions.Timeout:
            logger.warning(f"Timeout occurred for {url} on attempt {attempt + 1}.")
        except requests.exceptions.HTTPError as e:
            logger.warning(f"HTTP error {e.response.status_code} for {url} on attempt {attempt + 1}.")
        except requests.exceptions.RequestException as e:
            logger.warning(f"Request exception for {url} on attempt {attempt + 1}: {e}")

        if attempt < retries - 1:
            logger.info(f"Retrying in {delay} seconds...")
            time.sleep(delay)
        else:
            logger.error(f"Failed to fetch {url} after {retries} attempts.")
            return None

def extract_reference_code(js_pt_call_str):
    """Extracts the reference code from a javascript:pt('...') string."""
    match = re.search(r"javascript:pt\('([^']+?)'\)", js_pt_call_str)
    if match:
        return match.group(1)
    logger.warning(f"Could not extract reference code from: {js_pt_call_str}")
    return None

def parse_main_class_page(html_content, class_num_str_padded):
    """
    Parses the main class page to extract subclass codes, titles, and definition reference codes.
    Returns a list of dictionaries: [{'subclass_code_text': '...', 'reference_code': '...'}]
    """
    soup = BeautifulSoup(html_content, 'html.parser')
    subclasses_data = []

    # The structure seems to be rows (<tr>) with data cells (<td>)
    # Subclass info is often in a cell with class 'Sub1', 'Sub2', etc. or in the first few cells.
    # The definition link is an <a> tag with href like javascript:pt('...')
    for row in soup.find_all('tr'):
        cells = row.find_all('td')
        if not cells or len(cells) < 2: # Need at least a cell for subclass and one for potential link
            continue

        subclass_code_text = None
        reference_code = None

        # 1. Extract Subclass Code and Title
        # Iterate through the first few cells to find the subclass info
        # The design manual states: "Locate the element containing the subclass code and its descriptive text/title.
        # This is typically an <a> tag. If an <a> tag is not present in the expected location for the subclass code,
        # find the <b> tag in that area and extract its text content."
        # The cell often has a class like 'Sub1', 'Sub2', etc.
        
        potential_subclass_cell = None
        # Check cells for class 'SubX'
        for cell_idx, cell in enumerate(cells):
            cell_classes = cell.get('class', [])
            if any(c.lower().startswith('sub') for c in cell_classes):
                potential_subclass_cell = cell
                break

        if potential_subclass_cell:
            a_tag = potential_subclass_cell.find('a')
            if a_tag and a_tag.text.strip():
                subclass_code_text = a_tag.text.strip()
            else:
                b_tag = potential_subclass_cell.find('b')
                if b_tag and b_tag.text.strip():
                    subclass_code_text = b_tag.text.strip()
        
        if not subclass_code_text:
            continue # Skip if no subclass text found

        # 2. Extract Definition Link Reference Code
        # "Locate the <a> tag containing the javascript:pt('...') in its href attribute.
        # Based on example_main_page.txt, this is often the second <a> tag in the relevant row
        # that has a javascript: href."
        # We will search all <a> tags in the row for the specific href pattern.
        
        # Search for the specific javascript:pt link across all cells in the row
        # Search for the reference code, focusing on cells with class 'SubTtl'
        for cell in cells:
            cell_classes = cell.get('class', [])
            if 'SubTtl' in cell_classes:
                # This cell has the SubTtl class.
                # First, try to find a javascript:pt link within this SubTtl cell.
                js_links = cell.find_all('a', href=lambda href: href and 'javascript:pt(' in href.lower())
                if js_links:
                    ref_code_candidate = extract_reference_code(js_links[0]['href'])
                    if ref_code_candidate:
                        reference_code = ref_code_candidate
                        logger.debug(f"Found reference_code via javascript:pt link in SubTtl cell: {reference_code}")
                        break  # Found reference_code, exit loop for this row
                else:
                    # No javascript:pt link found in this SubTtl cell, so extract its text.
                    sub_ttl_text = cell.get_text(strip=True)
                    if sub_ttl_text:
                        reference_code = sub_ttl_text
                        logger.debug(f"Found reference_code via text content in SubTtl cell: {reference_code}")
                        break  # Found reference_code, exit loop for this row
            # If reference_code is found from a SubTtl cell, the break above will exit this loop.
            # If the cell is not 'SubTtl', we continue to the next cell in the row.

        if subclass_code_text and reference_code:
            subclasses_data.append({'subclass_code_text': subclass_code_text, 'reference_code': reference_code})

    if not subclasses_data:
        logger.warning(f"No subclasses found on main page for D{class_num_str_padded}")
    return subclasses_data

def parse_definition_page(html_content, reference_code): # Added reference_code argument
    """
    Parses the subclass definition page to extract the definition text.
    Uses reference_code to identify specific definition divs.
    """
    soup = BeautifulSoup(html_content, 'html.parser')
    definition_parts = []
    
    # Extract target div base IDs from reference_code
    target_base_ids = []
    if reference_code:
        main_ref_part = reference_code.split('#')[0] # Remove URL fragment if present
        ref_components = main_ref_part.split('&')
        # Components for div IDs usually start from the 3rd element (index 2)
        # e.g., D01_102&S&2&3 -> components are ['D01_102', 'S', '2', '3']
        # We are interested in '2', '3', etc.
        if len(ref_components) > 2:
            for comp in ref_components[2:]:
                # Component should be non-empty and not 'S' (schedule view marker)
                # It can be alphanumeric (e.g., '2', 'A', '10B')
                if comp and comp.strip() and comp.upper() != 'S':
                    target_base_ids.append(comp.strip())
    
    if target_base_ids:
        logger.debug(f"Targeting definition div base IDs: {target_base_ids} from reference_code: {reference_code}")
        for base_id in target_base_ids:
            # The actual definition divs have IDs like "2*", "A*", etc.
            definition_div = soup.find('div', id=base_id)
            
            if definition_div:
                logger.debug(f"Found definition div: {base_id}")
                current_div_texts = []
                font_tags = definition_div.find_all('font')
                if font_tags:
                    for font_tag in font_tags[1:]:
                        text = font_tag.get_text(strip=True)
                        text = re.sub(r'[^a-zA-Z ;]', '', text) # Sanitize text
                        if text: # Check if text is non-empty after sanitization
                            current_div_texts.append(text)
                            # Decide if we should break or collect all font tag texts
                            # For now, let's collect all sanitized, non-empty font texts from this div
                
                if not current_div_texts: # If no font tags or they yielded no text, get all text from the div
                    div_text = definition_div.get_text(strip=True)
                    div_text = re.sub(r'[^a-zA-Z ;]', '', div_text) # Sanitize text
                    if div_text: # Check if text is non-empty after sanitization
                        logger.debug(f"No <font> tags in {base_id} or they were empty after sanitization, using sanitized all text from div: '{div_text[:50]}...'")
                        current_div_texts.append(div_text)
                
                if current_div_texts:
                    definition_parts.extend(current_div_texts)
                # else:
                #    logger.warning(f"No text extracted from div with id='{target_div_id_with_star}' for reference_code='{reference_code}'.")
            else:
                logger.warning(f"Expected definition div with id='{base_id}' not found for reference_code='{reference_code}'.")
    else:
        logger.info(f"Could not derive target div IDs from reference_code: '{reference_code}'. Proceeding to fallback search.")

    # Fallback if no parts found via reference_code or if reference_code didn't yield target IDs
    if not definition_parts:
        logger.info(f"No definition parts found using reference_code '{reference_code}'. Falling back to searching all 'div.switchcontent[id$=\"*\"]'.")
        # Fallback: find all divs with class 'switchcontent' and ID ending with '*'
        fallback_divs = soup.find_all('div', class_='switchcontent', id=lambda x: x and x.endswith('*'))
        
        if fallback_divs:
            logger.debug(f"Found {len(fallback_divs)} fallback divs (class 'switchcontent', id ending with '*')")
            for div in fallback_divs:
                current_div_texts = []
                font_tags = div.find_all('font')
                if font_tags:
                    for font_tag in font_tags:
                        text = font_tag.get_text(strip=True)
                        text = re.sub(r'[^a-zA-Z ;]', '', text) # Sanitize text
                        if text: # Check if text is non-empty after sanitization
                            current_div_texts.append(text)
                if not current_div_texts: # Fallback to whole div text if no font tags or they are empty after sanitization
                    div_text = div.get_text(strip=True)
                    div_text = re.sub(r'[^a-zA-Z ;]', '', div_text) # Sanitize text
                    if div_text: # Check if text is non-empty after sanitization
                        current_div_texts.append(div_text)
                
                if current_div_texts:
                    definition_parts.extend(current_div_texts)
        else:
            logger.warning("Fallback search for 'div.switchcontent[id$=\"*\"]' yielded no divs.")

    if not definition_parts:
        logger.warning(f"No definition text extracted for reference_code='{reference_code}' even after fallbacks. Returning empty.")
        return ""

    # Remove duplicates while preserving order and join
    unique_definition_parts = list(dict.fromkeys(definition_parts))
    return "; ".join(unique_definition_parts)

def isolate_subclass_code(subclass_code_text):
    """
    Isolates the subclass code (e.g., "1", "23.5", "101A") from the full text (e.g., "101 A specific thing").
    Assumes code is the first part, possibly followed by a space.
    """
    if not subclass_code_text:
        return ""
    # Regex to capture leading numbers, possibly with decimals or letters, before a space or end of string.
    # This handles cases like "1", "1.1", "100A", "DIG.1"
    match = re.match(r'^([A-Z0-9\.]+)', subclass_code_text)
    if match:
        return match.group(1)
    logger.warning(f"Could not isolate subclass code from: '{subclass_code_text}'. Using full text.")
    return subclass_code_text # Fallback to full text if no clear code part

# Import the upsert function from patent_db_uspc
# Assuming patent_db_uspc.py is in the same directory or accessible via Python path
try:
    from .patent_db_uspc import upsert_uspc_definitions
except ImportError:
    # Fallback if running as a script and .patent_db_uspc is not found directly
    # This might happen if the script is run from a different working directory
    # or if IP.Patents is not a package recognized in that context.
    logger.warning("Could not import upsert_uspc_definitions with relative import. Trying absolute.")
    try:
        from IP.Patents.patent_db_uspc import upsert_uspc_definitions
    except ImportError as e:
        logger.error(f"Failed to import upsert_uspc_definitions: {e}. Database upsert will not be available.")
        upsert_uspc_definitions = None

def manage_uspc_definition_data():
    """
    Orchestrates the USPC definition data workflow:
    1. Checks if the USPC definitions CSV file exists.
    2. Runs the main_scraper if the CSV is not found.
    3. Reads the CSV data.
    4. Upserts the data to the patents_uspc_definition database table.
    """
    logger.info("Starting USPC definition data management process.")
    
    # Construct the full path to the output CSV file
    # OUTPUT_CSV_FILE is defined globally in this script
    csv_file_path = os.path.join(os.path.dirname(__file__), OUTPUT_CSV_FILE)
    
    if not os.path.exists(csv_file_path):
        logger.info(f"'{OUTPUT_CSV_FILE}' not found at '{csv_file_path}'. Running main_scraper to generate it.")
        main_scraper() # main_scraper writes the CSV
        # After scraping, check again if the file was created
        if not os.path.exists(csv_file_path):
            logger.error(f"main_scraper ran but '{OUTPUT_CSV_FILE}' was still not found. Aborting database upsert.")
            return
    else:
        logger.info(f"Found existing '{OUTPUT_CSV_FILE}' at '{csv_file_path}'.")

    # Read data from CSV
    uspc_data_to_upsert = []
    try:
        with open(csv_file_path, mode='r', encoding='utf-8', newline='') as csvfile:
            reader = csv.DictReader(csvfile)
            # Ensure fieldnames match what upsert_uspc_definitions expects ('class', 'subclass', 'definition')
            if not all(field in reader.fieldnames for field in ['class', 'subclass', 'definition']):
                logger.error(f"CSV file '{csv_file_path}' is missing one of the required columns: 'class', 'subclass', 'definition'.")
                return

            for row in reader:
                uspc_data_to_upsert.append({
                    'class': row.get('class'),
                    'subclass': row.get('subclass'),
                    'definition': row.get('definition')
                })
        logger.info(f"Successfully read {len(uspc_data_to_upsert)} records from '{csv_file_path}'.")
    except FileNotFoundError:
        logger.error(f"Error: The CSV file '{csv_file_path}' was not found after attempting to read it (should have been checked or created).")
        return
    except Exception as e:
        logger.error(f"Error reading CSV file '{csv_file_path}': {e}")
        return

    if not uspc_data_to_upsert:
        logger.info("No data read from CSV to upsert into the database.")
        return

    # Upsert data to database
    if upsert_uspc_definitions: # Check if import was successful
        logger.info("Attempting to upsert USPC definitions to the database...")
        try:
            processed_db_count = upsert_uspc_definitions(uspc_data_to_upsert)
            logger.info(f"Database upsert process for USPC definitions finished. Processed/Attempted: {processed_db_count} records.")
        except Exception as e:
            logger.error(f"An error occurred during the database upsert operation: {e}", exc_info=True)
    else:
        logger.warning("upsert_uspc_definitions function not available. Skipping database upsert.")
        
    logger.info("USPC definition data management process finished.")

def main_scraper():
    """Main function to orchestrate the scraping process."""
    logger.info("Starting USPTO Classification Scraper.")
    all_scraped_data = []
    processed_main_classes = 0

    for class_num in CLASS_NUMBERS_TO_SCRAPE:
        class_num_padded = format_class_number(class_num)
        main_class_id_for_csv = f"D{class_num_padded}" # As per "class: D01, D02..."
        logger.info(f"--- Processing Main Class: {main_class_id_for_csv} ---")

        main_page_url = BASE_URL_MAIN_CLASS_TEMPLATE.format(XX=class_num_padded)
        main_page_html = fetch_html(main_page_url)

        if not main_page_html:
            logger.error(f"Skipping main class D{class_num_padded} due to fetch failure.")
            continue
        
        processed_main_classes += 1
        subclasses_on_page = parse_main_class_page(main_page_html, class_num_padded)

        if not subclasses_on_page:
            logger.warning(f"No subclass information extracted for main class D{class_num_padded}.")
            continue

        logger.info(f"Found {len(subclasses_on_page)} potential subclasses/links for D{class_num_padded}.")

        for i, subclass_info in enumerate(subclasses_on_page):
            subclass_code_full_text = subclass_info.get('subclass_code_text')
            reference_code = subclass_info.get('reference_code')

            if not subclass_code_full_text or not reference_code:
                logger.warning(f"Missing subclass text or reference code for an entry in D{class_num_padded}. Skipping.")
                continue

            actual_subclass_code = isolate_subclass_code(subclass_code_full_text)
            logger.info(f"Processing D{class_num_padded} - Subclass {actual_subclass_code} ({i+1}/{len(subclasses_on_page)}): '{subclass_code_full_text}' with ref '{reference_code}'")

            definition_text = "" # Initialize definition_text
            if '&' in reference_code: # Only fetch definition if '&' is in reference_code
                logger.debug(f"Reference code '{reference_code}' contains '&', attempting to fetch definition.")
                definition_page_url = BASE_URL_DEFINITION_TEMPLATE.format(XX=class_num_padded, REF_CODE=reference_code)
                definition_page_html = fetch_html(definition_page_url)

                if definition_page_html:
                    definition_text = parse_definition_page(definition_page_html, reference_code) # Pass reference_code
                    if not definition_text:
                         logger.warning(f"Empty definition extracted for D{class_num_padded}, Subclass {actual_subclass_code} (ref: {reference_code}) from {definition_page_url}")
                else:
                    logger.error(f"Failed to fetch definition page for D{class_num_padded}, Subclass {actual_subclass_code} (ref: {reference_code}), URL: {definition_page_url if 'definition_page_url' in locals() else 'not formed'}")
            else:
                definition_text = reference_code
                logger.debug(f"Reference code '{reference_code}' does not contain '&', skipping definition fetch.")
            
            all_scraped_data.append({
                'class': main_class_id_for_csv,
                'subclass': actual_subclass_code,
                'definition': definition_text.strip()
            })
            
            logger.debug(f"Sleeping for {REQUEST_DELAY_SECONDS}s before next definition request.")
            time.sleep(REQUEST_DELAY_SECONDS) # Be polite

    logger.info(f"--- Scraping Finished. Processed {processed_main_classes} main classes. Total records: {len(all_scraped_data)} ---")

    # Write to CSV
    if all_scraped_data:
        output_file_full_path = os.path.join(os.path.dirname(__file__), OUTPUT_CSV_FILE)
        try:
            with open(output_file_full_path, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['class', 'subclass', 'definition']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(all_scraped_data)
            logger.info(f"Successfully wrote {len(all_scraped_data)} records to {output_file_full_path}")
        except IOError as e:
            logger.error(f"Could not write to CSV file {output_file_full_path}: {e}")
    else:
        logger.info("No data was scraped, CSV file not created.")

    logger.info("USPTO Classification Scraper finished.")

if __name__ == "__main__":
    manage_uspc_definition_data()