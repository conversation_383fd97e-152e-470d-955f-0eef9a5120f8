# Import functions from Plaintiff_Clean_Up
from Fixes.Plaintiff_Clean_Up import (
    delete_plaintiff_without_cases,
    merge_plaintiff_pair,
    fix_duplicate_names_auto,
    get_duplicate_plaintiff_names
)
import pandas as pd
import numpy as np # Import numpy
# Import cache manager module and refresh function
import cache_manager
from flask import request, url_for, jsonify
import json
from logdata import log_message # Import log_message
# Import necessary functions for overview update
from Alerts.Plaintiff import get_plaintif_overview_using_list, get_plaintiff_names
from DatabaseManagement.ImportExport import insert_and_update_df_to_GZ_batch

def init_plaintiffs_routes(app):
    """Register all plaintiff management routes with the Flask app"""

    @app.route('/get_plaintiffs', methods=['GET'])
    def get_plaintiffs():
        """Get plaintiffs data with filters applied"""
        log_message("Request received for /get_plaintiffs", level="DEBUG")
        # Get filter parameters
        sort_by = request.args.get('sort_by', 'id') # Default sort by ID
        sort_order = request.args.get('sort_order', 'asc') # Default sort ascending
        search_term = request.args.get('search_term', '').strip() # Remove leading/trailing whitespace
        force_refresh_param = request.args.get('force_refresh', 'false').lower() == 'true' # Get and convert force_refresh param

        try: # Single top-level try for the whole route
            # Ensure cache is loaded using the imported function, respecting force_refresh
            if not cache_manager.refresh_cached_data(force=force_refresh_param): # Check return value
                 log_message("Error: Cache not loaded in /get_plaintiffs", level="ERROR")
                 return jsonify({"error": "Data cache not available"}), 500

            # Check cache variables directly using cache_manager prefix
            if cache_manager.cached_plaintiff_df is None or cache_manager.cached_cases_df is None:
                log_message("Error: Required cached dataframes are None in /get_plaintiffs", level="ERROR")
                return jsonify({"error": "Data cache unavailable"}), 500

            # Apply search filter
            if search_term:
                df_plaintiffs_filtered = cache_manager.cached_plaintiff_df[cache_manager.cached_plaintiff_df['plaintiff_name'].str.contains(search_term, case=False, na=False)]
            else:
                df_plaintiffs_filtered = cache_manager.cached_plaintiff_df

            # Pre-calculate case counts for efficiency
            case_counts = cache_manager.cached_cases_df['plaintiff_id'].value_counts()
            nos_counts = cache_manager.cached_cases_df[cache_manager.cached_cases_df['nos_description'].notna() & (cache_manager.cached_cases_df['nos_description'] != '')]['plaintiff_id'].value_counts()

            # Calculate case counts and format results
            results = []
            for _, row in df_plaintiffs_filtered.iterrows():
                try: # Add try-except for robustness
                    plaintiff_id = int(row['id'])
                except (ValueError, TypeError):
                    log_message(f"Skipping plaintiff due to invalid ID: {row.get('id')}", level="WARNING")
                    continue

                total_cases_count = case_counts.get(plaintiff_id, 0)
                cases_with_nos_count = nos_counts.get(plaintiff_id, 0)
                cases_without_nos_count = total_cases_count - cases_with_nos_count
                # Extract English overview if available
                plaintiff_overview_full_data = None # Initialize to None
                plaintiff_overview_raw = row.get('plaintiff_overview') # Use .get for safety
                if pd.notna(plaintiff_overview_raw) and plaintiff_overview_raw:
                    try:
                        # Try parsing as JSON first
                        plaintiff_overview_full_data = json.loads(plaintiff_overview_raw) if isinstance(plaintiff_overview_raw, str) else plaintiff_overview_raw
                        # Ensure it's a dictionary with expected keys, otherwise create a default structure
                        if not isinstance(plaintiff_overview_full_data, dict) or 'English' not in plaintiff_overview_full_data or 'Chinese' not in plaintiff_overview_full_data:
                             overview_text = str(plaintiff_overview_full_data) # Use string representation if not a valid dict
                             plaintiff_overview_full_data = {'English': overview_text, 'Chinese': ''} # Create a basic dict
                    except (json.JSONDecodeError, TypeError) as e: # Catch specific errors
                        log_message(f"Error decoding plaintiff_overview for ID {plaintiff_id}: {e}. Data: {plaintiff_overview_raw}", level="WARNING")
                        # Handle cases where the format might be different or plain text
                        overview_text = str(plaintiff_overview_raw) # Fallback to string representation
                        plaintiff_overview_full_data = {'English': overview_text, 'Chinese': ''} # Create a basic dict
                else:
                     # If overview is null/empty, provide a default structure
                     plaintiff_overview_full_data = {'English': '', 'Chinese': ''}

                results.append({
                    'id': int(plaintiff_id),
                    'plaintiff_name': row.get('plaintiff_name', ''), # Handle potential None
                    'plaintiff_overview_data': plaintiff_overview_full_data, # Full data for frontend JS
                    'cases_count': f"{cases_with_nos_count} / {cases_without_nos_count}",
                    'total_cases': int(total_cases_count) # Convert numpy int64 to standard python int
                })

            # Sort results
            reverse_order = (sort_order.lower() == 'desc')
            if sort_by == 'id':
                results.sort(key=lambda x: x['id'], reverse=reverse_order)
            elif sort_by == 'plaintiff_name':
                results.sort(key=lambda x: (x['plaintiff_name'] or "").lower(), reverse=reverse_order) # Case-insensitive, handle None
            elif sort_by == 'total_cases':
                results.sort(key=lambda x: x['total_cases'], reverse=reverse_order)

            log_message(f"Returning {len(results)} plaintiffs for /get_plaintiffs", level="INFO")
            return jsonify({'plaintiffs': results})

        except Exception as e: # Single except block for the whole route
            log_message(f"Error in /get_plaintiffs: {e}", level="ERROR")
            # Optionally log traceback here
            import traceback
            traceback.print_exc()
            return jsonify({"error": "An internal error occurred"}), 500


    @app.route('/run_plaintiff_action', methods=['POST'])
    def run_plaintiff_action():
        """Run a plaintiff cleanup or update action"""
        action = request.json.get('action')
        log_message(f"Received request for plaintiff action: {action}", level="INFO")

        result = {'status': 'error', 'message': 'Unknown action'}

        try:
            # Force refresh before action
            if not cache_manager.refresh_cached_data():
                log_message("Error: Cache refresh failed in /run_plaintiff_action", level="ERROR")
                return jsonify({"error": "Data cache not available"}), 500

            # Check cache again after refresh attempt using cache_manager prefix
            if cache_manager.cached_cases_df is None or cache_manager.cached_plaintiff_df is None:
                log_message("Error: Cache still None after refresh in /run_plaintiff_action", level="ERROR")
                return jsonify({"error": "Data cache unavailable"}), 500

            if action == 'delete_without_cases':
                # Pass the currently cached dataframes directly using cache_manager prefix
                # delete_without_cases should ideally modify these DFs in place or return updated ones
                deleted_count = delete_plaintiff_without_cases(
                    cache_manager.cached_cases_df,
                    cache_manager.cached_plaintiff_df,
                    perform_delete=True
                )
                result = {'status': 'success', 'message': f'Successfully deleted {deleted_count} plaintiffs without cases'}
                log_message(f"Action '{action}' completed. {deleted_count} plaintiffs deleted.", level="INFO")
                # No extra refresh needed here, delete_plaintiff_without_cases should manage its state or the next request will refresh

            elif action == 'fix_auto':
                fix_duplicate_names_auto(cache_manager.cached_cases_df, cache_manager.cached_plaintiff_df)
                result = {'status': 'success', 'message': 'Successfully merged duplicate plaintiff names automatically'}
            elif action == 'fix_manual':
                # Redirect to duplicate detection page
                return jsonify({'status': 'redirect', 'url': url_for('plaintiff_duplicates')})
            

            else:
                 result = {'status': 'error', 'message': f'Unknown or deprecated action: {action}'}
                 log_message(f"Unknown or deprecated action requested: {action}", level="WARNING")

        except Exception as e:
            log_message(f"Error executing action '{action}': {e}", level="ERROR")
            # Optionally log traceback
            import traceback
            traceback.print_exc()
            result = {'status': 'error', 'message': f'Error executing action {action}: {str(e)}'}

        return jsonify(result)
    
    @app.route('/get_duplicate_plaintiffs')
    def get_duplicate_plaintiffs():
        """Get potential duplicate plaintiffs"""
        duplicate_pairs = get_duplicate_plaintiff_names(cache_manager.cached_plaintiff_df, cache_manager.cached_cases_df)
        
        return jsonify({'duplicates': duplicate_pairs})

    @app.route('/resolve_duplicate_pair', methods=['POST'])
    def resolve_duplicate_pair_api():
        """Merge or reject a pair of plaintiffs based on user decision."""
        data = request.get_json() # Get data once
        id1 = data.get('id1')
        id2 = data.get('id2')
        resolution_type = data.get('resolution_type') # 'merge_to_1', 'merge_to_2', 'reject'
        log_message(f"Received request to resolve pair ({id1}, {id2}) with action: {resolution_type}", level="INFO")

        if not all([id1, id2, resolution_type]):
             log_message("Missing parameters for /resolve_duplicate_pair", level="WARNING")
             return jsonify({'status': 'error', 'message': 'Missing required parameters (id1, id2, resolution_type)'}), 400

        try:
             id1_int = int(id1)
             id2_int = int(id2)
        except ValueError:
             log_message(f"Invalid ID format received: id1={id1}, id2={id2}", level="WARNING")
             return jsonify({'status': 'error', 'message': 'Invalid IDs provided. Must be integers.'}), 400

        success = False
        message = ""

        try:
            # Ensure cache is loaded before potentially modifying data
            # No force needed, the called functions should handle DB/cache consistency
            if not cache_manager.refresh_cached_data(force=False):
                log_message("Error: Cache not loaded in /resolve_duplicate_pair", level="ERROR")
                return jsonify({"error": "Data cache not available"}), 500

            # The merge/reject functions now handle DB updates and should ideally
            # update the relevant caches directly or return info needed to update.
            # Let's assume they update the cache for now.
            if resolution_type == 'merge_to_1':
                # merge_plaintiff_pair now expected to handle DB and cache update for plaintiffs, cases, and review table
                # It might need access to cache_manager internally or accept DFs
                success = merge_plaintiff_pair(keep_id=id1_int, delete_id=id2_int) # Assume this function updates cache
                message = f'Successfully merged plaintiff {id2_int} into {id1_int}.' if success else f'Failed to merge plaintiff {id2_int} into {id1_int}.'
            elif resolution_type == 'merge_to_2':
                success = merge_plaintiff_pair(keep_id=id2_int, delete_id=id1_int) # Assume this function updates cache
                message = f'Successfully merged plaintiff {id1_int} into {id2_int}.' if success else f'Failed to merge plaintiff {id1_int} into {id2_int}.'
            # elif resolution_type == 'reject':
            #     # reject_plaintiff_pair now expected to handle DB and cache update for review table
            #     success = reject_plaintiff_pair(id1_int, id2_int) # Assume this function updates cache
            #     message = f'Successfully marked pair ({id1_int}, {id2_int}) as not duplicates.' if success else f'Failed to mark pair ({id1_int}, {id2_int}) as rejected.'
            else:
                 log_message(f"Invalid resolution type received: {resolution_type}", level="WARNING")
                 return jsonify({'status': 'error', 'message': 'Invalid resolution type specified'}), 400

            # No explicit refresh here - assume functions updated cache directly
            log_message(f"Resolution outcome for pair ({id1_int}, {id2_int}): Success={success}, Message={message}", level="INFO")
            status_code = 200 if success else 500
            return jsonify({'status': 'success' if success else 'error', 'message': message}), status_code

        except Exception as e:
            log_message(f"Critical error resolving plaintiff pair ({id1_int}, {id2_int}, {resolution_type}): {e}", level="CRITICAL")
            # Optionally log traceback
            import traceback
            traceback.print_exc()
            return jsonify({'status': 'error', 'message': f'An unexpected server error occurred: {str(e)}'}), 500
        

    @app.route('/merge_plaintiffs', methods=['POST'])
    def merge_plaintiffs():
        """Merge a pair of plaintiffs"""
        keep_id = request.json.get('keep_id')
        delete_id = request.json.get('delete_id')
        
        try:
            affected_cases = merge_plaintiff_pair(cache_manager.cached_cases_df, cache_manager.cached_plaintiff_df, keep_id, delete_id)
            
            # Clean up after the merge
            delete_plaintiff_without_cases(cache_manager.cached_cases_df, cache_manager.cached_plaintiff_df)
            
            return jsonify({
                'status': 'success', 
                'message': f'Successfully merged plaintiffs. {affected_cases} cases were updated.'
            })
        except Exception as e:
            return jsonify({'status': 'error', 'message': f'Error merging plaintiffs: {str(e)}'})


    @app.route('/update_plaintiff_overview', methods=['POST'])
    def update_plaintiff_overview():
        """Update the overview for a specific plaintiff using AI."""
        data = request.get_json()
        plaintiff_id = data.get('plaintiff_id')
    
        log_message(f"Received request to update overview for plaintiff_id: {plaintiff_id}", level="INFO")

        if not plaintiff_id:
            log_message("Missing plaintiff_id for /update_plaintiff_overview", level="WARNING")
            return jsonify({'status': 'error', 'message': 'Missing required parameter: plaintiff_id'}), 400

        try:
            plaintiff_id_int = int(plaintiff_id)
        except ValueError:
            log_message(f"Invalid plaintiff_id format: {plaintiff_id}", level="WARNING")
            return jsonify({'status': 'error', 'message': 'Invalid plaintiff_id format. Must be an integer.'}), 400

        try:
            # Ensure cache is loaded (no force needed, just read access)
            if not cache_manager.refresh_cached_data(force=False):
                log_message("Error: Cache not loaded in /update_plaintiff_overview", level="ERROR")
                return jsonify({"error": "Data cache not available"}), 500

            if cache_manager.cached_cases_df is None or cache_manager.cached_plaintiff_df is None:
                 log_message("Error: Required cached dataframes are None in /update_plaintiff_overview", level="ERROR")
                 return jsonify({"error": "Data cache unavailable"}), 500

            # 1. Get plaintiff names associated with this ID from cases
            plaintiff_name = cache_manager.cached_plaintiff_df[cache_manager.cached_plaintiff_df['id'] == plaintiff_id_int]['plaintiff_name'].values[0]
            plaintiff_names_list = get_plaintiff_names(cache_manager.cached_cases_df, plaintiff_id_int)
            if not plaintiff_names_list:
                log_message(f"No associated plaintiff names found for plaintiff_id {plaintiff_id_int}", level="WARNING")
                # Decide if this is an error or just means no update possible
                # For now, let's return a specific message
                return jsonify({'status': 'info', 'message': f'No associated case names found to generate overview for plaintiff {plaintiff_id_int}.'}), 200

            log_message(f"Found names for plaintiff {plaintiff_id_int}: {plaintiff_names_list}", level="DEBUG")

            # 2. Call the AI function to get the new overview
            # Consider adding brands and summary if needed/available easily
            # plaintiff_brands_list = get_plaintiff_brand(cache_manager.cached_cases_df, plaintiff_id_int) # Optional
            # case_summary = ... # Optional, might need more logic to get relevant summary
            new_overview_json_str = get_plaintif_overview_using_list(plaintiff_names_list) #, plaintiff_brands_list=plaintiff_brands_list, case_summary=case_summary)
            log_message(f"Generated new overview for plaintiff {plaintiff_id_int}: {new_overview_json_str}", level="INFO")

            # Parse the result to send back structured data
            try:
                new_overview_data = json.loads(new_overview_json_str)
            except json.JSONDecodeError:
                log_message(f"Failed to parse AI response as JSON for plaintiff {plaintiff_id_int}: {new_overview_json_str}", level="ERROR")
                # Handle error - maybe return the raw string or an error message
                return jsonify({'status': 'error', 'message': 'Failed to parse AI response.'}), 500


            # 3. Update the database
            update_df = pd.DataFrame([{'id': plaintiff_id_int, 'plaintiff_name': plaintiff_name, 'plaintiff_overview': new_overview_json_str}])
            insert_and_update_df_to_GZ_batch(update_df, "tb_plaintiff", "id")

            # 4. Update the cache
            plaintiff_index = cache_manager.cached_plaintiff_df[cache_manager.cached_plaintiff_df['id'] == plaintiff_id_int].index
            if not plaintiff_index.empty:
                cache_manager.cached_plaintiff_df.loc[plaintiff_index[0], 'plaintiff_overview'] = new_overview_json_str
                log_message(f"Updated cache for plaintiff {plaintiff_id_int}", level="INFO")
            else:
                log_message(f"Plaintiff {plaintiff_id_int} not found in cache after DB update?", level="WARNING")
                # This shouldn't happen if cache is consistent, but log it.

            # 5. Return success response
            return jsonify({
                'status': 'success',
                'message': f'Successfully updated overview for plaintiff {plaintiff_id_int}.',
                'new_overview': new_overview_data # Send back the parsed new overview
            })

        except Exception as e:
            log_message(f"Error updating plaintiff overview for ID {plaintiff_id_int}: {e}", level="CRITICAL")
            import traceback
            traceback.print_exc()
            return jsonify({'status': 'error', 'message': f'An unexpected server error occurred: {str(e)}'}), 500


    return app
