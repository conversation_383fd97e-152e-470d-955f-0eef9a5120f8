import os
import re
import time
import asyncio
import datetime
import random
import aiohttp
import json
import logging
from pathlib import Path
import xml.etree.ElementTree as ET
from urllib.parse import urlencode

# For USPTO patent searches using APIs, standard rate limits are 60 requests per API key per minute,
#  with PDF and ZIP downloads limited to 4 requests per API key per minute.
# During off-peak hours (10 PM - 5 AM EST), these limits increase to 120 requests and 12 downloads respectively

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


class PatentApi:
    """
    A class to interact with the USPTO Patent API for patent data retrieval.
    """
    # Make this a class attribute so it can be accessed as PatentApi.USPTO_PATENT_API_BASE_URL
    # USPTO_PATENT_API_BASE_URL = "https://ppubs.uspto.gov/dirsearch-public"
    USPTO_PATENT_API_BASE_URL = "https://ppubs.uspto.gov/api"
    USPTO_ASSIGNMENT_API_BASE_URL = "https://assignment-api.uspto.gov"

    def __init__(self):
        """
        Initializes the PatentApi client.
        """
        self.session = None  # Initialize session in start method
        self.access_token = None
        self.token_expiry = None
        self.request_token = None  # For document download requests

        # Rate limiting - new implementation
        self.download_timestamps = []  # Track timestamps of downloads
        self.download_semaphore = asyncio.Semaphore(4)  # Limit concurrent downloads
        self.download_lock = asyncio.Lock()  # For thread-safe access to timestamps list

    async def start_session(self):
        """Starts an aiohttp client session."""
        if self.session is None or self.session.closed:
            self.session = aiohttp.ClientSession()
            # Automatically get a token when starting a session
            await self.get_access_token()

    async def close_session(self):
        """Closes the aiohttp client session."""
        if self.session and not self.session.closed:
            await self.session.close()
            self.session = None
            self.access_token = None
            self.token_expiry = None

    async def get_access_token(self):
        """
        Gets an access token from the USPTO Patent API.
        The token is returned in the response header as X-Access-Token.
        """
        if self.session is None or self.session.closed:
            # Avoid calling start_session recursively if it's already trying to get a token
            if not hasattr(self, '_getting_token') or not self._getting_token:
                 self._getting_token = True
                 try:
                     await self.start_session()
                 finally:
                     self._getting_token = False
            else:
                 # If already getting token, just ensure session exists
                 if self.session is None or self.session.closed:
                      self.session = aiohttp.ClientSession()


        url = f"{self.USPTO_PATENT_API_BASE_URL}/users/me/session"

        print("Getting access token from USPTO Patent API")
        try:
            async with self.session.post(url, ssl=True) as response:
                if response.status == 200:
                    # Get token from header
                    self.access_token = response.headers.get('X-Access-Token')

                    # Use the X-Access-Token as the request token
                    if self.access_token:
                        self.request_token = self.access_token
                        print(f"Using X-Access-Token as request token: {self.request_token[:10]}...")
                    else:
                        # Try to get request token from response as fallback
                        try:
                            # First, get the response text
                            response_text = await response.text()
                            print(f"Response text: {response_text}")

                            # Try parsing as JSON
                            try:
                                response_json = json.loads(response_text)
                                print(f"Response JSON: {response_json}")

                                # Look for requestToken in standard location
                                self.request_token = response_json.get('requestToken')

                                # If still not found, search for it in the response text
                                if not self.request_token and 'requestToken' in response_text:
                                    # Try to extract it using regex
                                    import re
                                    match = re.search(r'requestToken["\']?\s*:\s*["\']([^"\']+)["\']', response_text)
                                    if match:
                                        self.request_token = match.group(1)
                                        print(f"Extracted request token from response text")
                            except json.JSONDecodeError:
                                # Not JSON, try to extract token directly from text
                                if 'requestToken' in response_text:
                                    import re
                                    match = re.search(r'requestToken["\']?\s*:\s*["\']([^"\']+)["\']', response_text)
                                    if match:
                                        self.request_token = match.group(1)
                                        print(f"Extracted request token from non-JSON response")

                            # Log the result
                            if self.request_token:
                                print(f"Request token obtained: {self.request_token[:10]}...")
                            else:
                                print(f"No request token found in response. The API may have changed.")
                                print(f"Response headers: {response.headers}")
                                print(f"First 200 chars of response: {response_text[:200]}")

                        except Exception as e:
                            print(f"Error processing response: {e}")

                    # Set token expiry - assuming token lasts for 1 hour
                    self.token_expiry = datetime.datetime.now() + datetime.timedelta(hours=1)
                    logging.info("✅ Successfully obtained access token")
                    return self.access_token
                else:
                    print(f"⚠️ Failed to get access token. Status code: {response.status}")
                    response.raise_for_status()
        except Exception as e:
            logging.error(f"⚠️ Error getting access token: {str(e)}")
            raise

    async def _ensure_token_valid(self):
        """
        Ensures that the access token is valid and refreshes it if necessary.
        """
        current_time = datetime.datetime.now()

        # If token doesn't exist or is about to expire (within 5 minutes), get a new one
        if (self.access_token is None or
            self.token_expiry is None or
            current_time + datetime.timedelta(minutes=5) >= self.token_expiry):
            await self.get_access_token()

    async def _check_download_rate_limit(self):
        """
        Check if we've exceeded download rate limits and wait if necessary.
        Uses a lock to prevent race conditions when multiple coroutines access
        the timestamps list simultaneously.
        """
        async with self.download_lock:
            # Remove timestamps older than 60 seconds
            current_time = time.time()
            self.download_timestamps = [t for t in self.download_timestamps if current_time - t < 60]

            # Check if we've hit the limit (4 normal, 12 off-peak)
            current_hour = datetime.datetime.now(tz=datetime.timezone(datetime.timedelta(hours=-5))).hour  # EST
            off_peak = 22 <= current_hour or current_hour < 5
            limit = 12 if off_peak else 4

            current_count = len(self.download_timestamps)

            if current_count >= limit:
                # We've hit the limit, wait until oldest timestamp expires
                wait_time = 60 - (current_time - self.download_timestamps[0]) + 0.1  # Add a small buffer
                if wait_time > 0:
                    oldest_timestamp = datetime.datetime.fromtimestamp(self.download_timestamps[0]).strftime('%H:%M:%S')
                    print(f"\033[91m 🔥 USPTO Patent API: Rate limit reached. Downloads in past minute: {current_count}/{limit}")
                    print(f"   Oldest request: {oldest_timestamp}, waiting {wait_time:.1f} seconds for it to expire\033[0m")

                    # Release the lock while waiting
                    self.download_lock.release()
                    try:
                        await asyncio.sleep(wait_time)
                    finally:
                        # Re-acquire the lock after waiting
                        await self.download_lock.acquire()

                    # After waiting, clean up expired timestamps again
                    current_time = time.time()
                    self.download_timestamps = [t for t in self.download_timestamps if current_time - t < 60]
            else:
                print(f"\033[92m ✓ USPTO Patent API: Download request {current_count+1}/{limit} in the last minute\033[0m")

            # Record this request
            self.download_timestamps.append(current_time)

    async def _request_with_retry(self, url, method="GET", data=None, max_retries=5, is_download=False):
        """
        Handles API requests with retry logic.

        Args:
            url (str): The API endpoint URL.
            method (str): HTTP method (GET, POST, etc.)
            data (dict, optional): Data to be sent in the request body.
            max_retries (int, optional): Maximum retry attempts.
            is_download (bool, optional): Whether this is a download request (PDF/HTML)

        Returns:
            The response content.

        Raises:
            RuntimeError: If the API request fails after maximum retries.
        """
        await self._ensure_token_valid()

        # Apply rate limiting for downloads
        if is_download:
            # Use semaphore to limit concurrent downloads to 4
            async with self.download_semaphore:
                # Check rate limits after acquiring semaphore to prevent race conditions
                await self._check_download_rate_limit()
                return await self._perform_request(url, method, data, max_retries)
        else:
            # For regular requests, no strict rate limiting needed
            return await self._perform_request(url, method, data, max_retries)

    async def _perform_request(self, url, method="GET", data=None, max_retries=5):
        """Actually performs the request with retry logic"""
        retry_attempts = 0

        while retry_attempts < max_retries:
            try:
                headers = {}
                if self.access_token:
                    # Use X-Access-Token header instead of Authorization
                    headers["X-Access-Token"] = self.access_token

                # print(f"Requesting URL: {url}, Method: {method}, Attempt: {retry_attempts+1}")

                if method.upper() == "GET":
                    async with self.session.get(url, headers=headers, ssl=True) as response:
                        if response.status == 200:
                            content = await response.read()
                            # logging.info(f"✅ URL request successful after {retry_attempts+1} attempts for URL: {url}")
                            return content
                        elif response.status == 401:
                            # Token might have expired, get a new one
                            logging.warning("⚠️ Token expired. Getting a new one.")
                            await self.get_access_token()
                            retry_attempts += 1
                            continue
                        elif response.status == 429:
                            # Rate limit exceeded
                            retry_attempts += 0.2
                            wait_time = min(60, 5 * (2 ** retry_attempts))  # Exponential backoff capped at 60 seconds
                            logging.warning(f"⚠️ Rate limit exceeded. Waiting {wait_time:.1f} seconds before retry...")
                            await asyncio.sleep(wait_time)
                            continue
                        else:
                            # logging.warning(f"⚠️ API returned status code: {response.status} for URL: {url}")
                            response.raise_for_status()

                elif method.upper() == "POST":
                    headers["Content-Type"] = "application/json"
                    async with self.session.post(url, headers=headers, json=data, ssl=True) as response:
                        if response.status == 200:
                            content = await response.read()
                            # logging.info(f"✅ URL request successful after {retry_attempts+1} attempts for URL: {url}")
                            return content
                        elif response.status == 401:
                            # Token might have expired, get a new one
                            print("⚠️ Token expired. Getting a new one.")
                            await self.get_access_token()
                            retry_attempts += 1
                            continue
                        elif response.status == 429:
                            # Rate limit exceeded
                            retry_attempts += 1
                            wait_time = min(60, 5 * (2 ** retry_attempts))  # Exponential backoff capped at 60 seconds
                            print(f"⚠️ USPTO Patent API: Rate limit exceeded. Waiting {wait_time:.1f} seconds before retry...")
                            await asyncio.sleep(wait_time)
                            continue
                        else:
                            # print(f"⚠️ USPTO Patent API: API returned status code: {response.status} for URL: {url}")
                            response.raise_for_status()

            except (aiohttp.ClientError, aiohttp.http_exceptions.HttpProcessingError) as e:
                print(f"⚠️ USPTO Patent API: Error during request for URL: {url}.     Error: {str(e)}.      Attempt {retry_attempts+1} of {max_retries}")

                # Check if this is a "Connector is closed" error or session is closed
                if "Connector is closed" in str(e) or self.session is None or self.session.closed:
                    print("Session appears to be closed. Restarting session and getting new token...")
                    await self.start_session()  # This will create a new session and get a token

                retry_attempts += 1
                if retry_attempts < max_retries:
                    # Add exponential backoff
                    wait_time = min(60, 5 * (2 ** retry_attempts))  # Capped at 60 seconds
                    logging.info(f"Waiting {wait_time:.1f} seconds before retry...")
                    await asyncio.sleep(wait_time)
                else:
                    print(f"❌ Failed after {max_retries} attempts: {str(e)}")
            
            except Exception as e:
                print(f"❌ Failed after {max_retries} attempts: {str(e)}")
                retry_attempts += 1

        return None  # Return None if all retries failed but didn't raise an exception

    async def search_patents(self, query, database_filters=None, fields=None,
                            page_size=50, cursor_marker="*", sort="date_publ desc"):
        """
        Search for patents using the USPTO Patent API.

        Args:
            query (str): The search query (e.g., "apple AND samsung")
            database_filters (list, optional): List of database filters. Defaults to ["USPAT", "US-PGPUB", "USOCR"].
            fields (list, optional): Fields to include in the response.
                                    Defaults to ["documentId", "patentNumber", "title", "datePublished",
                                                "inventors", "pageCount", "type"].
            page_size (int, optional): Number of results per page. Defaults to 50.
            cursor_marker (str, optional): Cursor marker for pagination. Defaults to "*".
            sort (str, optional): Sort order. Defaults to "date_publ desc".

        Returns:
            dict: The search results with standardized format.
        """
        url = f"{self.USPTO_PATENT_API_BASE_URL}/searches/generic"

        if database_filters is None:
            database_filters = [
                {"databaseName": "USPAT"},
                {"databaseName": "US-PGPUB"},
                {"databaseName": "USOCR"}
            ]

        if fields is None:
            fields = [
                "documentId", "patentNumber", "title", "datePublished",
                "inventors", "assignees", "pageCount", "type"
            ]

        # Prepare the request payload
        payload = {
            "cursorMarker": cursor_marker,
            "databaseFilters": database_filters,
            "fields": fields,
            "op": "AND",
            "pageSize": page_size,
            "q": query,
            "searchType": 0,
            "sort": sort
        }

        # Make the request
        response = await self._request_with_retry(url, method="POST", data=payload)

        if response:
            # Parse the JSON response
            data = json.loads(response)

            # Standardize the response format (API returns 'docs' but our code expects 'results')
            standardized_response = {
                "cursorMarker": data.get("cursorMarker"),
                "numFound": data.get("numFound", 0),
                "results": data.get("docs", []),  # Store docs as results for compatibility with tests
                "nextCursorMarker": data.get("cursorMarker")  # Use cursorMarker for pagination
            }
            if len(data.get("docs", [])) > 0:
                print(f'✅ search_patents: for query: {query} , returned {len(data.get("docs", []))} results: {[doc["patentNumber"] for doc in data.get("docs", [])]}')
            else:
                print(f'❌ search_patents: for query: {query} , returned 0 results')

            return standardized_response
        else:
            return None

    async def search_patents_paginated(self, query, database_filters=None, fields=None,
                                      page_size=50, max_pages=10, sort="date_publ desc"):
        """
        Search for patents with pagination support.

        Args:
            query (str): The search query (e.g., "apple AND samsung")
            database_filters (list, optional): List of database filters.
            fields (list, optional): Fields to include in the response.
            page_size (int, optional): Number of results per page. Defaults to 50.
            max_pages (int, optional): Maximum number of pages to retrieve. Defaults to 10.
            sort (str, optional): Sort order. Defaults to "date_publ desc".

        Returns:
            list: All search results combined from all pages.
        """
        cursor_marker = "*"
        all_results = []
        current_page = 0

        while cursor_marker and current_page < max_pages:
            response = await self.search_patents(
                query,
                database_filters=database_filters,
                fields=fields,
                page_size=page_size,
                cursor_marker=cursor_marker,
                sort=sort
            )

            if not response:
                break

            # Add the results to our list
            if "results" in response and response["results"]:
                all_results.extend(response["results"])

            # Update the cursor marker for the next page
            cursor_marker = response.get("nextCursorMarker")
            if not cursor_marker or cursor_marker == "*":  # Avoid infinite loop
                break

            current_page += 1

        return all_results

    async def search_patents_by_owner_in_Patent_Assignment_Search(self, owner_name: str, nb_results: int = 50, max_retries=3) -> list:
        """
        Search for patents by owner name using the USPTO Assignment API.

        Args:
            owner_name (str): The name of the owner to search for.
            nb_results (int): The maximum number of results to return (default: 50).
            max_retries (int): Maximum retry attempts for the request.

        Returns:
            list: A list of dictionaries, each containing patent details
                  (patentNumber, patAssigneeName, inventors, issueDate).
                  Returns an empty list if no results or an error occurs.
        """
        if self.session is None or self.session.closed:
            await self.start_session() # Ensure session is active, though token isn't used here

        params = {
            'query': owner_name,
            'filter': 'OwnerName',
            'rows': nb_results
        }
        encoded_params = urlencode(params)
        url = f"{self.USPTO_ASSIGNMENT_API_BASE_URL}/patent/lookup?{encoded_params}"
        # logging.info(f"Searching patents by owner: {owner_name} using URL: {url}")

        retry_attempts = 0
        while retry_attempts < max_retries:
            try:
                # Use the existing session, but don't pass token headers for this specific public endpoint
                async with self.session.get(url, ssl=True) as response:
                    if response.status == 200:
                        xml_content = await response.text()
                        # logging.info(f"Successfully fetched data for owner: {owner_name}")

                        # Parse XML
                        root = ET.fromstring(xml_content)
                        results = []
                        # Find the result list and then iterate through doc elements
                        result_node = root.find("./result[@name='response']")
                        if result_node is not None:
                            for doc in result_node.findall('doc'):
                                patent_data = {}

                                # Helper to extract text safely from potentially multiple elements
                                def extract_text(element_name, separator='; '):
                                    elements = doc.findall(f"./arr[@name='{element_name}']/str")
                                    if not elements:
                                        # Try finding date element if str not found
                                        elements = doc.findall(f"./arr[@name='{element_name}']/date")
                                    if not elements:
                                        # Try finding single str/date element if arr not found
                                        element = doc.find(f"./str[@name='{element_name}First']")
                                        if element is not None and element.text:
                                            return element.text.strip()
                                        element = doc.find(f"./date[@name='{element_name}First']")
                                        if element is not None and element.text:
                                            # Extract just the date part YYYY-MM-DD
                                            return element.text.split('T')[0]
                                        return None # Return None if not found

                                    texts = [elem.text.strip() for elem in elements if elem.text and elem.text.strip().upper() != 'NULL']
                                    if not texts:
                                        return None
                                    # Special handling for date - take the first one and format
                                    if element_name == 'issueDate':
                                        # Extract just the date part YYYY-MM-DD
                                        return texts[0].split('T')[0]
                                    return separator.join(texts)

                                # Use 'patentNumber' for consistency with the other search method
                                patent_data['patentNumber'] = extract_text('patNum')
                                patent_data['patAssigneeName'] = extract_text('patAssigneeName', separator='; ')
                                patent_data['inventors'] = extract_text('inventors', separator='; ')
                                patent_data['datePublished'] = extract_text('issueDate')
                                patent_data['title'] = extract_text('inventionTitle')

                                # Only add if patentNumber is present
                                if patent_data['patentNumber']:
                                    results.append(patent_data)

                        logging.info(f"Found {len(results)} patents for owner: {owner_name}")
                        if len(results) > 0:
                            print(f'✅ search_assignee: for owner: {owner_name} , returned {len(results)} results: {[patent["patentNumber"] for patent in results]}')
                        else:
                            print(f'❌ search_assignee: for owner: {owner_name} , returned 0 results')
                        return results

                    elif response.status == 429:
                        retry_attempts += 1
                        wait_time = min(60, 5 * (2 ** retry_attempts))
                        logging.warning(f"Rate limit exceeded for owner search. Waiting {wait_time:.1f}s...")
                        await asyncio.sleep(wait_time)
                        continue
                    else:
                        logging.error(f"Failed to search patents by owner. Status: {response.status}, Reason: {await response.text()}")
                        response.raise_for_status() # Raise exception for other errors

            except (aiohttp.ClientError, ET.ParseError, Exception) as e:
                logging.error(f"Error searching patents by owner '{owner_name}': {str(e)}. Attempt {retry_attempts+1}/{max_retries}")
                retry_attempts += 1
                if retry_attempts < max_retries:
                    wait_time = min(30, 2 * (2 ** retry_attempts))
                    await asyncio.sleep(wait_time)
                else:
                    logging.error(f"Max retries reached for owner search: {owner_name}")
                    return [] # Return empty list on persistent failure

        return [] # Return empty list if all retries fail


    async def download_patent_pdf(self, document_id):
        """
        Download a patent document in PDF format.

        Args:
            document_id (str): The document ID (e.g., "12243085")

        Returns:
            bytes: The PDF content as bytes.

        Example:
            pdf_content = await api_client.download_patent_pdf("12243085")
            with open("patent.pdf", "wb") as f:
                f.write(pdf_content)
        """
        # Ensure we have a valid token
        await self._ensure_token_valid()

        # Make sure we have a request token
        if not self.request_token:
            logging.warning("No request token available. Getting a new one.")
            await self.get_access_token()

            # If still no request token, we can't proceed
            if not self.request_token:
                logging.error("Failed to obtain request token for PDF download.")
                logging.error("This may be due to API restrictions or changes in the API response format.")
                logging.error("Please verify the API documentation or contact the API provider.")
                return None

        # Clean the document_id (remove any prefixes like "US-")
        clean_id = document_id
        if "-" in document_id:
            # Handle formats like ***********-B1
            parts = document_id.split("-")
            if len(parts) >= 2:
                # If format is ***********-B1, get the middle part
                if len(parts) >= 3 and parts[0].upper() in ["US", "USD", "EP"]:
                    clean_id = parts[1]
                else:
                    # Otherwise just use the last part
                    clean_id = parts[-1]

            # Remove any B1, B2, A1 suffix if it's directly in the ID without hyphen
            if clean_id.endswith("B1") or clean_id.endswith("B2") or clean_id.endswith("A1"):
                clean_id = clean_id[:-2]

        # logging.info(f"Using clean document ID: {clean_id} (original: {document_id})")
        url = f"{self.USPTO_PATENT_API_BASE_URL}/pdf/downloadPdf/{clean_id}?requestToken={self.request_token}"
        # logging.info(f"PDF download URL: {url}")

        # Download the PDF with rate limiting
        content = await self._request_with_retry(url, is_download=True)
        if content: # Check if content was successfully downloaded
             print(f"✅ download_patent_pdf: for document_id: {document_id} , returned {len(content)} bytes")
        else:
             print(f"⚠️ download_patent_pdf: Failed to download document_id: {document_id}")
        return content

    async def download_patent_html(self, document_id, source_db="USPAT"):
        """
        Download a patent document in HTML format.

        Args:
            document_id (str): The document ID (e.g., "12243085")
            source_db (str): The source database. Default is "USPAT".

        Returns:
            bytes: The HTML content as bytes.

        Example:
            html_content = await api_client.download_patent_html("12243085")
            with open("patent.html", "wb") as f:
                f.write(html_content)
        """
        # Ensure we have a valid token
        await self._ensure_token_valid()

        # Make sure we have a request token
        if not self.request_token:
            logging.warning("No request token available. Getting a new one.")
            await self.get_access_token()

            # If still no request token, we can't proceed
            if not self.request_token:
                logging.error("Failed to obtain request token for HTML download.")
                logging.error("This may be due to API restrictions or changes in the API response format.")
                logging.error("Please verify the API documentation or contact the API provider.")
                return None

        # Clean the document_id (remove any prefixes like "US-")
        clean_id = document_id
        if "-" in document_id:
            # Handle formats like ***********-B1
            parts = document_id.split("-")
            if len(parts) >= 2:
                # If format is ***********-B1, get the middle part
                if len(parts) >= 3 and parts[0].upper() in ["US", "USD", "EP"]:
                    clean_id = parts[1]
                else:
                    # Otherwise just use the last part
                    clean_id = parts[-1]

            # Remove any B1, B2, A1 suffix if it's directly in the ID without hyphen
            if clean_id.endswith("B1") or clean_id.endswith("B2") or clean_id.endswith("A1"):
                clean_id = clean_id[:-2]

        # logging.info(f"Using clean document ID: {clean_id} (original: {document_id})")
        url = f"{self.USPTO_PATENT_API_BASE_URL}/patents/html/{clean_id}?source={source_db}&requestToken={self.request_token}"
        # logging.info(f"HTML download URL: {url}")

        # Download the HTML with rate limiting
        content = await self._request_with_retry(url, is_download=True)
        if content: # Check if content was successfully downloaded
            print(f"✅ download_patent_html: for document_id: {document_id} , returned {len(content)} bytes")
        else:
            print(f"⚠️ download_patent_html: Failed to download document_id: {document_id}")
        return content

    async def save_patent(self, document_id, filename=None, output_dir=None, formats=None):
        """
        Download and save a patent document in various formats.

        Args:
            document_id (str): The document ID (e.g., "12243085")
            output_dir (str, optional): Directory to save files to. Default is current directory.
            formats (list, optional): List of formats to download. Default is ["pdf", "html"].

        Returns:
            dict: Dictionary with saved file paths for each format.

        Example:
            files = await api_client.save_patent("12243085", output_dir="patents")
            print(f"PDF saved to: {files['pdf']}")
            print(f"HTML saved to: {files['html']}")
        """
        if output_dir is None:
            output_dir = os.getcwd()

        if formats is None:
            formats = ["pdf", "html"]

        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)

        # Clean document_id for filenames
        if filename is None:
            filename = document_id
        # clean_id = document_id
        # if "-" in document_id:
        #     # Handle formats like ***********-B1
        #     parts = document_id.split("-")
        #     if len(parts) >= 2:
        #         # If format is ***********-B1, get the middle part
        #         if len(parts) >= 3 and parts[0].upper() in ["US", "USD", "EP"]:
        #             clean_id = parts[1]
        #         else:
        #             # Otherwise just use the last part
        #             clean_id = parts[-1]

        #     # Remove any B1, B2, A1 suffix if it's directly in the ID without hyphen
        #     if clean_id.endswith("B1") or clean_id.endswith("B2") or clean_id.endswith("A1"):
        #         clean_id = clean_id[:-2]

        # logging.info(f"Using clean document ID for save: {clean_id} (original: {document_id})")

        results = {}

        # Download and save requested formats
        for fmt in formats:
            if fmt.lower() == "pdf":
                pdf_path = os.path.join(output_dir, f"{filename}.pdf")
                if not os.path.exists(pdf_path):
                    pdf_content = await self.download_patent_pdf(document_id)
                    if pdf_content:
                        with open(pdf_path, "wb") as f:
                            f.write(pdf_content)
                        results["pdf"] = pdf_path
                        # logging.info(f"✅ Saved PDF to {pdf_path}")

            elif fmt.lower() == "html":
                html_path = os.path.join(output_dir, f"{filename}.html")
                if not os.path.exists(html_path):
                    html_content = await self.download_patent_html(document_id)
                    if html_content:
                        with open(html_path, "wb") as f:
                            f.write(html_content)
                        results["html"] = html_path
                        # logging.info(f"✅ Saved HTML to {html_path}")

            # Add support for other formats here if needed

        return results

async def main():
    api = PatentApi()
    try:
        # Start session - this also gets the initial token if needed by other methods
        await api.start_session()

        # Example: Search by owner name in Patent Assignment Search
        owner_name_to_search = "XIANGXIN LIU"
        print(f"\nSearching patents for owner: {owner_name_to_search}...")
        owner_results = await api.search_patents_by_owner_in_Patent_Assignment_Search(owner_name_to_search)

        if owner_results:
            print(f"Found {len(owner_results)} patents for owner '{owner_name_to_search}':")
            for i, patent in enumerate(owner_results):
                print(f"  Result {i+1}:")
                print(f"    Patent Number: {patent.get('patNum', 'N/A')}")
                print(f"    Assignee Name(s): {patent.get('patAssigneeName', 'N/A')}")
                print(f"    Inventor(s): {patent.get('inventors', 'N/A')}")
                print(f"    Issue Date: {patent.get('issueDate', 'N/A')}")
        else:
            print(f"No patents found for owner '{owner_name_to_search}'.")

        # --- other examples if needed ---
        # Example: Search patents paginated
        query = "semiconductor AND manufacturing"
        query = "dyson.as. AND D$.ccls."
        print(f"\nSearching patents with query: '{query}'...")
        search_results = await api.search_patents_paginated(query, max_pages=1) # Limit pages for example
        if search_results:
            print(f"Found {len(search_results)} total results (first page):")
            for i, result in enumerate(search_results[:5]): # Print first 5
                print(f"  Result {i+1}: ID={result.get('documentId', 'N/A')}, Title={result.get('title', 'N/A')[:50]}...")
        else:
            print("No results found for the query.")

        # Example: Download a patent PDF and HTML (uncomment to run)
        if search_results:
            doc_id_to_download = search_results[0].get('documentId')
            if doc_id_to_download:
                print(f"\nDownloading patent {doc_id_to_download}...")
                saved_files = await api.save_patent(doc_id_to_download, output_dir="downloaded_patents", formats=["pdf", "html"])
                print("Saved files:", saved_files)

    except Exception as e:
        logging.error(f"An error occurred in the main execution: {e}", exc_info=True) # Add traceback
    finally:
        await api.close_session()
        print("\nSession closed.")

if __name__ == "__main__":
    # Ensure the event loop policy is set for Windows if needed
    if os.name == 'nt':
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    asyncio.run(main())
