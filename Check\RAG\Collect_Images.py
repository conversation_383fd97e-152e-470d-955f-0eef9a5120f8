from DatabaseManagement.ImportExport import get_table_from_GZ
from Alerts.PicturesProcessing.RemoveDuplicates import compute_md5
import json
import zlib
import base64
import os
import requests
import imagehash
from PIL import Image
import shutil
import re
import asyncio
from Check.Do_Check_Download import download_from_url
from Common.Constants import sanitize_name, sem_task
from tqdm.asyncio import tqdm_asyncio
from tqdm import tqdm
from AI.GC_VertexAI import vertex_genai_multi
from AI.LLM_shared import get_json
from Alerts.PicturesProcessing.ProcessPicturesTrademarks import get_trademark_text_ocr
import cv2

async def collect_all_images(df, target_folder_path, case_type, existing_folder_path=None):
    if not os.path.exists(target_folder_path):
        os.makedirs(target_folder_path)
    tasks = []
    semaphore = asyncio.Semaphore(20)
    for index, row in df.iterrows():
        if case_type in row['images']:
            for image in row['images'][case_type].keys():
                # Ignore images that are the same as the certificate
                if "full_filename" in row['images'][case_type][image] and row['images'][case_type][image]['full_filename'] == image:
                    continue

                # For Patent, add the text to the filename
                if "product_name" in row['images'][case_type][image] and row['images'][case_type][image]['product_name'] != "":
                    file_name = f"{row['images'][case_type][image]['product_name']}_{image}"
                # elif "trademark_text" in row['images'][case_type][image]: 
                #     trademark_text = row['images'][case_type][image]['trademark_text']
                #     if trademark_text != "":
                #         file_name = f"{file_name}/Logo"
                #     else:
                #         # count the number of special characters in the trademark_text
                #         count_special_characters = sum(not c.isalnum() for c in trademark_text)

                #         file_name = f"{file_name}/Logo"
                else:
                    file_name = image

                file_name = sanitize_name(file_name)
                target_path = f"{target_folder_path}/{file_name}"
                
                # Avoid redownloading pictures which we have already downloaded
                if os.path.exists(target_path):
                    continue
                if existing_folder_path: 
                    existing_path = f"{existing_folder_path}/{file_name}"
                    if os.path.exists(existing_path):
                        continue

                url = f"http://troimages-1329604052.cos.ap-guangzhou.myqcloud.com/plaintiff_images/{row['plaintiff_id']}/high/{image}"
                tasks.append(sem_task(semaphore, download_from_url(url, target_path)))  

    # Process all downloads concurrently
    if tasks:
        await tqdm_asyncio.gather(*tasks)
    else:
        print("No new images to download.")




def classify_trademark_images(df, folder_path):
    # This function is used to fix the trademark images that are in the text folder but should be in the logo folder.
    # It is used after the images have been downloaded and the text folder has been created.

    os.makedirs(f"{folder_path}/Logo", exist_ok=True)
    os.makedirs(f"{folder_path}/Text", exist_ok=True)
    os.makedirs(f"{folder_path}/Unknown", exist_ok=True)
    
    for filename in os.listdir(folder_path):
        file_path = os.path.join(folder_path, filename)
        if os.path.isdir(file_path):
            continue
        
        docket = get_docket_from_filename(filename)
        if docket is None:
            continue

        # Get the case image row from the df
        for i in range(len(df[df['docket'] == docket])):
            try: 
                case_image = df[df['docket'] == docket]['images'].values[i]['trademarks'][filename]
                break
            except:
                print(f"No case image found for {filename}. Trying next case.")

        if "trademark_text" in case_image:
            trademark_text = case_image['trademark_text'][0]
            if trademark_text == "":
                file_destination_path = f"{folder_path}/Logo"
            else:
                # count the number of special characters in the trademark_text
                count_special_characters = sum(1 for c in trademark_text if not c.isalnum() and not c.isspace() and c not in {'-', "'", '.'})
                if count_special_characters == 0:
                    file_destination_path = f"{folder_path}/Text"
                else: # ask AI
                    prompt_list = [
                        ("text", 'This is a registered trademark. You need to classify it as either a Text or a Logo. You answer in this format: {"type": "text"} or {"type": "logo"}'),
                        ("image_path", file_path),
                    ]
                    ai_answer = vertex_genai_multi(prompt_list)
                    json_answer = get_json(ai_answer)
                    if "type" in json_answer and json_answer["type"] == "text":
                        file_destination_path = f"{folder_path}/Text"
                    elif "type" in json_answer and json_answer["type"] == "logo":
                        file_destination_path = f"{folder_path}/Logo"
                    
        shutil.move(file_path, file_destination_path)


# Remove duplicates by md5 hash
def remove_duplicates(folder_path, reference_folder=None):
    hash_dict = {}

    if reference_folder:
        for filename in os.listdir(reference_folder):
            file_path = os.path.join(reference_folder, filename)
            file_hash = compute_md5(file_path)
            hash_dict[file_hash] = file_path

    
    with tqdm(total=len(os.listdir(folder_path)), desc="Removing Exact Duplicates") as pbar:
        for filename in os.listdir(folder_path):
            file_path = os.path.join(folder_path, filename)
            file_hash = compute_md5(file_path)
            if file_hash in hash_dict:
                # original_file = os.path.basename(hash_dict[file_hash])
                os.remove(file_path)
            else:
                hash_dict[file_hash] = file_path
            pbar.update(1)


# Remove duplicates by filename
def remove_duplicates_by_filename_with_different_extension(folder_path):
    filename_dict = {}
    for full_filename in os.listdir(folder_path):
        file_path = os.path.join(folder_path, full_filename)
        filename = full_filename.split(".")[0]
        if filename in filename_dict:
            if full_filename.split(".")[1] == ".webp":
                os.remove(file_path)
            else:
                original_file = filename_dict[filename]
                os.remove(original_file)
        else:
            filename_dict[filename] = file_path

# Remove duplicates by patent name and page number
def remove_patent_duplicates(folder_path):
    filename_dict = {}
    for full_filename in os.listdir(folder_path):
        patent_name = full_filename.split("_")[0]
        page_number = re.search(r'page\d+', full_filename).group(0)
        file_path = os.path.join(folder_path, full_filename)
        if (patent_name, page_number) in filename_dict:
            os.remove(file_path)
        else:
            filename_dict[(patent_name, page_number)] = file_path

def remove_text_from_patent_filename(folder_path):
    for filename in os.listdir(folder_path):
        new_filename = "_".join(filename.split("_")[1:])
        os.rename(os.path.join(folder_path, filename), os.path.join(folder_path, new_filename))

# Remove duplicates by perceptual hashing
# def remove_near_duplicates(folder_path, hash_size=16, threshold=10):
#     """
#     Removes near-duplicate images in the specified directory based on perceptual hashing.
#     """
#     hashes = {}
#     for filename in os.listdir(folder_path):
#         file_path = os.path.join(folder_path, filename)
#         try:
#             with Image.open(file_path) as img:
#                 img_hash = imagehash.phash(img, hash_size=hash_size)
#             # Compare with existing hashes
#             duplicate_found = False
#             for existing_hash, existing_file in hashes.items():
#                 if img_hash - existing_hash < threshold:
#                     print(f"Removing near duplicate: {img_hash - existing_hash} threshold")
#                     os.remove(file_path)
#                     duplicate_found = True
#                     break
#             if not duplicate_found:
#                 hashes[img_hash] = file_path
#         except Exception as e:
#             print(f"Error processing {file_path}: {e}")


# Remove duplicates by perceptual hashing
def remove_near_duplicates(target_folder, reference_folder=None, hash_size=16, threshold=60, llm_threshold=30):
    """
    Removes images in target_folder that are near duplicates of any image in reference_folder.
    Uses perceptual hashing via imagehash.
    """
    # Build a dictionary of perceptual hashes for images in the reference folder.
    hashes = {}

    if reference_folder:
        filenames = os.listdir(reference_folder)
        with tqdm(total=len(filenames), desc="Building Hashes of Reference Folder") as pbar:
            for filename in filenames:
                ref_path = os.path.join(reference_folder, filename)
                try:
                    with Image.open(ref_path) as img:
                        ref_hash = imagehash.phash(img, hash_size=hash_size)
                    hashes[ref_hash] = ref_path
                except Exception as e:
                    print(f"Error processing {ref_path}: {e}")
                pbar.update(1)

    # Prepare the folder to store near duplicates.
    parent_dir = os.path.dirname(target_folder)
    near_duplicate_folder = os.path.join(parent_dir, "RemovedNearDuplicate")
    os.makedirs(near_duplicate_folder, exist_ok=True)

    # Process each image in the target folder.

    
    previous_folder_size = 99999999999999
    while previous_folder_size > len(os.listdir(target_folder)): # it is very hard to edit the folder and the hash as we loop through, so instead we break after the first match, and redo untill no file is moved anymore
        filenames = os.listdir(target_folder)
        previous_folder_size = len(filenames)
        with tqdm(total=len(filenames), desc="Processing Target Folder") as pbar:
            for filename in filenames:
                file_path = os.path.join(target_folder, filename)
                try:
                    if file_path in hashes.values():
                        file_hash = list(hashes.keys())[list(hashes.values()).index(file_path)]
                    else:
                        with Image.open(file_path) as img:
                            file_hash = imagehash.phash(img, hash_size=hash_size)

                    # Compare with every hash from the reference folder.
                    duplicate_found = False
                    for existing_hash, existing_file_path in hashes.items():
                        if existing_file_path == file_path:
                            continue                        
                        
                        if file_hash - existing_hash < threshold:
                            if file_hash - existing_hash > llm_threshold:
                                prompt_list = [
                                    ("text", 'These two images have been collected from different documents. I used a hash function to find that they are near duplicates. Your job is to determine if they are really duplicates (incl. the color is the same) and which one to keep (we keep the cleanest, without artifacts, e.g. bad borders). You answer in this format: {"is_duplicates"": "yes or no", "image_to_keep": "image1 or image2"}'),
                                    ("text", '\n\nimage1:'),
                                    ("image_path", file_path),
                                    ("text", '\n\nimage2:'),
                                    ("image_path", existing_file_path),
                                ]
                                ai_answer = vertex_genai_multi(prompt_list)
                                json_answer = get_json(ai_answer)
                                if "is_duplicates" in json_answer and json_answer["is_duplicates"] == "yes":
                                    if "image_to_keep" in json_answer and "1" in json_answer["image_to_keep"]:
                                        shutil.move(file_path, os.path.join(near_duplicate_folder, filename))
                                        duplicate_found = True
                                        break
                                    elif "image_to_keep" in json_answer and "2" in json_answer["image_to_keep"]:
                                        shutil.move(existing_file_path, os.path.join(near_duplicate_folder, filename))
                                        # !!! We need to remove the hash from the hashes dictionary for existing_file_path and add file_path
                                        # This is to dangerous to change the dictionary (hashes) as we loop through, so we just edit that hash and replace it's filepath
                                        hashes[existing_hash] = file_path
                                        duplicate_found = True
                                        break
                            
                            else:
                                shutil.move(file_path, os.path.join(near_duplicate_folder, filename))
                                duplicate_found = True
                                break

                    if not duplicate_found:
                        hashes[file_hash] = file_path

                except Exception as e:
                    print(f"Error processing {file_path}: {e}")
                    continue
                
                pbar.update(1)


def classify_near_duplicates(folder_path, reference_folder=None, hash_size=16, threshold=60, llm_threshold=30):
    # Classify near-duplicate images in folders based on the similarity of the hash.

    classified_folder = os.path.basename(folder_path) + "_classified"
    parent_dir = os.path.dirname(folder_path)
    classified_folder_path = f"{parent_dir}/{classified_folder}/"

    classified_folder_path = folder_path + "_classified"
    os.makedirs(classified_folder_path, exist_ok=True)
    hashes = {}

    if reference_folder:
        filenames = os.listdir(reference_folder)
        with tqdm(total=len(filenames), desc="Building Hashes of Reference Folder") as pbar:
            for filename in filenames:
                ref_path = os.path.join(reference_folder, filename)
                try:
                    with Image.open(ref_path) as img:
                        ref_hash = imagehash.phash(img, hash_size=hash_size)
                    hashes[ref_hash] = ref_path
                except Exception as e:
                    print(f"Error processing {ref_path}: {e}")
                pbar.update(1)

    i = 1
    filenames = os.listdir(folder_path)
    with tqdm(total=len(filenames), desc="Classifying near duplicates") as pbar:
        for filename in filenames:
            file_path = os.path.join(folder_path, filename)
            try:
                with Image.open(file_path) as img:
                    img_hash = imagehash.phash(img, hash_size=hash_size)

                # Compare with existing hashes
                duplicate_found = False
                for existing_hash, existing_file in hashes.items():
                    if img_hash - existing_hash < threshold:
                        if img_hash - existing_hash > llm_threshold:
                            prompt_list = [
                                ("text", 'These two images have been collected from different documents. I used a hash function to find that they are near duplicates. Your job is to determine if they are really duplicates (incl. the color is the same) and which one to keep (we keep the cleanest, without artifacts, e.g. bad borders). You answer in this format: {"is_duplicates"": "yes or no", "image_to_keep": "image1 or image2"}'),
                                ("text", '\n\nimage1:'),
                                ("image_path", file_path),
                                ("text", '\n\nimage2:'),
                                ("image_path", existing_file),
                            ]
                            ai_answer = vertex_genai_multi(prompt_list)
                            json_answer = get_json(ai_answer)
                            if "is_duplicates" in json_answer and json_answer["is_duplicates"] == "yes":
                                if "image_to_keep" in json_answer and "1" in json_answer["image_to_keep"]:
                                    os.makedirs(f"{classified_folder_path}/{img_hash - existing_hash}/LLM_yes", exist_ok=True)
                                    shutil.copy(file_path, f"{classified_folder_path}/{img_hash - existing_hash}/LLM_yes/{i}_1_keep.webp")
                                    shutil.copy(existing_file, f"{classified_folder_path}/{img_hash - existing_hash}/LLM_yes/{i}_2_remove.webp")
                                    duplicate_found = True
                                    i += 1
                                    break
                                elif "image_to_keep" in json_answer and "2" in json_answer["image_to_keep"]:
                                    os.makedirs(f"{classified_folder_path}/{img_hash - existing_hash}/LLM_yes", exist_ok=True)
                                    shutil.copy(file_path, f"{classified_folder_path}/{img_hash - existing_hash}/LLM_yes/{i}_1_remove.webp")
                                    shutil.copy(existing_file, f"{classified_folder_path}/{img_hash - existing_hash}/LLM_yes/{i}_2_keep.webp")
                                    duplicate_found = True
                                    i += 1
                                    break
                            elif "is_duplicates" in json_answer and json_answer["is_duplicates"] == "no":
                                    os.makedirs(f"{classified_folder_path}/{img_hash - existing_hash}/LLM_no", exist_ok=True)
                                    shutil.copy(file_path, f"{classified_folder_path}/{img_hash - existing_hash}/LLM_no/{i}_1.webp")
                                    shutil.copy(existing_file, f"{classified_folder_path}/{img_hash - existing_hash}/LLM_no/{i}_2.webp")
                                    duplicate_found = True
                                    i += 1
                                    break
                            else:
                                    os.makedirs(f"{classified_folder_path}/{img_hash - existing_hash}", exist_ok=True)
                                    shutil.copy(file_path, f"{classified_folder_path}/{img_hash - existing_hash}/{i}_1.webp")
                                    shutil.copy(existing_file, f"{classified_folder_path}/{img_hash - existing_hash}/{i}_2.webp")
                                    duplicate_found = True
                                    i += 1
                                    break
                        
                        else:
                            os.makedirs(f"{classified_folder_path}/{img_hash - existing_hash}", exist_ok=True)
                            shutil.copy(file_path, f"{classified_folder_path}/{img_hash - existing_hash}/{i}_1.webp")
                            shutil.copy(existing_file, f"{classified_folder_path}/{img_hash - existing_hash}/{i}_2.webp")
                            duplicate_found = True
                            i += 1
                            break
                if not duplicate_found:
                    hashes[img_hash] = file_path
            except Exception as e:
                print(f"Error processing {file_path}: {e}")
            pbar.update(1)



def get_docket_from_filename(img_file):
    match = re.search(r'_(\d_\d+cv\d+)_', img_file)
    if match:
        docket = match.group(1).replace("cv", "-cv-").replace("_", ":")
    else:
        match = re.search(r'_(\d_\d+-cv-\d+)_', img_file)
        if match:
            docket = match.group(1).replace("_", ":")
        else:
            print(f"No docket found for {img_file}")
            return None
    while len(docket) < 13: # add missing 0 if any
        docket = docket.replace("-cv-", "-cv-0")
    return docket


def get_case_image_from_df(cases_df, docket, filename, case_type):
    for i in range(len(cases_df[cases_df['docket'] == docket])):
        try: 
            case_image = cases_df[cases_df['docket'] == docket]['images'].values[i][case_type][filename]
            case = cases_df[cases_df['docket'] == docket].iloc[i]
            break
        except:
            print(f"No case image found for {filename}. Trying next case.")
    return case, case_image




if __name__ == "__main__":
    df = get_table_from_GZ("tb_case")
    folder_path = "D:/Documents/Programing/TRO/USside/Documents/AssessFiles/all_copyrights/"
    # collect_all_copyright_images(folder_path, "copyrights")
    # remove_duplicates("D:/Documents/Programing/TRO/USside/Documents/AssessFiles/all_copyrights_nodupe/")
    # classify_near_duplicates("D:/Documents/Programing/TRO/USside/Documents/AssessFiles/all_copyrights_nodupe/")
    
    # folder_path = "D:/Documents/Programing/TRO/USside/Documents/AssessFiles/all_patents2/"
    # collect_all_patent_images(folder_path, "patents")
    # remove_patent_duplicates("D:/Documents/Programing/TRO/USside/Documents/AssessFiles/all_patents_nodupe/")
    # remove_text_from_patent_filename("D:/Documents/Programing/TRO/USside/Documents/AssessFiles/all_patents_nodupe_notext/")

    # trademark_folder = "D:/Documents/Programing/TRO/USside/Documents/AssessFiles/For AI/Trademarks/Logo - NoDupe/"
    # fix_all_trademark_images(trademark_folder, "trademarks")
