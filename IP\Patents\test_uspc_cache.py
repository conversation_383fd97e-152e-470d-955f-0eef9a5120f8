#!/usr/bin/env python3
"""
Test script for USPC definitions cache implementation.
"""

import sys
import os
import logging

# Add the current directory to the path so we can import the modules
sys.path.insert(0, os.path.dirname(__file__))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_uspc_cache():
    """Test the USPC definitions cache functionality."""
    try:
        # Import the functions
        from patent_db_uspc import load_uspc_definitions_cache, get_uspc_definition_id, validate_uspc_classification
        
        logger.info("Testing USPC definitions cache...")
        
        # Test loading the cache
        logger.info("Loading USPC definitions cache...")
        success = load_uspc_definitions_cache()
        
        if success:
            logger.info("✅ USPC cache loaded successfully!")
            
            # Test validation function
            logger.info("Testing USPC validation...")
            
            # Test with some common USPC classes (these might not exist in your database)
            test_cases = [
                ("707", "722"),  # Common computer-related class
                ("D13", "103"),  # Design patent class
                ("123", "456"),  # Likely non-existent class
                ("", ""),        # Empty values
            ]
            
            for uspc_class, uspc_subclass in test_cases:
                is_valid = validate_uspc_classification(uspc_class, uspc_subclass)
                definition_id = get_uspc_definition_id(uspc_class, uspc_subclass)
                
                logger.info(f"Class: '{uspc_class}', Subclass: '{uspc_subclass}' -> Valid: {is_valid}, ID: {definition_id}")
                
        else:
            logger.error("❌ Failed to load USPC cache")
            
    except ImportError as e:
        logger.error(f"❌ Import error: {e}")
        logger.info("This might be expected if the database connection is not available")
    except Exception as e:
        logger.error(f"❌ Unexpected error: {e}", exc_info=True)

def test_parser_integration():
    """Test the parser integration."""
    try:
        from patent_parser import validate_uspc_classification, load_uspc_definitions_cache
        
        if validate_uspc_classification and load_uspc_definitions_cache:
            logger.info("✅ Parser successfully imported USPC validation functions")
        else:
            logger.warning("⚠️ Parser could not import USPC validation functions (this might be expected)")
            
    except ImportError as e:
        logger.error(f"❌ Parser import error: {e}")
    except Exception as e:
        logger.error(f"❌ Unexpected parser error: {e}", exc_info=True)

if __name__ == "__main__":
    logger.info("Starting USPC cache tests...")
    
    test_uspc_cache()
    test_parser_integration()
    
    logger.info("Tests completed!")
