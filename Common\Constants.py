import os
import re
from dotenv import load_dotenv

loaded = load_dotenv()
# print(".env loaded: ", loaded)

LN_chrome_folder = False
if os.name == 'nt':
    chrome_user_data_dir = os.path.join(os.environ['LOCALAPPDATA'], os.environ["LN_chrome_folder"])
else:
    chrome_user_data_dir = '/app/chrome_user_data'


# Folders locations
local_case_folder=os.path.join(os.getcwd(),'Documents', 'Case Files')
local_plaintiff_folder=os.path.join(os.getcwd(), 'Documents', 'Plaintiff Files')
local_assess_folder=os.path.join(os.getcwd(), 'Documents', 'AssessFiles')
local_ip_folder=os.path.join(os.getcwd(), 'Documents', 'IP')
nas_case_folder='/Maidalv/IP TRO Data/Case Files'  # Adjust this path as needed
nas_plaintiff_folder='/Maidalv/IP TRO Data/Plaintiff Files'  # Adjust this path as needed
nas_assess_folder='/Maidalv/IP TRO Data/AssessFiles'  # Adjust this path as needed
nas_ip_folder='/Maidalv/IP TRO Data/IP'  # Adjust this path as needed

# Function to sanitize folder names by replacing prohibited characters
def sanitize_name(name):
    # Define a pattern for invalid characters
    invalid_chars_pattern = r'[<>:"/\\|?*]'
    # Replace invalid characters with an underscore
    sanitized_name = re.sub(invalid_chars_pattern, '_', name)
    
    # Additional handling for special Unicode characters that may cause issues with cv2
    # This includes registered trademark (®), trademark (™), copyright (©), etc.
    special_chars_pattern = r'[®™©℠§¶†‡±¿¡]'
    sanitized_name = re.sub(special_chars_pattern, '', sanitized_name)
    
    # Replace any other non-ASCII characters
    sanitized_name = ''.join(c if c.isascii() and c.isprintable() else '' for c in sanitized_name)
    
    # Optionally, strip trailing spaces and periods
    sanitized_name = sanitized_name.rstrip(' .')
    return sanitized_name


async def sem_task(semaphore, task):
    async with semaphore:
        return await task


# Patterns that require download
download_patterns = [
    r"COMPLAINT.*filed",
    r"AMENDED complaint",
    r"CIVIL Cover Sheet",
    r"DECLARATION of .* temporary restraining order",
    r"MEMORANDUM .* for preliminary injunction",
    r"MEMORANDUM by .* in support of motion for temporary restraining order",
    r"PRELIMINARY INJUNCTION ORDER",
    r"MEMORANDUM .* for default judgment",
    r"FINAL JUDGMENT ORDER",
    r"MOTION by Defendant .* temporary restraining order",
    r"MOTION by Defendant .* dismiss",
    r"RESPONSE by Defendant .* motion for preliminary injunction",
    r"ANSWER to Complaint",
    r"COUNTERCLAIM filed",
    r"REPLY by .* Preliminary Injunction",
    r"REPLY by .* temporary restraining order",
    r"RESPONSE by .* Preliminary Injunction",
    r"RESPONSE by .* temporary restraining order",
    r"RESPONSE by .* Asset Restraint",
    r"REPLY by .* Asset Restraint",
    r"REPLY by .* motion for order",
    r"MEMORANDUM by .* motion for order",
    r"MOTION by .* lack of jurisdiction",
    r"REPLY by .* to memorandum",
    r"Judgment",
    r"FINAL DEFAULT JUDGMENT ORDER",
    r"EX PARTE TEMPORARY RESTRAINING ORDER",
    r"SCHEDULE A by Plaintiff"
]

# Patterns that do not require download
no_download_patterns = [
    r"ATTORNEY Appearance",
    r"MAILED trademark report to Patent Trademark Office",
    r"MAILED to",
    r"NOTICE TO THE PARTIES .* Mandatory Initial Discovery Pilot",
    r"MEMORANDUM by .* in support of motion for miscellaneous relief",
    r"NOTICE of Motion by .* for presentment",
    r"AMENDED memorandum in support of motion",
    r"MINUTE entry",
    r"CORONAVIRUS",
    r"STATUS Report",
    r"MOTION .* under seal",
    r"MOTION .* to seal document",
    r"MOTION .* for preliminary injunction",
    r"MOTION .* for default judgment",
    r"MOTION .* temporary restraining order",
    r"MOTION .* electronic service of process",
    r"DECLARATION of .* Electronic Service of Process",
    r"SUMMONS Returned Executed",
    r"CIVIL BOND",
    r"CASE ASSIGNED to",
    r"Notice of Claims",
    r"NOTIFICATION of Affiliates",
    r"MOTION .* for Alternative Service",
    r"SURETY BOND",
    r"NOTICE of Voluntary Dismissal",
    r"CERTIFICATE of Service",
    r"MOTION by Defendant .* briefing schedule",
    r"MOTION .* leave to file",
    r"MOTION .* for extension of time",
    r"MAIL RETURNED",
    r"Mail sent to",
    r"MAILED.*Copyright",
    r"MAILED.*patent",
    r"MAILED.*trademark",
    r"SCHEDULING REPORT",
    r"INJUNCTION BOND",
    r"GENERAL ORDER",
    r"FORM AO 120",
    r"Extension of Time",
    r"EXECUTIVE COMMITTEE ORDER",
    r"FULL SATISFACTION of Judgment",
    r"EXTENSION OF TEMPORARY RESTRAINING ORDER",
    r"LETTER from",
    r"LETTER addressed to",
    r"DECLARATION by Plaintiff",
    r"DECLARATION of"
]


# Research: Docket Alarm: using * is not compatible with " ... "
queries = [
    '"Unincorporated Associations Identified on Schedule A"',
    '"Unincorporated Associations Identified in Schedule A"',
    '"Unincorporated Association Identified on Schedule A"',
    '"Unincorporated Association Identified in Schedule A"',
    '"Unincorporated Associations Doing Business As The Aliases Identified on Schedule A"',
    '"Unincorporated Associations Doing Business As The Aliases Identified in Schedule A"',

    '"Unicorporated Associations Identified on Schedule A"',
    '"Unicorporated Associations Identified in Schedule A"',
    '"Unicorporated Association Identified on Schedule A"',
    '"Unicorporated Association Identified in Schedule A"',
    '"Unicorporated Associations Doing Business As The Aliases Identified on Schedule A"',
    '"Unicorporated Associations Doing Business As The Aliases Identified in Schedule A"',

    '"Unincorporated Associations on Schedule A"',
    '"Unincorporated Associations in Schedule A"',
    '"Unincorporated Association on Schedule A"',
    '"Unincorporated Association in Schedule A"',
    '"Unincorporated Associations Doing Business As The Aliases on Schedule A"',
    '"Unincorporated Associations Doing Business As The Aliases in Schedule A"',

    '"Unicorporated Associations on Schedule A"',
    '"Unicorporated Associations in Schedule A"',
    '"Unicorporated Association on Schedule A"',
    '"Unicorporated Association in Schedule A"',
    '"Unicorporated Associations Doing Business As The Aliases on Schedule A"',
    '"Unicorporated Associations Doing Business As The Aliases in Schedule A"',

    '"Unincorporated Associates on Schedule A"',
    '"Unincorporated Associates in Schedule A"',
    '"Unincorporated Associates Identified on Schedule A"',
    '"Unincorporated Associates Identified in Schedule A"',
    '"Parties Identified on Schedule A"',
    '"Parties Identified in Schedule A"',
    '"Unincorporated Associations Identified on the Attached Schedule A"',
    '"Unincorporated Associations Identified in the Attached Schedule A"',
    '"Foreign Entities Identified on Schedule A"',
    '"Foreign Entities Identified in Schedule A"',
    '"Unincorporated Associations set forth on Schedule A"',
    '"Unincorporated Associations set forth in Schedule A"',
    '"Unincorporated Associations Identified on the Schedule A"',
    '"Unincorporated Associations Identified in the Schedule A"',
    '"Unincorporated Corporations Identified on Schedule A"',
    '"Unincorporated Corporations Identified in Schedule A"',

]

cases_steps_col_types = {
    'case_id': 'NUMBER',
    'step_nb': 'NUMBER',
    'download_required': 'NUMBER',
    'files_number': 'NUMBER',
    'files_downloaded': 'NUMBER',
    'files_failed': 'NUMBER',
    'step_date_filed': 'DATE',
    'create_time': 'TIMESTAMP',
    'update_time': 'TIMESTAMP',
}

cases_col_types = {
    'date_filed': 'DATE',
    'create_time': 'TIMESTAMP',
    'update_time': 'TIMESTAMP',
}

int_cl_to_description = {
    1: "Chemicals for use in industry, science and photography, as well as in agriculture, horticulture and forestry; unprocessed artificial resins, unprocessed plastics; fire extinguishing and fire prevention compositions; tempering and soldering preparations; substances for tanning animal skins and hides; adhesives for use in industry; putties and other paste fillers; compost, manures, fertilizers; biological preparations for use in industry and science.",
    2: "Paints, varnishes, lacquers; preservatives against rust and against deterioration of wood; colorants, dyes; inks for printing, marking and engraving; raw natural resins; metals in foil and powder form for use in painting, decorating, printing and art.",
    3: "Non-medicated cosmetics and toiletry preparations; non-medicated dentifrices; perfumery, essential oils; bleaching preparations and other substances for laundry use; cleaning, polishing, scouring and abrasive preparations.",
    4: "Industrial oils and greases, wax; lubricants; dust absorbing, wetting and binding compositions; fuels and illuminants; candles and wicks for lighting.",
    5: "Pharmaceuticals, medical and veterinary preparations; sanitary preparations for medical purposes; dietetic food and substances adapted for medical or veterinary use, food for babies; dietary supplements for human beings and animals; plasters, materials for dressings; material for stopping teeth, dental wax; disinfectants; preparations for destroying vermin; fungicides, herbicides.",
    6: "Common metals and their alloys, ores; metal materials for building and construction; transportable buildings of metal; non-electric cables and wires of common metal; small items of metal hardware; metal containers for storage or transport; safes.",
    7: "Machines, machine tools, power-operated tools; motors and engines, except for land vehicles; machine coupling and transmission components, except for land vehicles; agricultural implements, other than hand-operated hand tools; incubators for eggs; automatic vending machines.",
    8: "Hand tools and implements, hand-operated; cutlery; side arms, except firearms; razors.",
    9: "Scientific, research, navigation, surveying, photographic, cinematographic, audiovisual, optical, weighing, measuring, signalling, detecting, testing, inspecting, life-saving and teaching apparatus and instruments; apparatus and instruments for conducting, switching, transforming, accumulating, regulating or controlling the distribution or use of electricity; apparatus and instruments for recording, transmitting, reproducing or processing sound, images or data; recorded and downloadable media, computer software, blank digital or analogue recording and storage media; mechanisms for coin-operated apparatus; cash registers, calculating devices; computers and computer peripheral devices; diving suits, divers’ masks, ear plugs for divers, nose clips for divers and swimmers, gloves for divers, breathing apparatus for underwater swimming; fire-extinguishing apparatus.",
    10: "Surgical, medical, dental and veterinary apparatus and instruments; artificial limbs, eyes and teeth; orthopaedic articles; suture materials; therapeutic and assistive devices adapted for the disabled; massage apparatus; apparatus, devices and articles for nursing infants; sexual activity apparatus, devices and articles.",
    11: "Apparatus and installations for lighting, heating, cooling, steam generating, cooking, drying, ventilating, water supply and sanitary purposes.",
    12: "Vehicles; apparatus for locomotion by land, air or water.",
    13: "Firearms; ammunition and projectiles; explosives; fireworks.",
    14: "Precious metals and their alloys; jewellery, precious and semi-precious stones; horological and chronometric instruments.",
    15: "Musical instruments; music stands and stands for musical instruments; conductors’ batons.",
    16: "Paper and cardboard; printed matter; bookbinding material; photographs; stationery and office requisites, except furniture; adhesives for stationery or household purposes; drawing materials and materials for artists; paintbrushes; instructional and teaching materials; plastic sheets, films and bags for wrapping and packaging; printers’ type, printing blocks.",
    17: "Unprocessed and semi-processed rubber, gutta-percha, gum, asbestos, mica and substitutes for all these materials; plastics and resins in extruded form for use in manufacture; packing, stopping and insulating materials; flexible pipes, tubes and hoses, not of metal.",
    18: "Leather and imitations of leather; animal skins and hides; luggage and carrying bags; umbrellas and parasols; walking sticks; whips, harness and saddlery; collars, leashes and clothing for animals.",
    19: "Materials, not of metal, for building and construction; rigid pipes, not of metal, for building; asphalt, pitch, tar and bitumen; transportable buildings, not of metal; monuments, not of metal.",
    20: "Furniture, mirrors, picture frames; containers, not of metal, for storage or transport; unworked or semi-worked bone, horn, whalebone or mother-of-pearl; shells; meerschaum; yellow amber.",
    21: "Household or kitchen utensils and containers; cookware and tableware, except forks, knives and spoons; combs and sponges; brushes, except paintbrushes; brush-making materials; articles for cleaning purposes; unworked or semi-worked glass, except building glass; glassware, porcelain and earthenware.",
    22: "Ropes and string; nets; tents and tarpaulins; awnings of textile or synthetic materials; sails; sacks for the transport and storage of materials in bulk; padding, cushioning and stuffing materials, except of paper, cardboard, rubber or plastics; raw fibrous textile materials and substitutes therefor.",
    23: "Yarns and threads for textile use.",
    24: "Textiles and substitutes for textiles; household linen; curtains of textile or plastic.",
    25: "Clothing, footwear, headwear.",
    26: "Lace, braid and embroidery, and haberdashery ribbons and bows; buttons, hooks and eyes, pins and needles; artificial flowers; hair decorations; false hair.",
    27: "Carpets, rugs, mats and matting, linoleum and other materials for covering existing floors; wall hangings, not of textile.",
    28: "Games, toys and playthings; video game apparatus; gymnastic and sporting articles; decorations for Christmas trees.",
    29: "Meat, fish, poultry and game; meat extracts; preserved, frozen, dried and cooked fruits and vegetables; jellies, jams, compotes; eggs; milk, cheese, butter, yoghurt and other milk products; oils and fats for food.",
    30: "Coffee, tea, cocoa and artificial coffee; rice, pasta and noodles; tapioca and sago; flour and preparations made from cereals; bread, pastries and confectionery; chocolate; ice cream, sorbets and other edible ices; sugar, honey, treacle; yeast, baking-powder; salt, seasonings, spices, preserved herbs; vinegar, sauces and other condiments; ice (frozen water).",
    31: "Raw and unprocessed agricultural, aquacultural, horticultural and forestry products; raw and unprocessed grains and seeds; fresh fruits and vegetables, fresh herbs; natural plants and flowers; bulbs, seedlings and seeds for planting; live animals; foodstuffs and beverages for animals; malt.",
    32: "Beers; non-alcoholic beverages; mineral and aerated waters; fruit beverages and fruit juices; syrups and other non-alcoholic preparations for making beverages.",
    33: "Alcoholic beverages, except beers; alcoholic preparations for making beverages.",
    34: "Tobacco and tobacco substitutes; cigarettes and cigars; electronic cigarettes and oral vaporizers for smokers; smokers’ articles; matches.",
    35: "Advertising; business management; business administration; office functions.",
    36: "Insurance; financial affairs; monetary affairs; real estate affairs.",
    37: "Building construction; repair; installation services.",
    38: "Telecommunications.",
    39: "Transport; packaging and storage of goods; travel arrangement.",
    40: "Treatment of materials.",
    41: "Education; providing of training; entertainment; sporting and cultural activities.",
    42: "Scientific and technological services and research and design relating thereto; industrial analysis and industrial research services; design and development of computer hardware and software.",
    43: "Services for providing food and drink; temporary accommodation.",
    44: "Medical services; veterinary services; hygienic and beauty care for human beings or animals; agriculture, horticulture and forestry services.",
    45: "Legal services; security services for the physical protection of tangible property and individuals; personal and social services rendered by others to meet the needs of individuals."
}


# court_mapping = {
#     'United States District Court, Illinois Northern': 'Illinois Northern District Court',
#     'US District Court for the Northern District of Illinois': 'Illinois Northern District Court',
#     'United States District Court, Florida Southern': 'Florida Southern District Court',
#     'US District Court for the Southern District of Florida': 'Florida Southern District Court',
#     'United States District Court, Wisconsin Eastern': 'Wisconsin Eastern District Court',
#     'US District Court for the Eastern District of Wisconsin': 'Wisconsin Eastern District Court',
#     'United States District Court, Florida Middle': 'Florida Middle District Court',
#     'US District Court for the Middle District of Florida': 'Florida Middle District Court',
#     'United States District Court, Texas Western': 'Texas Western District Court',
#     'US District Court for the Western District of Texas': 'Texas Western District Court',
#     'United States District Court, Virginia Eastern': 'Virginia Eastern District Court',
#     'US District Court for the Eastern District of Virginia': 'Virginia Eastern District Court',
#     'United States District Court, New York Southern': 'New York Southern District Court',
#     'US District Court for the Southern District of New York': 'New York Southern District Court',
#     'United States District Court, New York Western': 'New York Western District Court',
#     'US District Court for the Western District of New York': 'New York Western District Court',
#     'United States District Court, Missouri Western': 'Missouri Western District Court',
#     'US District Court for the Western District of Missouri': 'Missouri Western District Court',
#     'United States District Court, Colorado': 'Colorado District Court',
#     'US District Court for the District of Colorado': 'Colorado District Court',
#     'United States District Court, New York Eastern': 'New York Eastern District Court',
#     'US District Court for the Eastern District of New York': 'New York Eastern District Court',
#     'United States District Court, California Central': 'California Central District Court',
#     'US District Court for the Central District of California': 'California Central District Court',
#     'United States District Court, Pennsylvania Eastern': 'Pennsylvania Eastern District Court',
#     'US District Court for the Eastern District of Pennsylvania': 'Pennsylvania Eastern District Court',
#     'United States District Court, Massachusetts': 'Massachusetts District Court',
#     'US District Court for the District of Massachusetts': 'Massachusetts District Court',
#     'United States District Court, Hawaii': 'Hawaii District Court',
#     'US District Court for the District of Hawaii': 'Hawaii District Court',
#     'United States District Court, Illinois Southern': 'Illinois Southern District Court',
#     'US District Court for the Southern District of Illinois': 'Illinois Southern District Court',
#     'United States District Court, Rhode Island': 'Rhode Island District Court',
#     'US District Court for the District of Rhode Island': 'Rhode Island District Court',
#     'United States District Court, Minnesota': 'Minnesota District Court',
#     'US District Court for the District of Minnesota': 'Minnesota District Court',
#     'United States District Court, Texas Eastern': 'Texas Eastern District Court',
#     'US District Court for the Eastern District of Texas': 'Texas Eastern District Court',
#     'United States District Court, Nevada': 'Nevada District Court',
#     'US District Court for the District of Nevada': 'Nevada District Court',
#     'United States District Court, Tennessee Western': 'Tennessee Western District Court',
#     'US District Court for the Western District of Tennessee': 'Tennessee Western District Court',
#     'United States District Court, North Carolina Western': 'North Carolina Western District Court',
#     'US District Court for the Western District of North Carolina': 'North Carolina Western District Court',
#     'United States District Court, Washington Western': 'Washington Western District Court',
#     'US District Court for the Western District of Washington': 'Washington Western District Court',
#     'United States District Court, Indiana Southern': 'Indiana Southern District Court',
#     'US District Court for the Southern District of Indiana': 'Indiana Southern District Court',
#     'United States District Court, Ohio Northern': 'Ohio Northern District Court',
#     'US District Court for the Northern District of Ohio': 'Ohio Northern District Court',
#     'United States District Court, Delaware': 'Delaware District Court',
#     'US District Court for the District of Delaware': 'Delaware District Court',
#     'United States District Court, Georgia Northern': 'Georgia Northern District Court',
#     'US District Court for the Northern District of Georgia': 'Georgia Northern District Court',
#     'United States District Court, New York Northern': 'New York Northern District Court',
#     'US District Court for the Northern District of New York': 'New York Northern District Court',
#     'United States District Court, Tennessee Middle': 'Tennessee Middle District Court',
#     'US District Court for the Tennessee Middle District': 'Tennessee Middle District Court',
#     'United States District Court, Illinois Central': 'Illinois Central District Court',
#     'US District Court for the Illinois Central District': 'Illinois Central District Court',
#     'United States District Court, California Southern': 'California Southern District Court',
#     'US District Court for the California Southern District': 'California Southern District Court',
#     'United States District Court, Pennsylvania Middle': 'Pennsylvania Middle District Court',
#     'US District Court for the Pennsylvania Middle District': 'Pennsylvania Middle District Court',
#     'United States District Court, Georgia Middle': 'Georgia Middle District Court',
#     'US District Court for the Georgia Middle District': 'Georgia Middle District Court',
#     'United States District Court, Missouri Eastern': 'Missouri Eastern District Court',
#     'US District Court for the Eastern District of Missouri': 'Missouri Eastern District Court',
#     'United States District Court, New Jersey': 'New Jersey District Court',
#     'US District Court for the District of New Jersey': 'New Jersey District Court',
#     'United States District Court, New Mexico': 'New Mexico District Court',
#     'US District Court for the District of New Mexico': 'New Mexico District Court',
#     'United States District Court, Texas Southern': 'Texas Southern District Court',
#     'US District Court for the Southern District of Texas': 'Texas Southern District Court',
# }



STATES = [
    'Alabama', 'Alaska', 'Arizona', 'Arkansas', 'California', 'Colorado',
    'Connecticut', 'Delaware', 'Florida', 'Georgia', 'Hawaii', 'Idaho',
    'Illinois', 'Indiana', 'Iowa', 'Kansas', 'Kentucky', 'Louisiana',
    'Maine', 'Maryland', 'Massachusetts', 'Michigan', 'Minnesota',
    'Mississippi', 'Missouri', 'Montana', 'Nebraska', 'Nevada', 'New Hampshire',
    'New Jersey', 'New Mexico', 'New York', 'North Carolina', 'North Dakota',
    'Ohio', 'Oklahoma', 'Oregon', 'Pennsylvania', 'Rhode Island',
    'South Carolina', 'South Dakota', 'Tennessee', 'Texas', 'Utah',
    'Vermont', 'Virginia', 'Washington', 'West Virginia', 'Wisconsin',
    'Wyoming', 'District of Columbia', 'Puerto Rico'
]

DISTRICTS = ['Northern', 'Southern', 'Eastern', 'Western', 'Central', 'Middle']

court_mapping = {}
for state in STATES:
    # Add non-district entries
    court_mapping[f'United States District Court, {state}'] = f'{state} District Court'
    court_mapping[f'US District Court for the District of {state}'] = f'{state} District Court'
    
    # Add district variations
    for district in DISTRICTS:
        district_cap = district.capitalize()
        court_mapping[f'United States District Court, {state} {district_cap}'] = f'{state} {district_cap} District Court'
        court_mapping[f'US District Court for the {district_cap} District of {state}'] = f'{state} {district_cap} District Court'

STATE_ABBREVIATIONS = {
    'Alabama': 'AL', 'Alaska': 'AK', 'Arizona': 'AZ', 'Arkansas': 'AR', 'California': 'CA',
    'Colorado': 'CO', 'Connecticut': 'CT', 'Delaware': 'DE', 'Florida': 'FL', 'Georgia': 'GA',
    'Hawaii': 'HI', 'Idaho': 'ID', 'Illinois': 'IL', 'Indiana': 'IN', 'Iowa': 'IA',
    'Kansas': 'KS', 'Kentucky': 'KY', 'Louisiana': 'LA', 'Maine': 'ME', 'Maryland': 'MD',
    'Massachusetts': 'MA', 'Michigan': 'MI', 'Minnesota': 'MN', 'Mississippi': 'MS',
    'Missouri': 'MO', 'Montana': 'MT', 'Nebraska': 'NE', 'Nevada': 'NV', 'New Hampshire': 'NH',
    'New Jersey': 'NJ', 'New Mexico': 'NM', 'New York': 'NY', 'North Carolina': 'NC',
    'North Dakota': 'ND', 'Ohio': 'OH', 'Oklahoma': 'OK', 'Oregon': 'OR', 'Pennsylvania': 'PA',
    'Rhode Island': 'RI', 'South Carolina': 'SC', 'South Dakota': 'SD', 'Tennessee': 'TN',
    'Texas': 'TX', 'Utah': 'UT', 'Vermont': 'VT', 'Virginia': 'VA', 'Washington': 'WA',
    'West Virginia': 'WV', 'Wisconsin': 'WI', 'Wyoming': 'WY',
    'District of Columbia': 'DC', 'Puerto Rico': 'PR'
}

# Add mapping for variations like "Illinois Northern District Court" back to the state name "Illinois"
# This helps extract the state name reliably after standardization.
STATE_NAME_FROM_COURT = {std_name: state for state in STATES for std_name in [f'{state} District Court'] + [f'{state} {dist} District Court' for dist in DISTRICTS]}
# Add District of Columbia and Puerto Rico explicitly
STATE_NAME_FROM_COURT['District of Columbia District Court'] = 'District of Columbia'
STATE_NAME_FROM_COURT['Puerto Rico District Court'] = 'Puerto Rico'


DISTRICT_INITIALS = {
    'Northern': 'n', 'Southern': 's', 'Eastern': 'e', 'Western': 'w', 'Central': 'c', 'Middle': 'm'
}
