/* Base Styles */
:root {
    --background-color: #242424;
    --card-background: #2e2e2e;
    --image-box-background: #383838;
    --text-color: #f0f0f0;
    --text-secondary: #b0b0b0;
    --accent-color: #3f6dc2;
    --accent-hover: #4f7ed2;
    --error-color: #e74c3c;
    --success-color: #2ecc71;
    --warning-color: #f39c12;
    --border-radius: 8px;
    --shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 12px;
    --spacing-lg: 16px;
    --heading-size: 14px;
    --text-size: 12px;
    --detail-size: 10px;
    --overlay-background: rgba(0, 0, 0, 0.75);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    background-color: var(--background-color);
    color: var(--text-color);
    line-height: 1.4;
    font-size: var(--text-size);
}

/* Layout */
.app-container {
    max-width: 100%;
    margin: 0 auto;
    padding: var(--spacing-md);
}

header {
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid #444;
}

header h1 {
    font-size: 1.5rem;
    font-weight: 500;
}

/* Main Menu */
.main-menu {
    background-color: var(--card-background);
    border-radius: var(--border-radius);
    padding: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
    box-shadow: var(--shadow);
}

.menu-items {
    display: flex;
    list-style-type: none;
}

.menu-item {
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius);
    transition: background-color 0.2s;
}

.menu-item a {
    text-decoration: none;
    color: var(--text-color);
    font-weight: 500;
    font-size: 14px;
}

.menu-item:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.menu-item.active {
    background-color: var(--accent-color);
}

.menu-item.active a {
    color: white;
}

/* Filters */
.filters-container {
    background-color: var(--card-background);
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    box-shadow: var(--shadow);
}

.filters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(135px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.filter-group {
    display: flex;
    flex-direction: column;
}

.filter-group label {
    margin-bottom: var(--spacing-xs);
    font-size: var(--heading-size);
    font-weight: 500;
}

input, select {
    background-color: #1a1a1a;
    border: 1px solid #444;
    border-radius: 4px;
    padding: var(--spacing-sm);
    color: var(--text-color);
    font-size: var(--text-size);
    width: 100%;
}

input:focus, select:focus {
    outline: none;
    border-color: var(--accent-color);
}

/* Multi-select styling */
select[multiple] {
    height: 80px;
    overflow-y: auto;
}

select[multiple] option {
    padding: var(--spacing-xs) var(--spacing-sm);
}

select[multiple] option:checked {
    background-color: rgba(63, 109, 194, 0.5);
    color: white;
}

.actions-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: var(--spacing-md);
}

.action-buttons {
    display: flex;
    gap: var(--spacing-md);
}

.results-info {
    color: var(--text-secondary);
    font-size: var(--text-size);
}

.btn {
    padding: var(--spacing-sm) var(--spacing-lg);
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.2s, transform 0.1s;
}

.btn:active {
    transform: translateY(1px);
}

.primary {
    background-color: var(--accent-color);
    color: white;
}

.primary:hover {
    background-color: var(--accent-hover);
}

.secondary {
    background-color: #444;
    color: white;
}

.secondary:hover {
    background-color: #555;
}

.btn:disabled {
    background-color: #333;
    color: #777;
    cursor: not-allowed;
}

/* Cases container */
.cases-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.case-card {
    background-color: var(--card-background);
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    box-shadow: var(--shadow);
    transform: translateZ(0);
    transition: transform 0.2s;
}

.case-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
}

.case-title-container {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-grow: 1;
}

.case-title {
    font-size: var(--heading-size);
    font-weight: 600;
    margin-bottom: 0;
    flex-grow: 1;
}

.proposed-name {
    color: var(--accent-color);
    font-style: italic;
    font-weight: normal;
    font-size: 12px;
}

/* Improve Plaintiff Button in Title */
.case-title-container .improve-plaintiff-btn {
    width: 24px;
    height: 24px;
    min-width: 24px;
    font-size: 10px;
    padding: 0;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--accent-color);
    color: white;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    margin-left: 4px;
}

.case-title-container .improve-plaintiff-btn:hover {
    background-color: var(--accent-hover);
    transform: scale(1.1);
}

.case-title-container .improve-plaintiff-btn.success {
    background-color: var(--success-color);
}

.case-title-container .improve-plaintiff-btn.error {
    background-color: var(--error-color);
}

.validation-icons {
    display: flex;
    gap: var(--spacing-xs);
    align-items: center;
}

.validation-icon {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    background-color: #444;
    color: #777;
    font-size: 12px;
    transition: all 0.2s ease;
}

.validation-icon.active.validated {
    background-color: var(--success-color);
    color: white;
}

.validation-icon.active.review {
    background-color: var(--warning-color);
    color: white;
}

.validation-icon.active.failed {
    background-color: var(--error-color);
    color: white;
}

.validation-icon:hover {
    transform: scale(1.1);
}

.validation-icon i {
    font-size: 12px;
}

.case-link {
    cursor: pointer;
    text-decoration: none;
    color: var(--text-color);
    position: relative;
}

.case-link:hover::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100%;
    height: 1px;
    background-color: var(--text-color);
}


.ip-section {
    margin-top: var(--spacing-md);
}

.ip-title {
    font-size: var(--heading-size);
    margin-bottom: var(--spacing-md);
    border-bottom: 1px solid #444;
    padding-bottom: var(--spacing-xs);
    display: flex;
    justify-content: space-between;
}

.ip-title-text {
    margin-right: var(--spacing-sm);
}

.ip-status {
    font-size: var(--detail-size);
    color: var(--text-secondary);
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xs);
}

.ip-status-item {
    background-color: rgba(255, 255, 255, 0.1);
    padding: 2px 6px;
    border-radius: 4px;
    white-space: nowrap;
}

.ip-status-item-count {
    font-weight: bold;
}

.ip-items {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: var(--spacing-md);
}

.ip-item {
    background-color: var(--image-box-background);
    border-radius: 4px;
    padding: var(--spacing-sm);
    height: 100%;
}

.ip-item-image {
    width: 100%;
    height: 140px;
    object-fit: contain;
    margin-bottom: var(--spacing-sm);
    background-color: #222;
    border-radius: 4px;
}

.ip-item-info {
    font-size: var(--detail-size);
}

.ip-item-info div {
    margin-bottom: var(--spacing-xs);
}

.ip-item-label {
    color: var(--text-secondary);
    font-weight: 500;
    margin-right: 4px; /* Add a small space between label and value */
}

/* Pagination */
.pagination-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: var(--spacing-lg);
    margin-top: var(--spacing-lg);
}

/* Loading */
.loading {
    text-align: center;
    padding: var(--spacing-lg);
    color: var(--text-secondary);
}

/* No results */
.no-results {
    text-align: center;
    padding: var(--spacing-lg);
    color: var(--text-secondary);
}

/* Small screens */
@media (max-width: 768px) {
    .filters-grid {
        grid-template-columns: 1fr;
    }

    .case-info {
        grid-template-columns: 1fr;
    }

    .ip-items {
        grid-template-columns: 1fr;
    }

    .actions-row {
        flex-direction: column;
        gap: var(--spacing-md);
    }
}

/* Medium screens */
@media (min-width: 769px) and (max-width: 1024px) {
    .ip-items {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }
}

/* Filter case type checkboxes */
.filter-case-types {
    margin-bottom: var(--spacing-sm);
}

.checkbox-container {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xs);
}

.checkbox-label {
    display: flex;
    align-items: center;
    font-size: var(--text-size);
    cursor: pointer;
    margin-right: var(--spacing-sm);
}

.checkbox-label input[type="checkbox"] {
    width: auto;
    margin-right: var(--spacing-xs);
}

/* Dropdown checkbox menu */
.dropdown-checkbox {
    position: relative;
    display: inline-block;
    width: 100%;
}

.dropdown-checkbox-header {
    background-color: #1a1a1a;
    border: 1px solid #444;
    border-radius: 4px;
    padding: var(--spacing-sm);
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dropdown-checkbox-content {
    display: none;
    position: absolute;
    background-color: #1a1a1a;
    min-width: 100%;
    box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.5);
    padding: var(--spacing-sm);
    z-index: 1;
    border-radius: 4px;
    border: 1px solid #444;
    max-height: 200px;
    overflow-y: auto;
}

.dropdown-checkbox-content.show {
    display: block;
}

.dropdown-checkbox-item {
    padding: var(--spacing-xs) 0;
}





.case-details-row {
    display: flex;
    justify-content: space-between; /* Pushes left and right content apart */
    align-items: flex-start; /* Align items at the top */
    flex-wrap: wrap; /* Allow wrapping on smaller screens */
    gap: 10px; /* Add some space between rows if wrapped */
    margin-bottom: 10px; /* Space below the details row */
    border-bottom: 1px solid #eee; /* Optional separator */
    padding-bottom: 10px; /* Optional padding */
}

.case-info-left {
    display: flex;
    flex-wrap: wrap; /* Allow info items to wrap */
    gap: 5px 15px; /* Row gap, Column gap */
    flex-grow: 1; /* Allow left side to take available space */
}

.case-info-right {
    display: flex;
    gap: 15px; /* Space between the Langfuse and File links */
    flex-shrink: 0; /* Prevent right side from shrinking */
    white-space: nowrap; /* Keep links on one line */
    margin-left: 20px; /* Ensure some space from the left content */
}

.action-link {
    color: #007bff;
    text-decoration: none;
    font-weight: 500;
}

.action-link:hover {
    text-decoration: underline;
}

.action-link.disabled {
    color: #6c757d;
    pointer-events: none; /* Make it unclickable */
    cursor: default;
    opacity: 0.7;
}

.info-item {
  margin-bottom: 0; /* Reset margin if previously set */
  display: flex; /* Use flexbox for label and value */
  align-items: center; /* Align items vertically */
}

.info-label {
  font-weight: 500;
  color: var(--text-secondary);
  margin-right: 4px; /* Add a small space between label and value */
}

.info-value {
  text-align: left;
}

.case-description {
    margin-top: 10px; /* Add space above description */
    clear: both; /* Ensure it appears below floated elements if any */
}

/* Update Case Button & Overlay */
.update-case-btn {
    background-color: var(--accent-color);
    color: white;
    border: none;
    border-radius: 50%;
    width: 28px;
    height: 28px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
    transition: background-color 0.2s;
}

.update-case-btn:hover {
    background-color: var(--accent-hover);
}

.overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--overlay-background);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.overlay-content {
    background-color: var(--card-background);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    width: 500px;
    max-width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: var(--shadow);
}

.overlay-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid #444;
}

.overlay-title {
    font-size: 18px;
    font-weight: 600;
}

.close-overlay {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    font-size: 20px;
}

.close-overlay:hover {
    color: var(--text-color);
}

.overlay-body {
    margin-bottom: var(--spacing-lg);
}

.option-group {
    margin-bottom: var(--spacing-md);
}

.option-group-title {
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
}

.radio-option, .checkbox-option {
    display: flex;
    align-items: flex-start;
    margin-bottom: var(--spacing-xs);
    padding: var(--spacing-xs);
    border-radius: 4px;
}

.radio-option:hover, .checkbox-option:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

.radio-option input[type="radio"],
.checkbox-option input[type="checkbox"] {
    margin-right: var(--spacing-sm);
    width: auto;
    margin-top: 2px;
}

.option-label {
    display: flex;
    flex-direction: column;
}

.option-text {
    font-weight: 500;
}

.option-description {
    font-size: var(--detail-size);
    color: var(--text-secondary);
}

.file-type-options {
    margin-left: 20px;
    padding-left: var(--spacing-sm);
    border-left: 2px solid #444;
}

.disabled {
    opacity: 0.4;
    pointer-events: none;
    background-color: rgba(0, 0, 0, 0.1);
}

.overlay-footer {
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-md);
}

/* Progress Log */
.progress-log {
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 4px;
    padding: var(--spacing-sm);
    margin: var(--spacing-md) 0;
    height: 160px;
    overflow-y: auto;
    resize: vertical;
    position: relative;
}

.progress-log-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 4px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    margin-bottom: 4px;
}

.progress-log-content {
    font-family: monospace;
    font-size: 12px;
    white-space: pre-wrap;
}

.close-log {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    font-size: 14px;
    padding: 0;
}

.close-log:hover {
    color: var(--text-color);
}

/* Steps Section Styles */
.steps-info-item {
    cursor: pointer;
    position: relative;
}

.steps-info-item:hover .info-label {
    text-decoration: underline;
}

.steps-section {
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 4px;
    padding: var(--spacing-sm);
    margin: var(--spacing-md) 0;
    height: 300px; /* Initial height */
    max-height: 80vh; /* Maximum height when expanded */
    overflow-y: auto;
    resize: vertical;
    position: relative;
}

.steps-section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 4px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    margin-bottom: 8px;
}

.steps-section-title {
    font-weight: 600;
    font-size: 14px;
}

.close-steps {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    font-size: 14px;
    padding: 0;
}

.close-steps:hover {
    color: var(--text-color);
}

.steps-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 12px;
}

.steps-table th,
.steps-table td {
    text-align: left;
    padding: 6px 8px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.steps-table th {
    font-weight: 600;
    color: var(--text-color);
    background-color: rgba(0, 0, 0, 0.2);
}

.steps-table tr:nth-child(even) {
    background-color: rgba(255, 255, 255, 0.02);
}

.steps-table tr:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

/* Adjust table columns width */
.steps-table th:nth-child(1), /* Date Filed */
.steps-table td:nth-child(1) {
    width: 100px; /* 80% wider than default */
}

.steps-table th:nth-child(7), /* Last Updated */
.steps-table td:nth-child(7) {
    width: 90px; /* 20% wider than default */
}

/* Make proceeding text columns take available space */
.steps-table th:nth-child(3),
.steps-table td:nth-child(3),
.steps-table th:nth-child(4),
.steps-table td:nth-child(4) {
    min-width: 200px;
}

.steps-count {
    color: var(--text-color);
}

.steps-count.duplicate {
    color: #ff5555; /* Red color for duplicate step numbers */
}

/* Review Plaintiff Styles */
.review-container {
    margin-top: var(--spacing-lg);
}

.review-table {
    width: 100%;
    border-collapse: collapse;
    background-color: var(--card-background);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
    font-size: var(--text-size);
    margin-bottom: var(--spacing-lg);
}

.review-table th,
.review-table td {
    text-align: left;
    padding: 10px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.review-table th {
    background-color: rgba(0, 0, 0, 0.2);
    font-weight: 600;
    color: var(--text-color);
    position: sticky;
    top: 0;
    z-index: 10;
}

.review-table tr:nth-child(even) {
    background-color: rgba(255, 255, 255, 0.02);
}

.review-table tr:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

.plaintiff-names-cell {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.plaintiff-names-cell:hover {
    overflow: visible;
    white-space: normal;
    background-color: var(--card-background);
    position: relative;
    z-index: 20;
    box-shadow: var(--shadow);
    border-radius: var(--border-radius);
    padding: 5px;
}

.action-cell {
    display: flex;
    gap: var(--spacing-md);
    align-items: center;
}

.radio-option {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.radio-option input[type="radio"] {
    margin-right: 4px;
    cursor: pointer;
}

.approve-option label {
    color: var(--success-color);
}

.reject-option label {
    color: var(--error-color);
}

.proposed-name-cell {
    font-weight: 500;
    color: var(--accent-color);
}

.truncate {
    max-width: 150px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.truncate:hover {
    overflow: visible;
    white-space: normal;
    background-color: var(--card-background);
    position: relative;
    z-index: 20;
    box-shadow: var(--shadow);
    border-radius: var(--border-radius);
    padding: 5px;
}

/* Improve Plaintiff Button */
.plaintiff-info-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.improve-plaintiff-btn {
    background-color: var(--accent-color);
    color: white;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    transition: all 0.2s ease-in-out;
}

.improve-plaintiff-btn:hover {
    background-color: var(--accent-hover);
    transform: scale(1.1);
}

.improve-plaintiff-btn.success {
    background-color: var(--success-color);
}

.improve-plaintiff-btn.error {
    background-color: var(--error-color);
}

.info-value {
    word-break: break-word;
}

.info-value .proposed-name {
    color: var(--accent-color);
    font-style: italic;
}