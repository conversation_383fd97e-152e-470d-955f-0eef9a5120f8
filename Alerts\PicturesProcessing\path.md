For each case
    for each step folder
        For each pdf
            Ignore industry report
            OCR the pdf
            Look for keywords in OCR text that indicate some IP (either Trademark works, Copyright words or Patent words):
                Get trademarks from the PDF:
                    OCR to get the reg nb (or for one formart the serial number / application number)
                    Get the data from USPTO (it currently fails with serial numbers!): 
                        Trademark info from XML, or from ... if unavaible
                        Image from URL, or from status (?) zip
                        Certificate from ?
                    If any info cannot be retrieved from USPTO: use OCR as a fallback
                Get copyrights from the PDF:
                    .....
                Get patents from the PDF:
                    LLM to get the reg nbs
                    Patent-> Get patents using registration numbers
            If we found IP type same as the nos_description: stop

    If we did not find IP type same as nos_description (or we found copyright certificated but no pictures):
        Send all the PDFs to the AI, and ask for the registration numbers of IP:
            Get trademarks using registration numbers (using API):
                ....
            Get copyrights using registration numbers:
                .....
            Patent-> Get patents using registration numbers


    If we did not find IP type same as nos_description:
        Use the plaintiff name to search nos_description IP:
            For trademark:
                Search uspto website (uc) using "Owner" field (filter out dead and pending trademarks)
                Get the list of serial_number (== application number) for all the trademarks (registration number is not available on result page)
                Get trademarks using serial numbers (using API):
                    ....
            For copyright:
                ....
            For patents:
                Patent-> Search by Name (using quasi API)
                Patent-> Get patents using registration numbers
                Ask LLM to select the best Patents (max 20) based on the Complaint PDF
                Process these patents PDFs:
                    Extract Design pages
                    Extract the Certificate page
                    Store all the information in the case_df dataframe




Patent -> Get patents using registration numbers (using quasi API):
    For each patent reg number: (Registration number needs to be only the middle part (********) of the document_id (US D7,844,454 S)) 
        If the reg number is in Database
            get patent (html and pdf) from NAS
            get data from html
            add plaintiff id in database
        If the reg number is not in database:
            get patent (html and pdf) from USPTO (using reg number)
            get data from html
            Ask LLM which are the design pages  (should be store these in the database to save time?)
            send to NAS
            save to Database
    Process these patents PDFs:
        Extract Design pages
        Extract the Certificate page
        Store all the information in the case_df dataframe

Patent -> Search by Name (using quasi API):
    Get Assignee, Applicant, Inventor using LLM and the complaint

    Search name is always limited to the first 2 words, and excludes Chinese city names

    Patent Search:
        Try 1: Assignee from AI in Patent Search
            Using granted patents:
                if more than 20 results: rerun the search taking only Design patents
            If no results in Granted Patents => look in applied patents
                if more than 20 results: rerun the search taking only Design patents
        Try 2: ⁠Applicant from AI in Patent Search

        same sub logic as Assignee 
        ⁠Inventor from AI in Patent Search

        same sub logic as Assignee 
        Plantiff name in Patent Search

        same sub logic as Assignee 
    If no result from Patent Search -> Assignee Search:
        ⁠Try 1: Assignee from AI in Assignee Search
            i⁠f more than 20 results: only keep the design patent
        Try 2: ⁠Plaintiff Name in Assignee Search
            if more than 20 results: only keep the design patent

