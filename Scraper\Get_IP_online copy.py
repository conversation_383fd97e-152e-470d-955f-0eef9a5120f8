import asyncio, re, json, aiohttp, os, sys, mimetypes
sys.path.append(os.getcwd())
from bs4 import BeautifulSoup
from markdownify import markdownify
from AI.GC_VertexAI import vertex_genai_multi_async, vertex_genai_image_gen_async
from AI.LLM_shared import get_json
from Check.Do_Check_Download import download_from_url
from Common.Constants import sanitize_name

def format_case_number(case_number: str) -> list[str]:
    """
    Generates different formats for the case number.
    Input: e.g., "1:25-cv-00097"
    Output: ["25-cv-00097", "25-cv-97", "25cv97"]
    """
    match = re.match(r'\d+:(\d+)-([a-zA-Z]+)-(\d+)', case_number)
    if not match:
        # Handle cases where the input format might be different or invalid
        # For now, return empty list or raise error? Let's return empty for robustness.
        print(f"Warning: Could not parse case number format: {case_number}")
        return []

    part1 = match.group(1)
    part2 = match.group(2)
    part3 = match.group(3)
    part3_short = str(int(part3)) # Remove leading zeros

    formats = [
        f"{part1}-{part2}-{part3}",
        f"{part1}-{part2}-{part3_short}",
        f"{part1}{part2}{part3_short}"
    ]
    return formats

async def fetch_url(session: aiohttp.ClientSession, url: str) -> str | None:
    """Fetches content from a given URL."""
    try:
        async with session.get(url, timeout=40) as response:
            response.raise_for_status() # Raise an exception for bad status codes
            return await response.text()
    except aiohttp.ClientError as e:
        print(f"Error fetching {url}: {e}")
        return None
    except asyncio.TimeoutError:
        print(f"Timeout fetching {url}")
        return None

async def find_target_urls(session: aiohttp.ClientSession, search_url_template: str, case_formats: list[str]) -> list[str]:
    """Searches case formats on a website and extracts target article URLs."""
    target_urls = set() # Use a set to avoid duplicates

    for case_format in case_formats:
        search_url = search_url_template.replace("xxxx", case_format)
        print(f"Searching: {search_url}")
        content = await fetch_url(session, search_url)
        if not content:
            continue

        soup = BeautifulSoup(content, 'html.parser')

        # Apply website-specific URL patterns using more direct selectors
        if "10100.com" in search_url_template:
            # Find links directly matching the pattern /article/\d+
            links = soup.find_all('a', href=re.compile(r'^/article/\d+'))
            for link in links:
                # Construct absolute URL if needed (assuming relative links)
                base_url = "https://www.10100.com" # Define base URL
                absolute_url = base_url + link['href'] if link['href'].startswith('/') else link['href']
                target_urls.add(absolute_url)
        elif "maijiazhichi.com" in search_url_template:
            # Find h2 tags with class 'item-title', then get the 'a' tag inside
            h2_tags = soup.find_all('h2', class_='item-title')
            for h2 in h2_tags:
                link = h2.find('a', href=True)
                if link:
                    href = link['href']
                    # Exclude URLs containing 'allcase'
                    if "tro" not in href:
                        base_url = "https://maijiazhichi.com"
                        href = base_url + href if href.startswith('/') else href
                        target_urls.add(href) # Assuming these are absolute URLs already
        elif "sellerdefense.cn" in search_url_template:
            # Find h3 tags with class 'entry-title', then get the 'a' tag inside
            h3_tags = soup.find_all('h3', class_='entry-title')
            for h3 in h3_tags:
                link = h3.find('a', href=True)
                if link:
                    href = link['href']
                    # Exclude URLs containing 'allcase'
                    if "allcase" not in href:
                        base_url = "https://sellerdefense.cn"
                        href = base_url + href if href.startswith('/') else href
                        target_urls.add(href) # Assuming these are absolute URLs already

    print(target_urls)
    return list(target_urls)


async def find_target_urls_using_markdown(session: aiohttp.ClientSession, search_url_template: str, case_formats: list[str]) -> list[str]:
    """Searches case formats on a website, converts to Markdown, and extracts target article URLs via regex."""
    target_urls = set() # Use a set to avoid duplicates

    # Determine base URL for resolving relative links found in markdown
    base_domain_match = re.match(r'(https?://[^/]+)', search_url_template)
    base_url = base_domain_match.group(1) if base_domain_match else None

    for case_format in case_formats:
        search_url = search_url_template.replace("xxxx", case_format)
        print(f"Searching (Markdown method): {search_url}")
        content = await fetch_url(session, search_url)
        if not content:
            continue

        # Convert HTML to Markdown
        try:
            # Use body_width=0 to prevent line wrapping that might break URLs
            markdown_content = markdownify(content, heading_style="ATX", bullets='*', body_width=0)
        except Exception as e:
            print(f"Error converting HTML to Markdown for {search_url}: {e}")
            continue

        prompt = f'{markdown_content} \n\n This page shows seach results. What are the results (I need the name and URL)? Return your answer as {{"name_result_1": "url_1", "name_result_2": "url_2", etc... }}'
        response = await vertex_genai_multi_async([("text", prompt)], model_name="gemini-2.5-pro-exp-03-25")
        json_response = get_json(response)
        for name, url in json_response.items():
            if "allcase" not in url and "tro" not in url :
                url = base_url + url if url.startswith('/') else url
                target_urls.add(url)

    print(target_urls)
    return list(target_urls)


async def extract_data_from_url(session: aiohttp.ClientSession, url: str, date_filed: str, docket: str) -> dict | None:
    """Fetches a target URL, converts to Markdown, and sends to LLM."""
    print(f"Fetching data from: {url}")
    content = await fetch_url(session, url)
    if not content:
        return None

    soup = BeautifulSoup(content, 'html.parser')

    # Attempt to find a main content area - this often improves Markdown quality
    # These selectors are guesses and might need adjustment per site
    main_content = soup.find('article') or soup.find('main') or soup.find('div', class_=re.compile(r'(content|main|post|article)'))
    if not main_content:
        main_content = soup.body # Fallback to the whole body

    # Convert the relevant HTML part to Markdown
    markdown_content = markdownify(str(main_content), heading_style="ATX", bullets='*')

    # Prepare the prompt for the LLM
    prompt = f"""
    Analyze the following Markdown content extracted from the webpage {url}.
    Identify and extract all intellectual property registration numbers mentioned.
    Specifically look for:
    1.  Trademark Registration Numbers (usually start with digits or contain specific keywords like "Trademark Reg. No.")
    2.  Patent Registration Numbers (often start with "US" followed by digits, or contain keywords like "Patent No.")
    3.  Copyright Registration Numbers (often start with "VA", "TX", "SR", or contain keywords like "Copyright Reg. No.")

    Also, extract the URLs of any images that appear to be related to Copyright registrations. Try to associate each image URL with a specific Copyright Registration Number if possible. If an image is present but no clear registration number is nearby, list the image URL with a placeholder key like "no_reg_X". If a registration number is found with no associated image, list it with a null value for the image URL.
    Additionally, check the page (especially near copyright information) for a single URL pointing to the artist's main website or portfolio (artist_url). If found, include it as a top-level key in the JSON.

    Return the extracted information ONLY as a JSON object with the following structure:
    {{
      "trademarks": ["list of strings"],
      "patents": ["list of strings"],
      "copyrights": {{
        "reg_no_1": "image_url_1",
        "reg_no_2": null,
        "no_reg_1": "image_url_2"
      }},
      "artist_url": "url_string_or_null"
    }}
    where
    - "copyrights": Maps registration numbers (or "no_reg_X" placeholders) to associated image URLs (or null).
    - "artist_url": Contains the single artist website URL found on the page, or null if none was found.

    Do not include any introductory text, explanations, or markdown formatting in your response. Just the JSON object.

    Markdown Content:
    ---
    {markdown_content}
    ---
    """

    response = await vertex_genai_multi_async([("text", prompt)], model_name="gemini-2.5-pro-exp-03-25")
    
    try:
        json_response = get_json(response)
        return json_response
    except json.JSONDecodeError:
        print(f"Error decoding LLM response for {url}: {response}")
        return None


async def scrape_case_data(date_filed: str, docket: str) -> dict:
    """
    Main function to scrape data for a given case number from multiple websites.
    """
    case_formats = format_case_number(docket)
    if not case_formats:
        return {"error": f"Invalid case number format: {docket}"}

    websites = [
        "https://www.10100.com/search/xxxx",
        "https://sellerdefense.cn/?s=xxxx",
        "https://maijiazhichi.com/?s=xxxx"
    ]

    all_trademarks = set()
    all_patents = set()
    all_copyrights = {} # Use a dict to handle association and avoid duplicates
    all_artist_urls = set() # To store unique artist URLs found across pages

    async with aiohttp.ClientSession() as session:
        tasks = []
        target_url_tasks = []
        url_results = []
        for site_template in websites:
            # Create tasks to find URLs using both methods for each site
            # target_url_tasks.append(find_target_urls(session, site_template, case_formats))
            target_url_tasks.append(find_target_urls_using_markdown(session, site_template, case_formats))
            # url_results += await find_target_urls(session, site_template, case_formats)
            # url_results += await find_target_urls_using_markdown(session, site_template, case_formats)


        # Run all URL finding tasks concurrently
        url_results = await asyncio.gather(*target_url_tasks)

        # Combine all found URLs into a single set to ensure uniqueness
        all_found_urls = set()
        for url_list in url_results:
            all_found_urls.update(url_list)

        print(f"Found {len(all_found_urls)} unique target URLs across all methods and sites.")

        # Create tasks to extract data from each unique target URL found
        for url in all_found_urls:
            tasks.append(extract_data_from_url(session, url, date_filed, docket))

        # Run all data extraction tasks concurrently
        results = await asyncio.gather(*tasks)

        # Aggregate results
        copyright_no_reg_counter = 1
        for data in results:
            if data:
                if data.get("trademarks"):
                    all_trademarks.update(data["trademarks"])
                if data.get("patents"):
                    all_patents.update(data["patents"])
                # Aggregate Copyrights (reg -> img_url)
                if data.get("copyrights") and isinstance(data["copyrights"], dict):
                    for reg, img_url in data["copyrights"].items():
                         # Ensure img_url is a string or None
                        if not (isinstance(img_url, str) or img_url is None):
                            print(f"Warning: Skipping invalid copyright image URL format for reg '{reg}': {img_url}")
                            continue
                        if reg.startswith("no_reg_"): # Relabel placeholder keys
                            unique_key = f"no_reg_{copyright_no_reg_counter}"
                            all_copyrights[unique_key] = img_url
                            copyright_no_reg_counter += 1
                        elif reg not in all_copyrights: # Add if new registration number
                            all_copyrights[reg] = img_url
                        elif img_url and not all_copyrights.get(reg): # Update if we found an image for existing reg previously lacking one
                            all_copyrights[reg] = img_url
                        # If reg exists and has an image, don't overwrite with null or another image from a different source

                # Aggregate Artist URL
                artist_url = data.get("artist_url")
                if artist_url and isinstance(artist_url, str):
                    all_artist_urls.add(artist_url)


    # Process all copyright pictures: download them and remove watermark
    case_foler = sanitize_name(f"{date_filed.strftime('%Y-%m-%d')} - {docket}")
    process_copyrights_pictures_taks = []
    for reg, img_url in all_copyrights.items():
        process_copyrights_pictures_taks.append(copyright_download_and_remove_watermark(reg, img_url, case_foler))
    
    results = await asyncio.gather(*process_copyrights_pictures_taks)
    for result in results:
        all_copyrights[result["reg"]] = result["image_path"]
        
    return {
        "trademarks": sorted(list(all_trademarks)),
        "patents": sorted(list(all_patents)),
        "copyrights": all_copyrights,
        "artist_urls": sorted(list(all_artist_urls)) # Added artist URLs list
    }

async def copyright_download_and_remove_watermark(reg_no, img_url, case_folder):
    prompt = "Your job is to make the exact same image but without the watermark"
    prompt = "Remove the watermark from the image. Do not change anything else."
    prompt = "Your job is to generate a picture exactly the same but remove the current grey-blue diagonal watermark"

    file_extension = img_url.split(".")[-1]
    local_path = os.path.join(os.getcwd(), "Documents", "IP", "Copyrights", case_folder, f"{reg_no}.{file_extension}")

    await download_from_url(img_url, local_path)
    inline_data = await vertex_genai_image_gen_async([("text", prompt), ("image_path", local_path)])
    if inline_data and not isinstance(inline_data, str):  # isinstance(imdata, genai.types.Blob)
        file_extension = mimetypes.guess_extension(inline_data.mime_type)
        local_path_no_water = os.path.join(os.getcwd(), "Documents", "IP", "Copyrights", case_folder, f"{reg_no}_genai{file_extension}")
        f = open(local_path_no_water, "wb")
        f.write(inline_data.data)
        f.close()
    return {"reg": reg_no, "image_path": local_path_no_water}





# Example Usage (can be run with `python -m Scraper.scraper`)
if __name__ == "__main__":
    async def main():
        import pandas as pd
        date = pd.to_datetime("2024-05-03")
        test_case = "1:25-cv-00097"
        print(f"Scraping data for case: {test_case}")
        data = await scrape_case_data(date, test_case)
        print("\n--- Final Result ---")
        print(json.dumps(data, indent=2))

    asyncio.run(main())
