import os
import sys
sys.path.append(os.getcwd())
import cv2
import numpy as np
import os
from PIL import Image
import imagehash
import zlib
import base64
import pytesseract
import fitz  # PyMuPDF
import pandas as pd
import zlib
import base64
import platform
from logdata import log_message, set_context

# "certificate of registration", "registration number", "registration decision date", 
# "Copyright2P1": ["certificate of registration", "form va"], 

keywords = {
    "Trademark": [
        ["principal register"]
    ],
    "Copyright": [
        ["completion", "publication", "author", "copyright claimant", "title 17", "registration number", "certificate of registration"],
        ["completion", "publication", "author", "copyright claimant", "title 17", "registration number", "effective date of registration"], # some don't have the "certificate of registration" at the top
        ["certificate of registration", "certificate issued", "under the seal", "title 17", "author", "copyright claimant"],
        ["type of work", "copyright claimant", "application title", "date of creation", "registration number", "description"]  # Search from online copyright office. # Not always there: "nation of first publication",
    ],
    "Patent": [
        ["united states design patent", "date of patent"],
        ["united states patent", "date of patent"]
    ]
}


def find_complaint_pdf_path(directory):
    # Strict search: complaint AND not sheet, exhibit, schedule
    for folder in os.listdir(directory): 
        for pdf_filename in os.listdir(os.path.join(directory, folder)): 
            if pdf_filename.endswith(".pdf") and ("complaint" in pdf_filename.lower() and ("sheet" not in pdf_filename.lower() and "exhibit" not in pdf_filename.lower() and "schedule" not in pdf_filename.lower())):
                return os.path.join(directory, folder, pdf_filename)

    # Easy search: complaint OR not sheet, exhibit, schedule
    for folder in os.listdir(directory): 
        for pdf_filename in os.listdir(os.path.join(directory, folder)): 
            if pdf_filename.endswith(".pdf") and ("complaint" in pdf_filename.lower() or ("sheet" not in pdf_filename.lower() and "exhibit" not in pdf_filename.lower() and "schedule" not in pdf_filename.lower())):
                return os.path.join(directory, folder, pdf_filename)


def find_keywords_in_pdfs(similar_images_for_case, total_similar_images, case_directory_1, keyword_sets):
    for pdf_file in os.listdir(case_directory_1):
        if pdf_file.lower().endswith('.pdf'):
            pdf_file_without_extension = os.path.splitext(pdf_file)[0]
            pdf_path = os.path.join(case_directory_1, pdf_file)
            pdf_document = fitz.open(pdf_path)
            
            pages_with_keywords = []
            images_to_add = []
            for page_num in range(len(pdf_document)):
                # Extract text directly from PDF
                page = pdf_document[page_num]
                text = page.get_text()
                
                # Render page as image for OCR
                img = convert_page_number_to_image(pdf_document, page_num + 1)
                ocr_text = pytesseract.image_to_string(img)
                
                # Combine both text sources
                combined_text = text + " " + ocr_text
                
                if any(all(keyword.lower() in combined_text.lower() for keyword in keyword_set) for keyword_set in keyword_sets):
                    pages_with_keywords.append(page_num + 1)  # Add 1 because page numbers start at 1
                    # if the page has 2 images , delete both and replace with save_image_to_jpg(img,....)
                    image_on_page = [file for file in os.listdir(os.path.join(case_directory_1, pdf_file_without_extension)) if file.startswith(f"{pdf_file_without_extension}_page{page_num + 1}_")]
                    if len(image_on_page) > 1:
                        for img_file in image_on_page:
                            img_path = os.path.join(case_directory_1, pdf_file_without_extension, img_file)
                            os.remove(img_path)
                            if img_path in similar_images_for_case[pdf_file_without_extension]:
                                similar_images_for_case[pdf_file_without_extension].remove(img_path)

            
            if pages_with_keywords:
                if pdf_file_without_extension not in similar_images_for_case:
                    similar_images_for_case[pdf_file_without_extension] = []
            
                pages_in_similar = [int(image_name.split("_page")[1].split("_")[0]) for image_name in similar_images_for_case[pdf_file_without_extension]]
                pages_not_in_similar = [page for page in pages_with_keywords if page not in pages_in_similar]
                # if pages_not_in_similar:
                    # print(f"Pages with keywords not in similar_images_for_case[{pdf_file_without_extension}]: {pages_not_in_similar}")
                pages_in_similar_not_found = [page for page in pages_in_similar if page not in pages_with_keywords]
                if pages_in_similar_not_found:
                    print(f"Pages in similar_images_for_case[{pdf_file_without_extension}] not found by text extraction or OCR: {pages_in_similar_not_found}")
                    for page_num in pages_in_similar_not_found:
                        pdfpage = pdf_document[page_num]
                        text = pdfpage.get_text()
                        img = convert_page_number_to_image(pdf_document, page_num + 1)
                        ocr_text = pytesseract.image_to_string(img)
                        combined_text = text + " " + ocr_text
                        print(combined_text)

                for page in pages_not_in_similar:
                    save_page_number_to_image(pdf_document, page, os.path.join(case_directory_1, pdf_file_without_extension, f"{pdf_file_without_extension}_page{page}_0.jpg"))
                    images_to_add.append(os.path.join(case_directory_1, pdf_file_without_extension, f"{pdf_file_without_extension}_page{page}_0.jpg"))
                similar_images_for_case[pdf_file_without_extension].extend(images_to_add)
                total_similar_images += len(images_to_add)

            elif pdf_file_without_extension in similar_images_for_case and similar_images_for_case[pdf_file_without_extension]:
                pages_in_similar_not_found = [int(image_name.split("_page")[1].split("_")[0]) for image_name in similar_images_for_case[pdf_file_without_extension]]
                print(f"Pages in similar_images_for_case[{pdf_file_without_extension}] not found by text extraction or OCR: {pages_in_similar_not_found}")

            pdf_document.close()
            
    return similar_images_for_case, total_similar_images
    


def assess_complaint_similarity_to_template(ref_hash, size, case_directory_1, threshold=17):
    similar_images_for_case = {}
    total_similar_images = 0
    for foldername in os.listdir(case_directory_1):
        folder_path = os.path.join(case_directory_1, foldername)
        if os.path.isdir(folder_path):
            similar_images_in_pdf, differences, total_similar_images_folder = assess_similarity_to_template_single_folder(folder_path, ref_hash, size, threshold)
            similar_images_for_case[foldername] = similar_images_in_pdf
            total_similar_images += total_similar_images_folder
    return similar_images_for_case, total_similar_images


# def assess_similarity_to_template_single_folder(folder_path, ref_hash, size, threshold=17):
#     total_similar_images = 0
#     similar_images_in_pdf = []
#     differences = {}
#     for filename in os.listdir(folder_path):
#         if filename.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.gif', '.tiff', '.webp', '.jpx')):
#             filepath = os.path.join(folder_path, filename)
#             try:
#                 difference = assess_similarity_to_template_single_file(filepath, ref_hash, size)
#                 differences[filename] = str(difference)
#                 if difference < threshold:  # Threshold value; adjust as needed
#                     similar_images_in_pdf.append(filepath)
#                     total_similar_images += 1
#             except Exception as e:
#                 pass
#     return similar_images_in_pdf, differences, total_similar_images


# def assess_similarity_to_template_single_file(filepath, ref_hash, size):
#     with Image.open(filepath) as img:
#         # Check if the image is horizontal and rotate if necessary
#         if img.width > img.height:
#             img = img.rotate(90, expand=True)
#         img = img.convert('L')  # Convert to grayscale
#         img = img.resize(size, Image.Resampling.LANCZOS)
#     hash = imagehash.phash(img)
#     difference = hash - ref_hash
#     return difference


def assess_similarity_to_template_single_folder(folder_path, ref_hash, size, threshold=17):
    # Pre-compile the extension check tuple for faster lookup
    VALID_EXTENSIONS = ('.png', '.jpg', '.jpeg', '.bmp', '.gif', '.tiff', '.webp', '.jpx')
    
    total_similar_images = 0
    similar_images_in_pdf = []
    differences = {}
    
    # Use list comprehension for faster file filtering
    image_files = [f for f in os.listdir(folder_path) 
                   if f.lower().endswith(VALID_EXTENSIONS)]
    
    for filename in image_files:
        filepath = os.path.join(folder_path, filename)
        try:
            difference = assess_similarity_to_template_single_file(filepath, ref_hash, size)
            differences[filename] = str(difference)
            if difference < threshold:
                similar_images_in_pdf.append(filepath)
                total_similar_images += 1
        except Exception:  # Removed unused 'e' variable
            continue  # Using continue instead of pass for clarity

    return similar_images_in_pdf, differences, total_similar_images


def assess_similarity_to_template_single_file(filepath, ref_hash, size):
    with Image.open(filepath) as img:
        # Combine operations to reduce number of image copies
        img = (img.rotate(90, expand=True) if img.width > img.height else img).convert('L').resize(size, Image.Resampling.LANCZOS)
        return imagehash.phash(img) - ref_hash



def save_page_number_to_image(pdf_document, page_number, target_name):
    img_array = convert_page_number_to_image(pdf_document, page_number)
    save_image_to_jpg(img_array, target_name)
    
def save_image_to_jpg(img_array, target_name):
    # Apply compression
    encode_param = [int(cv2.IMWRITE_JPEG_QUALITY), 80]  # Adjust quality (0-100) as needed
    _, encoded_img = cv2.imencode('.jpg', img_array, encode_param)
    
    # Save the compressed image
    with open(target_name, 'wb') as f:
        f.write(encoded_img)

    # print(f"Saved image: {target_name}, Size: {len(encoded_img) / 1024:.2f} KB")


def convert_page_number_to_image(pdf_document, page_number):
    page = pdf_document[page_number-1]
    
    # Increase the resolution (zoom factor)
    zoom = 3  # Higher zoom for better quality
    mat = fitz.Matrix(zoom, zoom)
    
    # Render the page with the higher resolution
    pix = page.get_pixmap(matrix=mat, alpha=False)
    
    # Convert PyMuPDF pixmap to numpy array
    img_array = np.frombuffer(pix.samples, dtype=np.uint8).reshape(pix.height, pix.width, 3)
    
    # Convert BGR to RGB (OpenCV uses BGR by default)
    img_array = cv2.cvtColor(img_array, cv2.COLOR_BGR2RGB)

    return img_array



# @profile
def crop_white_space(img, min_area=0.0005, simetric=False):  # First: 0.004, then 0.0036, then 0.0005
    
    # Convert image to numpy array if it's not already
    if isinstance(img, Image.Image):
        img = np.array(img)
        if img.ndim == 3 and img.shape[2] == 3:
            img = cv2.cvtColor(img, cv2.COLOR_RGB2BGR)
    
    # Convert to grayscale
    if len(img.shape) == 3:
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    else:
        gray = img

    # Apply Gaussian Blur
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)

    # Apply adaptive thresholding
    thresh = cv2.adaptiveThreshold(
        blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY_INV, 11, 2
    )

    # Morphological operations to remove noise
    kernel = np.ones((3, 3), np.uint8)
    cleaned = cv2.morphologyEx(thresh, cv2.MORPH_OPEN, kernel)

    # Find contours and filter by area
    min_area = img.shape[0] * img.shape[1] * min_area / 100
    contours, _ = cv2.findContours(cleaned, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    large_contours = [cnt for cnt in contours if cv2.contourArea(cnt) > min_area]

    if large_contours:
        # Get combined bounding box
        x_min, y_min = np.inf, np.inf
        x_max, y_max = -np.inf, -np.inf

        for cnt in large_contours:
            x, y, w, h = cv2.boundingRect(cnt)
            x_min = min(x_min, x)
            y_min = min(y_min, y)
            x_max = max(x_max, x + w)
            y_max = max(y_max, y + h)

        # Add padding if necessary
        padding = 5
        x_min = max(x_min - padding, 0)
        y_min = max(y_min - padding, 0)
        x_max = min(x_max + padding, img.shape[1])
        y_max = min(y_max + padding, img.shape[0])


        if simetric:
            # Calculate the amount of white space on each side
            left_white = x_min
            right_white = img.shape[1] - x_max
            top_white = y_min
            bottom_white = img.shape[0] - y_max

            # Adjust the cropping to be symmetric
            if left_white / img.shape[1] < 0.4 and (left_white - right_white) / img.shape[1] > 0.03:
                tmp_x_max = int(min(img.shape[1], img.shape[1] - left_white + (0.01 * img.shape[1])))
                last_line = img[:, tmp_x_max]
                if np.mean(last_line > 240) * 100 > 95:
                    x_max = tmp_x_max
            if right_white / img.shape[1] < 0.4 and (right_white - left_white) / img.shape[1] > 0.03:
                tmp_x_min = int(max(0, right_white - (0.01 * img.shape[1])))
                last_line = img[:, tmp_x_min]
                if np.mean(last_line > 240) * 100 > 95:
                    x_min = tmp_x_min
            if bottom_white / img.shape[0] < 0.4 and (bottom_white - top_white) / img.shape[0] > 0.03:
                tmp_y_max = int(min(img.shape[0], img.shape[0] - bottom_white + (0.01 * img.shape[0])))
                last_line = img[tmp_y_max, :]
                if np.mean(last_line > 240) * 100 > 95:
                    y_max = tmp_y_max
            if top_white / img.shape[0] < 0.4 and (top_white - bottom_white) / img.shape[0] > 0.03:
                tmp_y_min = int(max(0, top_white - (0.01 * img.shape[0])))
                last_line = img[tmp_y_min, :]
                if np.mean(last_line > 240) * 100 > 95:
                    y_min = tmp_y_min

        # Crop the image
        cropped = img[y_min:y_max, x_min:x_max]
        return cropped, (x_min, y_min)
    else:
        return img, (0, 0)  # Return original image if no large contours are found
    

def is_compressed(data):
    try:
        # Try to decompress and decode
        zlib.decompress(base64.b64decode(data.encode('utf-8')))
        return True
    except (zlib.error, base64.binascii.Error, TypeError):
        return False