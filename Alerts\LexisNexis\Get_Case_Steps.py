import time
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import traceback
import re
from Common.Constants import download_patterns, no_download_patterns
from .Download_File import get_step_file
from logdata import log_message
import pandas as pd
from decimal import Decimal, ROUND_HALF_UP
from FileManagement.NAS import NASConnection


def get_case_steps_and_files(driver, docket_number, court, date_filed, steps_df, file_type, nas: NASConnection):
    pacer_file_download_count = 0
    total_files_downloaded = 0

    ### Get case table with all the steps
    try:
        # Wait for the table to be populated by JavaScript and find the table
        status = True
        table = WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.CLASS_NAME, "SS_DataTable")))
        table_length = len(table.find_elements(By.TAG_NAME, 'tr'))
        
        # Confirm that the table has 6 columns
        header_row = table.find_element(By.TAG_NAME, 'tr')
        if len(header_row.find_elements(By.TAG_NAME, 'th')) != 6:
            log_message(f"Table does not have 6 columns for {docket_number} from {court}. Process failed.")
            return steps_df, False, 0, 0
    
        
        # Identify all the steps that are in the same date as the complaint, and that have links to exhibits
        time.sleep(0.5)

        def look_for(include, exclude):
            steps_needed_for_IP = []
            item_date = None
            for i, tr in enumerate(table.find_elements(By.TAG_NAME, 'tr')[1:]):  # Skip the first header row, use enumerate
                step_description = tr.find_elements(By.TAG_NAME, 'td')[4].text.lower()
                step_date = tr.find_elements(By.TAG_NAME, 'td')[3].text.strip()
                if item_date == None and re.search(include, step_description) and not re.search(exclude, step_description):
                    item_date = step_date
                    steps_needed_for_IP.append(str(i + 1))
                elif item_date != None and item_date == step_date:
                    links = tr.find_elements(By.TAG_NAME, 'td')[4].find_elements(By.TAG_NAME, 'a')
                    # Check if any of the links contain the word "exhibit" and not "AO 120" (which is the form notifing USPTO)
                    # if "sealed" not in step_description and   => we removed this rule because we want the sealed as well because it might be unsealed
                    if not re.search(exclude, step_description) and any("exhibit" in link.text.lower() for link in links) and len(tr.find_elements(By.TAG_NAME, 'td')[1].text.strip()) > 0: # Make sure the "Availability" column is not empty
                        steps_needed_for_IP.append(str(i + 1))
                elif item_date != None:
                    break
            return steps_needed_for_IP, i
        
        steps_needed_for_IP, last_step_of_complaint_day = look_for(include="complaint", exclude="temporary restraining order | motion | memorandum | declaration | ao 120")

        if "deep" in file_type:
            steps_needed_for_amended_complaint_IP, _ = look_for(include="amended complaint", exclude="temporary restraining order | memorandum | declaration | ao 120")
            steps_needed_for_IP.extend(steps_needed_for_amended_complaint_IP)
            steps_needed_for_tro_IP, _ = look_for(include="memorandum .* temporary restraining order", exclude="ao 120")
            if len(steps_needed_for_tro_IP) == 0:
                steps_needed_for_tro_IP, _ = look_for(include="motion .* temporary restraining order", exclude="ao 120")
            steps_needed_for_IP.extend(steps_needed_for_tro_IP)
            steps_needed_for_preliminary_injunction_IP, _ = look_for(include="memorandum .* preliminary injunction", exclude="ao 120")
            if len(steps_needed_for_preliminary_injunction_IP) == 0:
                steps_needed_for_preliminary_injunction_IP, _ = look_for(include="motion .* preliminary injunction", exclude="ao 120")
            steps_needed_for_IP.extend(steps_needed_for_preliminary_injunction_IP)

        #Determine the entry the most links (likelly the screenshots evidences agains the seller)
        evidence_row = None
        if "evidence" in file_type: 
            max_links = 3
            for i, tr in enumerate(table.find_elements(By.TAG_NAME, 'tr')[(last_step_of_complaint_day+1):]): # Skip the first header row and all the rows on the complaint day
                nblinks_row = len(tr.find_elements(By.TAG_NAME, 'a'))
                td_text = tr.find_elements(By.TAG_NAME, 'td')[4].text.lower()
                if nblinks_row > max_links and any(keyword in td_text for keyword in ["declaration", "tro", "temporary restraining order"]) and not "declaration of keith" in td_text:
                    max_links = nblinks_row
                    evidence_row = i+1
        
        # for row_index in steps_needed_for_IP:
        #     # Re-find the table (important if the page might have updated)
        #     table = WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.CLASS_NAME, "SS_DataTable")))
        #     tr = table.find_elements(By.TAG_NAME, 'tr')[row_index]  # +1 because we skipped the header row

        
        for row_nb in range(1, table_length): # we skipped the header row
            table = WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.CLASS_NAME, "SS_DataTable")))  # more safe, we reaquire the table and the row at every step of the loop. Avoids stale elements
            tr = table.find_elements(By.TAG_NAME, 'tr')[row_nb]  # +1 because we skipped the header row
            cells = [td.text.strip() for td in tr.find_elements(By.TAG_NAME, 'td')[1:]]  # Skip the first columns (tick box)
            if cells:
                download_required = assess_download_requirement(len(cells[0]) != 0, cells[3]) # download_link and step_text analysis
                # cells.extend([docket_number, court, download_required, None, None, None])
                # steps_df.loc[len(steps_df)] = cells[1:] # cells[0] is the link to download the file
                new_row = pd.DataFrame({
                    'step_nb': [cells[1]],
                    'step_date_filed': [pd.to_datetime(cells[2]).date()],
                    'proceeding_text': [cells[3]],
                    'docket': [docket_number],
                    'court': [court],
                    'download_required': [download_required],
                    'files_downloaded': [None],
                    'files_number': [None],
                    'files_failed': [None]
                })
                steps_df = pd.concat([steps_df, new_row], ignore_index=True)

                if (file_type == "all" and download_required) or (file_type == "all" and cells[1] in steps_needed_for_IP) or \
                        ("0" in file_type and (cells[1] in steps_needed_for_IP) and cells[0] == "Free") or \
                        ("1" in file_type and (cells[1] in steps_needed_for_IP)) or \
                        ("free" in file_type and cells[0] == "Free" and (download_required or cells[1] in steps_needed_for_IP)) or \
                        ("evidence" in file_type and row_nb == evidence_row and "sealed" not in cells[3].lower()):
                    if row_nb == evidence_row:
                        print(f"Evidence row found for {docket_number} from {court}, step {cells[1]}")
                    
                    driver.execute_script("arguments[0].scrollIntoView(); window.scrollBy(0, -150);", tr) # puts the tr in view
                    total_files, files_processed, files_downloaded = get_step_file(driver, tr, docket_number, court, date_filed, cells[1], cells[2], nas)
                    steps_df.at[steps_df.index[-1], 'files_number'] = total_files
                    steps_df.at[steps_df.index[-1], 'files_downloaded'] = files_downloaded
                    steps_df.at[steps_df.index[-1], 'files_failed'] = files_processed - files_downloaded
                    if total_files != files_processed:
                        status = False
                    total_files_downloaded += files_downloaded
                    if cells[0] != "Free":
                        pacer_file_download_count +=1

        return steps_df, status, pacer_file_download_count, total_files_downloaded

    except Exception as e:
        print(f"Error get_case_steps_and_files: {docket_number} from {court}: {e}")
        print(f"Traceback:\n{traceback.format_exc()}")
        return steps_df, False, pacer_file_download_count, total_files_downloaded
    


compiled_download_patterns = [re.compile(pattern, re.IGNORECASE) for pattern in download_patterns]
compiled_no_download_patterns = [re.compile(pattern, re.IGNORECASE) for pattern in no_download_patterns]
def assess_download_requirement(downloadable, step_description):
    if not downloadable: # or "sealed" in step_description.lower()  => Now we want the sealed as well because it might be unsealed
        return False
    
    # Check for download patterns
    if any(pattern.search(step_description) for pattern in compiled_download_patterns):
        return True

    # Check for no-download patterns
    if any(pattern.search(step_description) for pattern in compiled_no_download_patterns):
        return False

    # If no pattern matches, return None
    return None



def fill_missing_step_numbers(df_steps):
    # Iterate through rows and fill in step_nb
    previous_step_nb = 0
    increment = Decimal('0.01')
    for index, row in df_steps.iterrows():
        # if (1) there is no step_nb, (2) the step_nb is not an integer, or (3) the step_nb is the same as the previous one
        if pd.isna(row['step_nb']) or not is_integer(row['step_nb']) or (previous_step_nb == int(row['step_nb'])):
            extracted_nb = extract_step_number(row['proceeding_text'])
            if extracted_nb is not None:
                if extracted_nb == previous_step_nb: # If the step number is the same as the previous one, add .00
                    new_step_nb = (previous_step_nb + increment).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
                    df_steps.at[index, 'step_nb'] = str(new_step_nb)
                    previous_step_nb = new_step_nb
                else:
                    df_steps.at[index, 'step_nb'] = str(extracted_nb) + '.00'
                    previous_step_nb = extracted_nb
            else:
                new_step_nb = (previous_step_nb + increment).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
                df_steps.at[index, 'step_nb'] = str(new_step_nb) 
                previous_step_nb = new_step_nb
        else:
            previous_step_nb = int(row['step_nb'])
            df_steps.at[index, 'step_nb'] = str(previous_step_nb) + '.00'

    return df_steps

def extract_step_number(text):
    match = re.search(r'Docket Entry\s+(\d{1,2})', text)
    return int(match.group(1)) if match else None

def is_integer(value):
    try:
        int(value)
        return True
    except (ValueError, TypeError):
        return False