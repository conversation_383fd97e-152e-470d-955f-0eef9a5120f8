from selenium.webdriver.common.by import By
from selenium.webdriver.common.action_chains import Action<PERSON>hai<PERSON>
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from Alerts.Chrome_Driver import send_keyboard_input
import os, time
from Alerts.Chrome_Driver import get_driver, move_mouse_to, random_delay
from logdata import log_message
from selenium.common.exceptions import NoSuchElementException




def get_logged_in_browser():
    driver = get_driver()
    random_delay()
    driver.get('https://advance.lexis.com/courtlinksearch')
    random_delay()

    # Check if need to login into the platform
    try:
        if driver.find_element(By.ID, 'userid'):
            login_into_platform(driver)
        log_message(f'User has been logged in successfully in LexisNexis.')
    except NoSuchElementException:
        log_message(f'User already logged in in LexisNexis.')

    # Check if the "Ok, got it" button is present and click it if found
    try:
        ok_button = WebDriverWait(driver, 5).until(
            EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Ok, got it')]"))
        )
        move_mouse_to(driver, ok_button)
        ok_button.click()
        random_delay()
        log_message("Clicked 'Ok, got it' button")
    except:
        pass

    return driver


def login_into_platform(driver):
    # Check if the login form is present
    try:
        actions = ActionChains(driver)
        
        # Move to the username input, click, and enter the username one by one
        username_input = driver.find_element(By.ID, 'userid')
        send_keyboard_input(driver, username_input, os.environ["LNusername"])

        # # Move to the "Remember Me" checkbox and click it
        remember_me_checkbox = driver.find_elements(By.ID, 'chkrmflag')
        if len(remember_me_checkbox) > 0 and remember_me_checkbox[0].is_displayed():
            actions.move_to_element(remember_me_checkbox[0]).click().perform()
 
        # Click the "Next" button
        next_button = driver.find_element(By.ID, 'signInSbmtBtn')
        actions.move_to_element(next_button).click().perform()

        # Wait for the password field to be enabled
        try:
            password_input = WebDriverWait(driver, 30).until(
                EC.element_to_be_clickable((By.ID, 'password'))
            )
        except:
            log_message("Password input not found, trying again...")
            time.sleep(10)
            password_input = WebDriverWait(driver, 30).until(
                EC.element_to_be_clickable((By.ID, 'password'))
            )

        random_delay()
        password_input.send_keys('\u0001')  # Ctrl+A
        password_input.send_keys('\u0008')  # Backspace
        random_delay()

        # Move to the password input, click, and enter the password one by one
        send_keyboard_input(driver, password_input, os.environ["LNpassword"])
 
        remember_me_checkbox = driver.find_elements(By.ID, 'chkrmflag')
        if len(remember_me_checkbox) > 0 and remember_me_checkbox[0].is_displayed():
            actions.move_to_element(remember_me_checkbox[0]).click().perform()


        # Move to the "Sign In" button and click it
        continue_button = driver.find_elements(By.ID, 'continue')
        submit_button = driver.find_elements(By.ID, 'signInSbmtBtn')
        sign_in_button = continue_button[0] if continue_button else submit_button[0]
        actions.move_to_element(sign_in_button).click().perform()

    except Exception as e:
        print("Login form not found or error during login:", e)


if __name__ == "__main__":
    driver = get_logged_in_browser()
    print(driver.current_url)
    driver.quit()

