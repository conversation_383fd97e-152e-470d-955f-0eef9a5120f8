"""
Trademark File Management Module

This module provides functions to manage trademark files, including downloading, extracting,
and sending to NAS.
"""

import os
import zipfile
import requests
import logging
import time
import shutil
from pathlib import Path
from FileManagement.NAS import NASConnection

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("trademark_file.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def download_file(url, local_path, headers=None, max_retries=5, retry_delay=5):
    """
    Download a file from a URL with retry logic.
    
    Args:
        url (str): URL to download from
        local_path (str): Path to save the file
        headers (dict, optional): HTTP headers
        max_retries (int): Maximum number of retry attempts
        retry_delay (int): Delay between retries in seconds
        
    Returns:
        bool: True if successful, False otherwise
    """
    for attempt in range(max_retries):
        try:
            response = requests.get(url, headers=headers, stream=True, timeout=60)
            response.raise_for_status()
            
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(local_path), exist_ok=True)
            
            # Save file
            with open(local_path, "wb") as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            logger.info(f"Successfully downloaded file from {url} to {local_path}")
            return True
        
        except requests.exceptions.RequestException as e:
            if response.status_code == 404:
                logger.warning(f"File not found at URL: {url}")
                return False
            
            logger.warning(f"Attempt {attempt+1}/{max_retries} failed: {str(e)}")
            
            if attempt < max_retries - 1:
                time.sleep(retry_delay)
            else:
                logger.error(f"Failed to download file after {max_retries} attempts: {url}")
                return False

def extract_zip(zip_path, extract_path):
    """
    Extract a ZIP file.
    
    Args:
        zip_path (str): Path to the ZIP file
        extract_path (str): Path to extract to
        
    Returns:
        list: List of extracted file paths
    """
    try:
        # Create directory if it doesn't exist
        os.makedirs(extract_path, exist_ok=True)
        
        # Extract ZIP file
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(extract_path)
        
        # Get list of extracted files
        extracted_files = []
        for root, _, files in os.walk(extract_path):
            for file in files:
                if file.endswith('.xml'):
                    extracted_files.append(os.path.join(root, file))
        
        logger.info(f"Successfully extracted {len(extracted_files)} files from {zip_path}")
        return extracted_files
    
    except Exception as e:
        logger.error(f"Error extracting ZIP file {zip_path}: {str(e)}")
        return []

def send_to_nas(local_path, remote_path):
    """
    Send a file to NAS.
    
    Args:
        local_path (str): Local file path
        remote_path (str): Remote path on NAS
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        with NASConnection() as nas:
            nas.transfer_file_with_scp(local_path=local_path, remote_path=remote_path, to_nas=True)
        
        logger.info(f"Successfully sent file {local_path} to NAS at {remote_path}")
        return True
    
    except Exception as e:
        logger.error(f"Error sending file {local_path} to NAS: {str(e)}")
        return False

def send_folder_to_nas(local_folder, remote_folder):
    """
    Send a folder to NAS.
    
    Args:
        local_folder (str): Local folder path
        remote_folder (str): Remote folder path on NAS
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        with NASConnection() as nas:
            nas.ssh_local_to_nas(local_folder, remote_folder)
        
        logger.info(f"Successfully sent folder {local_folder} to NAS at {remote_folder}")
        return True
    
    except Exception as e:
        logger.error(f"Error sending folder {local_folder} to NAS: {str(e)}")
        return False

def cleanup_files(files_to_keep, files_to_delete):
    """
    Clean up files after processing.
    
    Args:
        files_to_keep (list): List of files to keep
        files_to_delete (list): List of files to delete
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Delete files
        for file_path in files_to_delete:
            if os.path.exists(file_path):
                os.remove(file_path)
                logger.info(f"Deleted file: {file_path}")
        
        return True
    
    except Exception as e:
        logger.error(f"Error cleaning up files: {str(e)}")
        return False

def merge_image_directories(source_dir, destination_dir):
    """
    Moves contents from a source directory to a destination directory,
    preserving the subdirectory structure (e.g., xx/yy/image.webp).

    Args:
        source_dir (str): The temporary source directory containing new images.
        destination_dir (str): The main destination directory (e.g., IMAGES_DIR).
    """
    try:
        logger.info(f"Starting merge from {source_dir} to {destination_dir}")
        # Iterate through items (files and dirs) in the source directory
        for item_name in os.listdir(source_dir):
            source_item_path = os.path.join(source_dir, item_name)
            dest_item_path = os.path.join(destination_dir, item_name)

            # If it's a directory (e.g., the 'xx' level)
            if os.path.isdir(source_item_path):
                # Ensure the destination 'xx' directory exists
                os.makedirs(dest_item_path, exist_ok=True)
                # Iterate through sub-items (e.g., the 'yy' level)
                for sub_item_name in os.listdir(source_item_path):
                    source_sub_item_path = os.path.join(source_item_path, sub_item_name)
                    dest_sub_item_path = os.path.join(dest_item_path, sub_item_name)

                    # If it's a directory (e.g., the 'yy' level)
                    if os.path.isdir(source_sub_item_path):
                        # Ensure the destination 'yy' directory exists
                        os.makedirs(dest_sub_item_path, exist_ok=True)
                        # Move the actual image files
                        for image_file_name in os.listdir(source_sub_item_path):
                            source_image_path = os.path.join(source_sub_item_path, image_file_name)
                            dest_image_path = os.path.join(dest_sub_item_path, image_file_name)
                            # Use move, which handles overwriting if necessary and is atomic on some systems
                            shutil.move(source_image_path, dest_image_path)
                            # logger.debug(f"Moved {source_image_path} to {dest_image_path}") # Optional: debug logging
                    # If it's a file directly under 'xx' (shouldn't happen with current structure)
                    elif os.path.isfile(source_sub_item_path):
                         shutil.move(source_sub_item_path, dest_sub_item_path)
                         # logger.debug(f"Moved {source_sub_item_path} to {dest_sub_item_path}")
            # If it's a file directly in the temp dir (shouldn't happen)
            elif os.path.isfile(source_item_path):
                # Ensure parent directory exists in destination_dir before moving file
                os.makedirs(os.path.dirname(dest_item_path), exist_ok=True)
                shutil.move(source_item_path, dest_item_path)
                # logger.debug(f"Moved {source_item_path} to {dest_item_path}")

        logger.info(f"Successfully merged images from {source_dir} to {destination_dir}")
        return True
    except Exception as move_err:
        logger.error(f"Error merging images from {source_dir} to {destination_dir}: {move_err}")
        return False
