import zlib, base64, json, os, cv2, time, fitz, zipfile, asyncio, shutil, io, re
import pandas as pd
from PIL import Image
from tempfile import TemporaryDirectory
from multiprocessing import Manager
from contextlib import closing

from DatabaseManagement.Connections import get_gz_connection, is_connection_alive
from DatabaseManagement.ImportExport import get_table_from_GZ, insert_and_update_df_to_GZ_batch
from Alerts.PicturesProcessing.ProcessPicturesShared import convert_page_number_to_image
from AI.USPTO_TSDR_API import TSD<PERSON>pi
from Common.Constants import local_ip_folder, nas_ip_folder, sem_task
from FileManagement.NAS import NASConnection
from IP.ip_shared import get_trademark_df

# ❌⚠️📥


async def get_trademarks_uspto(full_list_of_reg_no):
    api_client = TSDRApi()
    await api_client.start_session()

    base_folder = os.path.join(local_ip_folder, "Trademarks")
    xml_folder = os.path.join(base_folder, "XML")
    image_folder = os.path.join(base_folder, "Images")
    document_folder = os.path.join(base_folder, "Documents")
    status_folder = os.path.join(base_folder, "Status")
    certificate_folder = os.path.join(base_folder, "Certificates")
    os.makedirs(base_folder, exist_ok=True)
    os.makedirs(xml_folder, exist_ok=True)
    os.makedirs(image_folder, exist_ok=True)
    os.makedirs(certificate_folder, exist_ok=True)
    os.makedirs(document_folder, exist_ok=True)
    os.makedirs(status_folder, exist_ok=True)
    
    # Get cached trademark dataframe - this is our single source of truth for database operations
    trademark_db_df = get_trademark_df()
    
    # Create a new DataFrame to collect results that will be returned
    result_df = pd.DataFrame(columns=["reg_no", "ser_no", "TRO", "applicant_name", 
                                      "text", "int_cls", "date", "nb_suits", "country_codes", 
                                      "associated_marks", "info_source", "image_source", 
                                      "certificate_source", "plaintiff_id", "metadata"])
    
    # Initialize NAS connection and database connection
    nas = NASConnection()
    db_connection = get_gz_connection()
    
    time_last_request = time.time()
    manager = Manager()
    certificate_needed = manager.list()  # Shared list
    
    # Create a queue for processing actions in a thread-safe manner
    queue = asyncio.Queue()  # Create the asyncio Queue
    
    # Set to track which trademarks we need to send to NAS
    nas_sync_needed = set()
    
    async def process_trademark(trademark):
        nonlocal time_last_request  # Access the nonlocal variables
        formatted_reg_no = api_client.format_id_number(trademark.get("reg_no", None), id_key='rn')
        formatted_ser_no = api_client.format_id_number(trademark.get("ser_no", None), id_key='sn')  # When we search by owner name, we get serial numbers (not registration numbers)
        if not formatted_reg_no and not formatted_ser_no:
            return
        plaintiff_id = trademark.get("plaintiff_id", None)
        metadata = trademark.get("metadata", None)

        # Check if trademark already exists in the database
        found = False
        data = None
        if (formatted_reg_no and formatted_reg_no in trademark_db_df["reg_no"].values) or (formatted_ser_no and formatted_ser_no in trademark_db_df["ser_no"].values):
            if formatted_reg_no:
                df_entry = trademark_db_df[trademark_db_df["reg_no"] == formatted_reg_no].iloc[0].copy()
            elif formatted_ser_no:
                df_entry = trademark_db_df[trademark_db_df["ser_no"] == formatted_ser_no].iloc[0].copy()
                formatted_reg_no = df_entry["reg_no"]

            print(f"🔥 Trademark already in database: {formatted_reg_no}")
            found, xml_local_path, image_local_path, certificate_local_path = await asyncio.to_thread(get_trademark_from_NAS, formatted_reg_no, df_entry, nas)
            
            # Update plaintiff_ids in the database if needed
            if plaintiff_id and found:
                await queue.put(("update_plaintiff_id", {
                    "formatted_reg_no": formatted_reg_no, 
                    "plaintiff_id": plaintiff_id
                }))
                
            if found:
                # Add required fields that might not be in the database
                df_entry["plaintiff_id"] = plaintiff_id
                if metadata:
                    df_entry["metadata"] = metadata
                
                # Add this row to the result DataFrame through the queue
                await queue.put(("add_to_result", df_entry))
                return
                
        # If not found in database => proceed with API calls: get info from XML file
        if formatted_reg_no and os.path.exists(os.path.join(xml_folder, f"{formatted_reg_no}.xml")): # If the XML file ison the HDD, use it
            with open(os.path.join(xml_folder, f"{formatted_reg_no}.xml"), 'rb') as f:
                xml_content_reg = f.read()
            data = api_client.process_xml_content(xml_content_reg)
        else: # If the file is not on the HDD: get the XML from USPTO
            if formatted_reg_no: 
                xml_content_reg = await api_client.get_status_info_xml(formatted_reg_no, id_key='rn')
            elif formatted_ser_no: # This can be the case where we extracted a serial number (application number) from the exhibit or from searching the website by trademark owner name
                xml_content_reg = await api_client.get_status_info_xml(formatted_ser_no, id_key='sn')
        
            if xml_content_reg:
                data = api_client.process_xml_content(xml_content_reg)
                if not formatted_reg_no: # e.g. when we search for trademarks by owner name and we start with the serial number
                    formatted_reg_no = data["reg_no"] # should always exist because we are searching for trademarks that are valid!
                await save_content(xml_content_reg, f"{formatted_reg_no}.xml", xml_folder)
            else:
                return

        
        # Create a new row based on API response data
        new_row = {}
        if data:
            new_row = {
                "formatted_reg_no": formatted_reg_no, "reg_no": data["reg_no"], "ser_no": data["application_number"],
                "TRO": True, "applicant_name": data["applicant_name"], "text": data["text"], "int_cls": data["int_cls"],
                "date": data["date"], "nb_suits": data["nb_suits"], "country_codes": data["country_codes"],
                "associated_marks": data["associated_marks"], "info_source": "USPTO_XML"
            }
            # Try to get image using URL
            image_downloaded, image_source = await get_image_from_url(api_client, formatted_reg_no, data, image_folder)
            if image_downloaded:
                new_row["image_source"] = image_source
        elif formatted_ser_no: # Only include essential fields when only serial number is available. When is this the case?
            new_row = {
                "formatted_reg_no": formatted_reg_no, "reg_no": formatted_reg_no, "ser_no": formatted_ser_no,
                "TRO": True, "plaintiff_id": plaintiff_id
            }
            # Try to get image with just the ser_no
            image_downloaded, image_source = await get_image_from_url(api_client, formatted_reg_no, formatted_ser_no, image_folder)
            if image_downloaded:
                new_row["image_source"] = image_source
        else:
            return  # Cannot proceed without data or formatted_ser_no
            
        # If image couldn't be retrieved via URL, try other methods
        if "image_source" not in new_row or not new_row["image_source"]:
            # Try to get image from status zip
            content_reg = await api_client.get_status_content(formatted_reg_no, id_key='rn', format='zip')
            status_zip_path = os.path.join(status_folder, f"{formatted_reg_no}.zip")
            
            if await save_content(content_reg, f"{formatted_reg_no}.zip", status_folder):
                image_downloaded = await get_image_from_status_zip(formatted_reg_no, status_zip_path, status_folder, image_folder)
                if image_downloaded:
                    new_row["image_source"] = "USPTO_STATUSZIP"
                else: # If still no image, try documents zip
                    documents_zip = await api_client.get_casedocs_bundle([formatted_reg_no], id_key='rn', format='zip')
                    documents_zip_path = os.path.join(document_folder, f"{formatted_reg_no}.zip")
                    
                    if await save_content(documents_zip, f"{formatted_reg_no}.zip", document_folder):
                        image_downloaded, certificate_found = await get_image_from_documents_zip(formatted_reg_no, documents_zip_path, document_folder, image_folder, certificate_folder)
                        if image_downloaded:
                            new_row["image_source"] = "USPTO_DOCZIP"
                        if certificate_found:
                            new_row["certificate_source"] = "USPTO"

        # Add metadata if available
        if metadata:
            new_row["metadata"] = metadata

        # Check if certificate exists or is needed
        if formatted_reg_no and not os.path.exists(os.path.join(certificate_folder, f"{formatted_reg_no}.webp")):
            certificate_needed.append(formatted_reg_no)
        else:
            new_row["certificate_source"] = "USPTO"
        
        # Save to database and send to NAS if this is a new trademark
        if not found:
            # Add to the sync needed set
            nas_sync_needed.add(formatted_reg_no)
            
            # Queue the database update (thread-safe)
            plaintiff_ids = [plaintiff_id] if plaintiff_id else []
            new_row["plaintiff_ids"] = plaintiff_ids
            
            # Queue operation to add/update trademark in database
            await queue.put(("save_trademark", {
                "formatted_reg_no": formatted_reg_no,
                "new_row": new_row
            }))
            
            # Add to result DataFrame through the queue
            await queue.put(("add_to_result", new_row))
            
        # Check for certificate in TRO_ALL folder
        if os.path.exists(os.path.join(base_folder, "CertificatesFromTRO_ALL", f"{formatted_reg_no}.webp")):
            os.makedirs(os.path.join(base_folder, "CertificatesFromTRO_Found"), exist_ok=True)
            shutil.copy(
                os.path.join(base_folder, "CertificatesFromTRO_ALL", f"{formatted_reg_no}.webp"), 
                os.path.join(base_folder, "CertificatesFromTRO_Found", f"{formatted_reg_no}.webp")
            )
        
        # Process certificates in batches
        if len(certificate_needed) >= 20:
            # Limit to maximum 23 certificates as per API limitation
            certificate_needed_copy = list(certificate_needed)[:23]
            # Remove only what we're processing
            for cert in certificate_needed_copy:
                if cert in certificate_needed:
                    certificate_needed.remove(cert)
            await queue.put(("certificate", certificate_needed_copy))
        
        await asyncio.sleep(0)  # Yield control to allow queue processing

    async def process_queue():
        nonlocal trademark_db_df, result_df  # Now we modify both dataframes in this function
        pending_nas_tasks = []  # Track NAS sync tasks
        pending_db_tasks = []   # Track database tasks
        
        while True:
            item = await queue.get()
            if item is None:  # Signal to stop
                # Wait for any pending tasks to complete before exiting
                pending_db_tasks = [t for t in pending_db_tasks if not t.done()] # Clean up completed database tasks
                if pending_db_tasks:
                    print(f"Waiting for {len(pending_db_tasks)} pending database tasks to complete...")
                    await asyncio.gather(*pending_db_tasks)
                
                pending_nas_tasks = [t for t in pending_nas_tasks if not t.done()] # Clean up completed NAS tasks
                if pending_nas_tasks:
                    print(f"Waiting for {len(pending_nas_tasks)} pending NAS tasks to complete...")
                    await asyncio.gather(*pending_nas_tasks)
                break
                
            item_type, item_data = item
            
            if item_type == "save_trademark":
                # Save to database
                formatted_reg_no = item_data["formatted_reg_no"]
                new_row = item_data["new_row"]
                
                # Create database save task and add to pending list
                db_task = asyncio.create_task(save_trademark_to_database(formatted_reg_no, new_row, db_connection, trademark_db_df))
                pending_db_tasks.append(db_task)
                
                # Create NAS sync task if needed
                if formatted_reg_no in nas_sync_needed:
                    nas_task = asyncio.create_task(send_trademark_to_nas(formatted_reg_no, nas, local_ip_folder, nas_ip_folder))
                    pending_nas_tasks.append(nas_task)
                
            elif item_type == "update_plaintiff_id":
                formatted_reg_no = item_data["formatted_reg_no"]
                plaintiff_id = item_data["plaintiff_id"]
                
                # Create database update task and add to pending list
                db_task = asyncio.create_task(update_trademark_plaintiff_ids(formatted_reg_no, plaintiff_id, db_connection, trademark_db_df))
                pending_db_tasks.append(db_task)
                print(f"\033[92m ✅ Updated plaintiff_ids for trademark {formatted_reg_no}\033[0m")
                    
            elif item_type == "add_to_result":
                # Add row to result DataFrame
                row_dict = item_data
                
                # Handle special fields and metadata
                row_for_df = row_dict.copy()
                
                # Add this row to the result DataFrame
                result_df = pd.concat([result_df, pd.DataFrame([row_for_df])], ignore_index=True)
                
                # If this is a duplicate, keep only the latest one
                if len(result_df[result_df["reg_no"] == row_for_df["reg_no"]]) > 1:
                    # Keep only the last row with this formatted_reg_no
                    duplicate_indices = result_df[result_df["reg_no"] == row_for_df["reg_no"]].index[:-1]
                    result_df = result_df.drop(duplicate_indices)
                
            elif item_type == "certificate":
                certificate_needed_copy = item_data
                print(f"🔥🔥🔥Requesting {len(certificate_needed_copy)} certificates from USPTO in a ZIP file.")
                reg_cert_pdf_content = await api_client.get_casedocs_bundle(certificate_needed_copy, id_key='rn', format='zip', option='category=RC')
                zip_filename = f"reg_cert_multi_{time.time()}.zip"
                saving_zip_result = await save_content(reg_cert_pdf_content, zip_filename, certificate_folder)
                if saving_zip_result:
                    # Use the result_df for unzipping certificates
                    unzip_and_extract_certificates(result_df, zip_filename, certificate_folder)
                    print(f"🔥✅🔥 Extracted certificates from the ZIP file.")
                    
                    # Update certificate_source in result_df
                    for cert_reg_no in certificate_needed_copy:
                        cert_path = os.path.join(certificate_folder, f"{cert_reg_no}.webp")
                        if os.path.exists(cert_path):
                            # Find the index in result_df for this registration number
                            idx = result_df[result_df["reg_no"] == cert_reg_no].index
                            if len(idx) > 0:
                                result_df.at[idx[0], "certificate_source"] = "USPTO"
                    
                    # Schedule NAS sync for these certificates
                    for cert_reg_no in certificate_needed_copy:
                        if cert_reg_no in nas_sync_needed:
                            nas_task = asyncio.create_task(
                                send_trademark_to_nas(cert_reg_no, nas, local_ip_folder, nas_ip_folder)
                            )
                            pending_nas_tasks.append(nas_task)
                            
            pending_nas_tasks = [t for t in pending_nas_tasks if not t.done()] # Clean up completed NAS tasks
            pending_db_tasks = [t for t in pending_db_tasks if not t.done()] # Clean up completed database tasks
                    
            queue.task_done()

    # Start the queue processing task
    queue_task = asyncio.create_task(process_queue())

    # Use semaphore to limit concurrent API calls
    semaphore = asyncio.Semaphore(10)
    tasks = [sem_task(semaphore, process_trademark(trademark)) for trademark in full_list_of_reg_no]
    
    # Process all trademarks
    await asyncio.gather(*tasks)
    
    # Process any remaining certificates
    if len(certificate_needed) > 0:
        await queue.put(("certificate", list(certificate_needed)))

    # Signal the queue processing task to stop and wait for it
    await queue.put(None)
    await queue_task
    await api_client.close_session()
    
    # Close database connection
    if db_connection:
        db_connection.close()
        
    # Close NAS connection
    if nas:
        nas.close()

    return result_df


# Image retrieval functions
async def get_image_from_url(api_client, formatted_reg_no, data, image_folder):
    """
    Retrieves trademark image using the USPTO URL.
    
    Args:
        api_client: The USPTO API client
        formatted_reg_no: Formatted registration number
        data: Trademark data dictionary with image_url
        image_folder: Folder path to save the image
        
    Returns:
        tuple: (success_bool, image_source)
    """
    image_path = os.path.join(image_folder, f"{formatted_reg_no}.webp")
    
    if not os.path.exists(image_path):
        if isinstance(data, dict) and "image_url" in data:
            image_data = await api_client.download_from_uspto(data["image_url"])
            if image_data:
                image = Image.open(io.BytesIO(image_data))
                image.save(image_path, "WEBP")
                return True, "USPTO_URL"
            else:
                print(f"🔥 Error: No image found for {formatted_reg_no}")
                return False, None
        elif isinstance(data, str):  # If data is a string, it's the ser_no
            image_url = f"https://tsdr.uspto.gov/img/{data}/large"
            image_data = await api_client.download_from_uspto(image_url)
            if image_data:
                image = Image.open(io.BytesIO(image_data))
                image.save(image_path, "WEBP")
                return True, "USPTO_URL"
            else:
                print(f"🔥 Error: No image found for {formatted_reg_no}")
                return False, None
        else:
            print(f"🔥 Error: No image URL found for {formatted_reg_no}")
            return False, None
    else:
        if isinstance(data, dict) and "pure_img_location" in data:
            return True, "USPTO_URL"
        return True, "USPTO_URL"


async def get_image_from_status_zip(formatted_reg_no, zip_path, status_folder, image_folder):
    """
    Extracts trademark image from a status zip file.
    
    Args:
        formatted_reg_no: Formatted registration number
        zip_path: Path to the zip file
        status_folder: Folder path for extracted status files
        image_folder: Folder path to save the image
        
    Returns:
        bool: True if image extraction was successful
    """
    # Extract zip file
    with zipfile.ZipFile(zip_path, 'r') as zip_ref:
        extracted_folder = os.path.join(status_folder, os.path.splitext(os.path.basename(zip_path))[0])
        zip_ref.extractall(extracted_folder)
    
    # Delete the zip file after extraction
    os.remove(zip_path)
    
    # Look for image files
    for file in os.listdir(extracted_folder):
        if file.lower().endswith(('.jpg', '.jpeg', '.png', '.webp', '.tif', '.tiff')):
            file_path = os.path.join(extracted_folder, file)
            if os.path.getsize(file_path) > 1024:  # Ensure file is not too small
                image = Image.open(file_path)
                image.save(os.path.join(image_folder, f"{formatted_reg_no}.webp"), "WEBP")
                print(f"✅ Image extracted from status zip for {formatted_reg_no}")
                return True
            else:
                print(f"🔥 Error: File {file} is too small to be an image")
    
    print(f"🔥 Error: No image found in status zip for {formatted_reg_no}")
    return False


async def get_image_from_documents_zip(formatted_reg_no, zip_path, document_folder, image_folder, certificate_folder=None):
    """
    Extracts trademark image and potentially certificate from a documents zip file.
    
    Args:
        formatted_reg_no: Formatted registration number
        zip_path: Path to the zip file
        document_folder: Folder path for extracted document files
        image_folder: Folder path to save the image
        certificate_folder: Optional folder path to save the certificate
        
    Returns:
        tuple: (image_found_bool, certificate_found_bool)
    """
    # Extract zip file
    with zipfile.ZipFile(zip_path, 'r') as zip_ref:
        extracted_folder = os.path.join(document_folder, os.path.splitext(os.path.basename(zip_path))[0])
        zip_ref.extractall(extracted_folder)
    
    # Delete the zip file after extraction
    os.remove(zip_path)
    
    image_found = False
    certificate_found = False
    
    # Process the extracted files
    for subfolder in os.listdir(extracted_folder):
        if not subfolder.isdigit():  # Skip non-numeric folders
            continue
            
        ser_no_folder = os.path.join(extracted_folder, subfolder)
        documents_folder_in_zip = os.path.join(ser_no_folder, "Documents")
        
        if not os.path.exists(documents_folder_in_zip):
            print(f"🔥 Error: No Documents folder found in {ser_no_folder}")
            continue
        
        # Look for drawing folder
        for folder_name in os.listdir(documents_folder_in_zip):
            # Process drawing images
            if "Drawing" in folder_name:
                drawing_folder = os.path.join(documents_folder_in_zip, folder_name)
                
                for image_file in os.listdir(drawing_folder):
                    if image_file.lower().endswith(('.png', '.jpg', '.jpeg', '.tiff', '.tif', '.webp')):
                        try:
                            image_path = os.path.join(drawing_folder, image_file)
                            image = Image.open(image_path)
                            image.save(os.path.join(image_folder, f"{formatted_reg_no}.webp"), "WEBP")
                            print(f"✅ Image extracted from documents zip for {formatted_reg_no}")
                            image_found = True
                            break
                        except Exception as e:
                            print(f"🔥 Error copying image from zip: {e}")
                
            # Process certificate if certificate_folder is provided
            if certificate_folder and "registration cert" in folder_name.lower():
                print(f"✅ Found registration certificate in Document Zip for {formatted_reg_no}")
                zip_certificate_folder = os.path.join(documents_folder_in_zip, folder_name)
                
                for file in os.listdir(zip_certificate_folder):
                    if file.endswith(".pdf"):
                        with fitz.open(os.path.join(zip_certificate_folder, file)) as pdf_document:
                            im_array = convert_page_number_to_image(pdf_document, 1)
                            cv2.imwrite(os.path.join(certificate_folder, f"{formatted_reg_no}.webp"), im_array)
                            certificate_found = True
                            break
                    elif file.lower().endswith(('.tif', '.tiff', '.jpg', '.jpeg', '.png')):
                        tif_path = os.path.join(zip_certificate_folder, file)
                        im_array = cv2.imread(tif_path)
                        cv2.imwrite(os.path.join(certificate_folder, f"{formatted_reg_no}.webp"), im_array)
                        certificate_found = True
                        break
    
    if not image_found:
        print(f"🔥 Error: No image found in Drawing folder in Document Zip for {formatted_reg_no}")
    
    return image_found, certificate_found

async def save_trademark_to_database(formatted_reg_no, new_row, db_connection, trademark_db_df):
    """
    Saves a new trademark to the database.
    
    Args:
        formatted_reg_no: Formatted registration number
        new_row: Dictionary containing trademark data
        db_connection: Database connection
        trademark_db_df: DataFrame cache of trademarks
        
    Returns:
        bool: True if save was successful
    """
    try:
        # Only save if we have meaningful data
        if not new_row.get("reg_no") or not new_row.get("ser_no"):
            print(f"⚠️ Insufficient data for trademark {formatted_reg_no}, not saving to database")
            return False
        
        # Create a copy for database storage, removing metadata which is not stored in DB
        db_row = new_row.copy()
        metadata = db_row.pop("metadata", None)  # Remove metadata if present
        
        # Convert list fields to JSON strings for MySQL
        for field in ["int_cls", "country_codes", "associated_marks", "plaintiff_ids"]:
            if field in db_row and isinstance(db_row[field], list):
                # Convert any NumPy int64 to native Python int
                if field in ["int_cls", "plaintiff_ids"] and db_row[field]:
                    db_row[field] = [int(val) for val in db_row[field]]
                db_row[field] = json.dumps(db_row[field])
            
        try:
            with db_connection.cursor() as cursor:
                # Check if all required fields are present
                required_fields = ["reg_no", "ser_no", "TRO", "applicant_name", "text", "int_cls", "date", 
                                "nb_suits", "country_codes", "associated_marks", "info_source", 
                                "image_source", "certificate_source", "plaintiff_ids"]
                
                # Set default values for missing fields
                for field in required_fields:
                    if field not in db_row or db_row[field] is None:
                        if field in ["int_cls", "country_codes", "associated_marks", "plaintiff_ids"]:
                            db_row[field] = json.dumps([])  # Empty JSON array
                        elif field in ["TRO"]:
                            db_row[field] = True
                        elif field in ["nb_suits"]:
                            db_row[field] = 0
                        else:
                            db_row[field] = ""
                
                cursor.execute(
                    "INSERT INTO tb_trademark (reg_no, ser_no, TRO, applicant_name, text, int_cls, date, nb_suits, country_codes, associated_marks, info_source, image_source, certificate_source, plaintiff_ids) "
                    "VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)", 
                    (db_row["reg_no"], db_row["ser_no"], db_row["TRO"], db_row["applicant_name"], 
                     db_row["text"], db_row["int_cls"], db_row["date"], db_row["nb_suits"], 
                     db_row["country_codes"], db_row["associated_marks"], db_row["info_source"], 
                     db_row["image_source"], db_row["certificate_source"], db_row["plaintiff_ids"])
                )
                db_connection.commit()
                
            # Add to the dataframe cache
            db_row_for_df = db_row.copy()
            # Convert JSON strings back to lists for the DataFrame
            for field in ["int_cls", "country_codes", "associated_marks", "plaintiff_ids"]:
                if field in db_row_for_df and isinstance(db_row_for_df[field], str):
                    try:
                        db_row_for_df[field] = json.loads(db_row_for_df[field])
                    except json.JSONDecodeError:
                        db_row_for_df[field] = []
                
            # Add back metadata for the DataFrame version if it existed
            if metadata:
                db_row_for_df["metadata"] = metadata
                
            # Add new row to trademark_db_df
            trademark_db_df = pd.concat([trademark_db_df, pd.DataFrame([db_row_for_df])], ignore_index=True)
            print(f"✅ Added trademark {formatted_reg_no} to database")
            return True
            
        except Exception as e:
            print(f"🔥 Error saving trademark to database: {str(e)}")
            return False
    except Exception as e:
        print(f"🔥 Error preparing trademark data for database: {str(e)}")
        return False

async def send_trademark_to_nas(formatted_reg_no, nas, local_ip_folder, nas_ip_folder):
    """
    Sends a single trademark's files to the NAS.
    
    Args:
        formatted_reg_no: Formatted registration number
        nas: NASConnection object
        local_ip_folder: Local IP folder path
        nas_ip_folder: NAS IP folder path
        
    Returns:
        bool: True if transfer was successful
    """
    try:
        # Define local paths
        local_xml_path = os.path.join(local_ip_folder, "Trademarks", "XML", f"{formatted_reg_no}.xml")
        local_image_path = os.path.join(local_ip_folder, "Trademarks", "Images", f"{formatted_reg_no}.webp")
        local_certificate_path = os.path.join(local_ip_folder, "Trademarks", "Certificates", f"{formatted_reg_no}.webp")
        
        # Define NAS paths
        nas_xml_path = f"{nas_ip_folder}/Trademarks/XML/{formatted_reg_no}.xml"
        nas_image_path = f"{nas_ip_folder}/Trademarks/Images/{formatted_reg_no}.webp"
        nas_certificate_path = f"{nas_ip_folder}/Trademarks/Certificates/{formatted_reg_no}.webp"
        
        if os.path.exists(local_xml_path) and not nas.ssh_exists(nas_xml_path):
            await asyncio.to_thread(nas.transfer_file_with_scp, local_path=local_xml_path, remote_path=nas_xml_path, to_nas=True)
            print(f"✅ Transferred trademark XML {formatted_reg_no} to NAS")
            
        if os.path.exists(local_image_path) and not nas.ssh_exists(nas_image_path):
            await asyncio.to_thread(nas.transfer_file_with_scp, local_path=local_image_path, remote_path=nas_image_path, to_nas=True)
            print(f"✅ Transferred trademark IMAGE {formatted_reg_no} to NAS")
            
        if os.path.exists(local_certificate_path) and not nas.ssh_exists(nas_certificate_path):
            await asyncio.to_thread(nas.transfer_file_with_scp, local_path=local_certificate_path, remote_path=nas_certificate_path, to_nas=True)
            print(f"✅ Transferred trademark CERTIFICATE {formatted_reg_no} to NAS")
            
        return True
    except Exception as e:
        print(f"🔥 Error sending trademark {formatted_reg_no} to NAS: {str(e)}")
        return False



async def update_trademark_plaintiff_ids(formatted_reg_no, plaintiff_id, db_connection, trademark_db_df):
    """
    Updates the plaintiff_ids for a trademark in the database.
    
    Args:
        formatted_reg_no: Formatted registration number
        plaintiff_id: ID of the plaintiff to add
        db_connection: Database connection
        trademark_db_df: Optional DataFrame cache of trademarks to check if update is needed
        
    Returns:
        bool: True if update was successful
    """

    row = trademark_db_df.loc[trademark_db_df['reg_no'] == formatted_reg_no]
    # Convert plaintiff_id to regular Python int to avoid numpy type issues
    plaintiff_id_int = int(plaintiff_id)
    

    # First check if update is already needed
    if not row.empty:
        current_plaintiff_ids_list = []
        if isinstance(row['plaintiff_ids'], str):
            try:
                loaded_ids = json.loads(row['plaintiff_ids'])
                if isinstance(loaded_ids, list):
                    current_plaintiff_ids_list = loaded_ids
            except (json.JSONDecodeError, TypeError):
                pass # Keep it as empty list if parsing fails
        elif isinstance(row['plaintiff_ids'], list):
            current_plaintiff_ids_list = row['plaintiff_ids']
        
        current_plaintiff_ids_list = [int(id_val) for id_val in current_plaintiff_ids_list if id_val is not None]

        if plaintiff_id_int in current_plaintiff_ids_list:
            return True # Exit the function early as the ID is already present



    # If we got here (not already in the plaintif_ids of the dataframe), we need to update the database
    max_retries = 3
    retry_count = 0
    
    # Convert plaintiff_id to regular Python int to avoid numpy type issues
    current_plaintiff_ids = []
    
    while retry_count < max_retries:
        try:
            # Check if connection is alive and reconnect if needed

            # Check if connection is alive and reconnect if needed
            if db_connection is None or not is_connection_alive(db_connection):
                print(f"\033[93m ⚠️ MySQL connection not available. Attempting to reconnect...\033[0m")
                db_connection = get_gz_connection()
                if db_connection is None:
                    raise Exception("Failed to establish database connection")
            
            # Get current plaintiff_ids
            with db_connection.cursor() as cursor:
                cursor.execute(
                    "SELECT plaintiff_ids FROM tb_trademark WHERE reg_no = %s", 
                    (formatted_reg_no,)
                )
                result = cursor.fetchone()
                
                if result:
                    # Convert plaintiff_id to regular Python int to avoid numpy type issues
                    plaintiff_id_int = int(plaintiff_id)
                    # Parse the JSON string to get the list
                    try:
                        current_plaintiff_ids = json.loads(result[0]) if result[0] else []
                        current_plaintiff_ids = [int(id_val) for id_val in current_plaintiff_ids]
                    except (json.JSONDecodeError, TypeError):
                        current_plaintiff_ids = []
                    
                    # Check if plaintiff_id is already in the list
                    if plaintiff_id_int not in current_plaintiff_ids:
                        # Add the plaintiff_id to the list then convert to string
                        updated_plaintiff_ids = current_plaintiff_ids + [plaintiff_id_int]
                        updated_plaintiff_ids_json = json.dumps(updated_plaintiff_ids)
                        
                        # Update the database
                        cursor.execute(
                            "UPDATE tb_trademark SET plaintiff_ids = %s WHERE reg_no = %s",
                            (updated_plaintiff_ids_json, formatted_reg_no)
                        )
                        db_connection.commit()
                        
                        print(f"\033[92m ✅ Updated plaintiff_ids for trademark {formatted_reg_no}\033[0m")
                    
                    # If we got here, operation was successful
                    return True
                return False  # Not found
            
            # If we got here, operation was successful, so break the retry loop
            return True
                    
        except Exception as e:
            retry_count += 1
            if retry_count < max_retries:
                print(f"\033[93m ⚠️ Error updating plaintiff_ids for trademark {formatted_reg_no}: {str(e)}. Retrying ({retry_count}/{max_retries})...\033[0m")
                time.sleep(2)  # Wait before retrying
            else:
                print(f"\033[91m 🔥 Error updating plaintiff_ids for trademark {formatted_reg_no}: {str(e)}. Max retries reached.\033[0m")
                return False


def get_trademark_from_NAS(formatted_reg_no, df_entry, nas):
    """
    Retrieves trademark files from local storage or NAS if needed.
    
    Args:
        formatted_reg_no: Formatted registration number
        df_entry: Dataframe entry for the trademark
        nas: NASConnection object
    
    Returns:
        tuple: (found_bool, xml_path, image_path, certificate_path)
    """
    # Get files from local or NAS
    xml_local_path = os.path.join(local_ip_folder, "Trademarks", "XML", f"{formatted_reg_no}.xml")
    image_local_path = os.path.join(local_ip_folder, "Trademarks", "Images", f"{formatted_reg_no}.webp")
    certificate_local_path = os.path.join(local_ip_folder, "Trademarks", "Certificates", f"{formatted_reg_no}.webp")
    
    # Check if all required files exist locally
    xml_exists = os.path.exists(xml_local_path)
    image_exists = os.path.exists(image_local_path)
    certificate_exists = os.path.exists(certificate_local_path)
    
    # If both main files exist locally, return success
    if xml_exists and image_exists and certificate_exists:
        print(f"✅ Trademark {formatted_reg_no} files found locally")
        return True, xml_local_path, image_local_path, certificate_local_path
    
    # If not, try to get from NAS
    files_transferred = False
    
    # Check which files we need to get from NAS
    nas_xml_path = f"{nas_ip_folder}/Trademarks/XML/{formatted_reg_no}.xml"
    nas_image_path = f"{nas_ip_folder}/Trademarks/Images/{formatted_reg_no}.webp"
    nas_certificate_path = f"{nas_ip_folder}/Trademarks/Certificates/{formatted_reg_no}.webp"
    
    # Create local directories if they don't exist
    os.makedirs(os.path.dirname(xml_local_path), exist_ok=True)
    os.makedirs(os.path.dirname(image_local_path), exist_ok=True)
    os.makedirs(os.path.dirname(certificate_local_path), exist_ok=True)
    
    # Transfer XML if needed
    if not xml_exists and nas.ssh_exists(nas_xml_path):
        nas.transfer_file_with_scp(
            local_path=xml_local_path,
            remote_path=nas_xml_path,
            to_nas=False
        )
        files_transferred = True
        xml_exists = True
    
    # Transfer image if needed
    if not image_exists and nas.ssh_exists(nas_image_path):
        nas.transfer_file_with_scp(
            local_path=image_local_path,
            remote_path=nas_image_path,
            to_nas=False
        )
        files_transferred = True
        image_exists = True
    
    # Transfer certificate if it exists
    if not certificate_exists and nas.ssh_exists(nas_certificate_path):
        nas.transfer_file_with_scp(
            local_path=certificate_local_path,
            remote_path=nas_certificate_path,
            to_nas=False
        )
        files_transferred = True
        certificate_exists = True
    
    if files_transferred:
        print(f"✅ Retrieved trademark {formatted_reg_no} files from NAS")
    
    # Check if we have the essential files now
    if xml_exists and image_exists and certificate_exists:
        return True, xml_local_path, image_local_path, certificate_local_path
    
    print(f"❌ Could not find complete files for trademark {formatted_reg_no}")
    return False, None, None, None


def unzip_and_extract_certificates(df_trademarks, zip_filename, certificate_folder):
    zip_path = os.path.join(certificate_folder, zip_filename)
    
    with TemporaryDirectory() as temp_dir:
        unzip_uspto(temp_dir, zip_path)

        for folder in os.listdir(temp_dir):  # reg_no folder
            zip_reg_no_folder = os.path.join(temp_dir, folder)
            
            # Find the row in the dataframe
            escaped_folder = re.escape(folder) # Escape special regex characters in the folder name
            
            df_row = df_trademarks[(df_trademarks["ser_no"].notna()) & (df_trademarks["ser_no"].str.contains(escaped_folder))]
            if len(df_row) == 0:
                df_row = df_trademarks[(df_trademarks["image_source"].notna()) & (df_trademarks["image_source"].str.contains(escaped_folder))]
            
            df_row = df_trademarks[(df_trademarks["ser_no"].notna()) & (df_trademarks["ser_no"].str.contains(folder))]
            if len(df_row) == 0:
                df_row = df_trademarks[(df_trademarks["image_source"].notna()) & (df_trademarks["image_source"].str.contains(folder))]
                if len(df_row) == 0:
                    df_na = df_trademarks[df_trademarks["image_source"].isna()]
                    df_row = df_na[df_na["ser_no"].apply(lambda x: str(x) in folder)]
                    if len(df_row) == 0:
                        df_row = df_trademarks[df_trademarks["reg_no"].apply(lambda x: str(x) in folder)]
                        if len(df_row) == 0:
                            print(f"🔥❌ Error: Found {len(df_row)} rows for {os.path.basename(zip_reg_no_folder)} in df_trademarks")
                            continue

            df_row_index = df_row.index[0]
            formatted_reg_no = df_row["reg_no"].iloc[0]
            

            zip_documents_folder = os.path.join(zip_reg_no_folder, os.listdir(zip_reg_no_folder)[0])

            # Reverse order: take the last certificate
            for cert_folder in os.listdir(zip_documents_folder)[::-1]:
                try:
                    zip_certificate_folder = os.path.join(zip_documents_folder, cert_folder)
                    file = os.listdir(zip_certificate_folder)[0]  # Take the first file. First PDF or if it is tif files the 1st one is the 1st page!
                    if file.endswith(".pdf"):
                        # Extract 1st page and save it as basename(zip_reg_no_folder).jpg
                        with fitz.open(os.path.join(zip_certificate_folder, file)) as pdf_document:
                            im_array = convert_page_number_to_image(pdf_document, 1)
                            cv2.imwrite(os.path.join(certificate_folder, f"{formatted_reg_no}.webp"), im_array)
                        df_trademarks.at[df_row_index, "certificate_source"] = "USPTO"
                        break
                    elif file.endswith(".tif") or file.endswith(".tiff") or file.endswith(".jpg") or file.endswith(".jpeg") or file.endswith(".png"):
                        tif_path = os.path.join(zip_certificate_folder, file)
                        im_array = cv2.imread(tif_path)
                        cv2.imwrite(os.path.join(certificate_folder, f"{formatted_reg_no}.webp"), im_array)
                        df_trademarks.at[df_row_index, "certificate_source"] = "USPTO"
                        break
                    else:
                        print(f"🔥❌ Error: Found {file} in {zip_certificate_folder}: no pdf or tif file")
                        continue
                except Exception as e:
                    print(f"🔥❌ Something went wrong in extring the certificate from the pdf => trying the next folder if there is one: {e}")

    os.remove(zip_path)


def unzip_uspto(temp_dir, zip_path):
# Why do we need this fundtion? Zip files from USPTO are malformed with some path using \ while others use / -> this works well on windows, not not on linux
    try:
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            for member_info in zip_ref.infolist():
                # Fix the path separators for the target OS
                target_path_parts = member_info.filename.split('\\')
                # Handle potential drive letters if created weirdly on Windows (e.g., C:\...)
                if len(target_path_parts) > 1 and target_path_parts[0].endswith(':'):
                    target_path_parts = target_path_parts[1:] # Remove drive letter part

                # Rejoin using the correct OS separator
                target_path = os.path.join(temp_dir, *target_path_parts)

                # Ensure parent directory exists
                if member_info.is_dir():
                    os.makedirs(target_path, exist_ok=True)
                else:
                    # Extract file: make sure parent dir exists first
                    parent_dir = os.path.dirname(target_path)
                    if parent_dir:
                        os.makedirs(parent_dir, exist_ok=True)

                    # Extract the file content to the corrected path
                    # Use extract method to get file content, then write manually
                    # This avoids potential issues with extract() trying to use the bad name
                    with zip_ref.open(member_info.filename) as source, \
                        open(target_path, "wb") as target:
                        target.write(source.read())

    except zipfile.BadZipFile:
        print(f"Error: {zip_path} is not a valid zip file or is corrupted.")
    except FileNotFoundError:
        print(f"Error: {zip_path} not found.")
    except Exception as e:
        print(f"An error occurred during extraction: {e}")


async def save_content(content, filename, folder, is_xml=False):
    if content:
        filepath = os.path.join(folder, filename)
        try:
            with open(filepath, 'wb') as f:
                f.write(content)
            print(f"✅ Saved {filename} to {filepath}")
            return True
        except Exception as e:
            print(f"🔥 Error saving {filename}: {e}")
            return False
    else:
        print(f"⚠️ No content received for {filename}, not saving.")
        return False
    


def create_full_list_of_reg_no(df_cases):
    api_client = TSDRApi()
    nas = NASConnection()
    full_list_of_reg_no = []
    certificates_folder = os.path.join(local_ip_folder, "Trademarks", "CertificatesFromTRO_ALL")
    os.makedirs(certificates_folder, exist_ok=True)
    for index, row in df_cases.iterrows():
        plaintiff_id = row["plaintiff_id"]
        if "trademarks" in row["images"].keys():
            for key, value in row["images"]["trademarks"].items():
                for i, reg_no in enumerate(row["images"]["trademarks"][key]["reg_no"]):
                    if reg_no != "":
                        # !!!!! How to know which one is which?
                        formatted_reg_no = api_client.format_id_number(reg_no, id_key='rn')

                        # destination = os.path.join(certificates_folder, f"{formatted_reg_no}.webp")
                        # if not os.path.exists(destination):
                        #     sanitized_case_name = sanitize_name(f"{pd.to_datetime(row['date_filed'], errors='coerce').strftime('%Y-%m-%d')} - {row['docket']}")
                        #     source = os.path.join(local_case_folder, sanitized_case_name, "images", row["images"]["trademarks"][key]["full_filename"][i])
                            
                        #     if os.path.exists(source):
                        #         shutil.copy(source, destination)
                        #     else:
                        #         print(f"🔥 Error: Source file {source} does not exist => copying the folder from NAS")
                        #         nas.ssh_nas_to_local(f"{nas_case_folder}/{sanitized_case_name}", os.path.join(local_case_folder, sanitized_case_name))
                        #         if os.path.exists(source):
                        #             shutil.copy(source, destination)
                        #         else:
                        #             print(f"🔥🔥🔥 Error: Source file {source} does not exist after copying the folder from NAS")
                        
                        
                        # Add to the list of registration numbers with plaintiff_id
                        full_list_of_reg_no.append({"plaintiff_id": plaintiff_id, "reg_no": formatted_reg_no})

    # Remove duplicates based on reg_no while preserving the first occurrence
    seen_reg_nos = set()
    unique_list = []
    for item in full_list_of_reg_no:
        if item["reg_no"] not in seen_reg_nos:
            seen_reg_nos.add(item["reg_no"])
            unique_list.append(item)

    print(f"There are {len(unique_list)} trademarks to scrape.")
    return unique_list



if __name__ == "__main__":
    df_cases = get_table_from_GZ("tb_case", force_refresh=False)
    df_plaintiff = get_table_from_GZ("tb_plaintiff", force_refresh=False)
    full_list_of_reg_no = create_full_list_of_reg_no(df_cases)
    
    # Process all trademarks using our optimized approach
    # This will update the database and return only the trademarks requested
    df_result = asyncio.run(get_trademarks_uspto(full_list_of_reg_no))
    
    print(f"Processed {len(df_result)} trademarks")
    
    # No need to update the database as it's already done in get_trademarks_uspto

    # Test with a subset for debugging
    # test_reg_numbers = [
    #     {"reg_no": "0626035", "plaintiff_id": 1}, 
    #     {"reg_no": "0902190", "plaintiff_id": 1}, 
    #     {"reg_no": "1177400", "plaintiff_id": 1}
    # ]
    # asyncio.run(get_trademarks_uspto(test_reg_numbers))

