import os
import shutil
import time
import shlex
from collections import defaultdict
from logdata import log_message

# Add project root to path to allow importing Common
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
from Common.Constants import local_case_folder, nas_case_folder

class NASSyncMixin:
    """
    Mixin class providing folder synchronization logic between local storage and NAS.
    Relies on methods from NASConnectionBase and NASTransferMixin.
    """

    # --- Helper Methods for Sync ---

    def _count_pdfs_local(self, local_path):
        """Recursively counts PDF files (case-insensitive) in a local directory."""
        pdf_count = 0
        if not os.path.isdir(local_path):
            log_message(f"NAS Sync: Local path is not a directory: {local_path}", level="warning")
            return 0
        try:
            for root, _, files in os.walk(local_path):
                for file in files:
                    if file.lower().endswith('.pdf'):
                        pdf_count += 1
            # log_message(f"NAS Sync: Counted {pdf_count} PDFs locally in {local_path}", level="debug")
            return pdf_count
        except Exception as e:
            log_message(f"NAS Sync: Error counting local PDFs in {local_path}: {e}", level="error")
            return 0 # Return 0 on error to avoid blocking sync

    def _count_pdfs_remote(self, remote_path):
        """Recursively counts PDF files (case-insensitive) in a remote directory via SSH."""
        # Relies on self.ssh_exists and self.ssh_execute_command from NASConnectionBase
        if not self.ssh_exists(remote_path):
            log_message(f"NAS Sync: Remote path does not exist for PDF count: {remote_path}", level="debug")
            return 0 # Folder doesn't exist, so 0 PDFs
        try:
            # Use shlex.quote for safety, although the path structure is somewhat controlled
            quoted_path = shlex.quote(f"/volume1{remote_path}")
            # Command breakdown:
            # find ... : search in the specified path
            # -type f : only find files
            # -iname "*.pdf" : case-insensitive match for PDF extension
            # | wc -l : pipe the list of files to word count (line count mode)
            command = f'find {quoted_path} -type f -iname "*.pdf" | wc -l'
            stdout, exit_code = self.ssh_execute_command(command)
            if exit_code == 0 and stdout and stdout.strip().isdigit():
                count = int(stdout.strip())
                # log_message(f"NAS Sync: Counted {count} PDFs remotely in {remote_path}", level="debug")
                return count
            else:
                # Handle cases where find returns nothing (exit code 0, empty stdout) or errors
                log_message(f"NAS Sync: Could not reliably count remote PDFs in {remote_path}. stdout: '{stdout}', exit_code: {exit_code}", level="warning")
                return 0 # Treat as 0 if count fails
        except ValueError:
            log_message(f"NAS Sync: Could not parse remote PDF count output: '{stdout}'", level="error")
            return 0
        except Exception as e:
            log_message(f"NAS Sync: Error counting remote PDFs in {remote_path}: {e}", level="error")
            return 0 # Return 0 on error

    def list_remote_directories(self, remote_base_path):
        """Lists top-level directories within a remote base path."""
        # Relies on self.ssh_execute_command from NASConnectionBase
        directories = []
        try:
            # Use shlex.quote for safety
            quoted_path = shlex.quote(f"/volume1{remote_base_path}")
            # Command breakdown:
            # find ... : search in the specified path
            # -maxdepth 1 : do not go into subdirectories
            # -mindepth 1 : do not include the base path itself
            # -type d : only find directories
            command = f'find {quoted_path} -maxdepth 1 -mindepth 1 -type d'
            stdout, exit_code = self.ssh_execute_command(command)

            if exit_code == 0 and stdout:
                full_paths = stdout.strip().split('\n')
                # Extract only the directory name relative to remote_base_path
                base_prefix = f"/volume1{remote_base_path}/" # Ensure trailing slash
                for full_path in full_paths:
                    if full_path.startswith(base_prefix):
                        dir_name = full_path[len(base_prefix):]
                        if dir_name: # Avoid empty strings if something goes wrong
                            directories.append(dir_name)
                log_message(f"NAS Sync: Found remote directories in {remote_base_path}: {len(directories)}", level="debug")
            elif exit_code != 0:
                 log_message(f"NAS Sync: Failed to list remote directories in {remote_base_path}. exit_code: {exit_code}", level="warning")
            # If stdout is empty and exit_code is 0, it means no subdirectories were found, which is valid.

        except Exception as e:
            log_message(f"NAS Sync: Error listing remote directories in {remote_base_path}: {e}", level="error")
            # Return empty list on error

        return directories


    def _get_nas_pdf_counts_by_folder(self, base_remote_path):
        """
        Gets a dictionary of {folder_name: pdf_count} for all top-level folders
        under the base_remote_path by executing one find command on the NAS.
        """
        # Relies on self.ssh_execute_command from NASConnectionBase
        pdf_counts = {}
        try:
            log_message(f"NAS Sync Test: Fetching all PDF paths under {base_remote_path}...", level="info")
            quoted_base_path = shlex.quote(f"/volume1{base_remote_path}")
            command = f'find {quoted_base_path} -type f -iname "*.pdf"'
            stdout, exit_code = self.ssh_execute_command(command)

            if exit_code != 0:
                log_message(f"NAS Sync Test: Failed to execute find command for PDF paths. Exit code: {exit_code}", level="error")
                return {} # Return empty dict on command failure

            if not stdout:
                log_message(f"NAS Sync Test: No PDF files found under {base_remote_path}.", level="info")
                return {} # No PDFs found

            all_pdf_paths = stdout.strip().split('\n')

            # Process the paths to count PDFs per top-level directory
            base_path_prefix = f"/volume1{base_remote_path}/" # Ensure trailing slash
            counts_by_folder = defaultdict(int)

            for pdf_path in all_pdf_paths:
                if not pdf_path.startswith(base_path_prefix):
                    log_message(f"NAS Sync Test: Skipping unexpected path format: {pdf_path}", level="warning")
                    continue

                # Extract the part of the path after the base prefix
                relative_path = pdf_path[len(base_path_prefix):]
                # Find the first directory component
                first_slash_index = relative_path.find('/')
                if first_slash_index != -1:
                    top_level_folder = relative_path[:first_slash_index]
                    counts_by_folder[top_level_folder] += 1
                else:
                    # Handle PDF directly in base_remote_path (ignore for folder counts)
                    pass

            pdf_counts = dict(counts_by_folder)
            log_message(f"NAS Sync Test: Processed PDF counts by folder: {len(pdf_counts)} folders.", level="info")

        except Exception as e:
            log_message(f"NAS Sync Test: Error getting NAS PDF counts: {e}", level="error")
            return {} # Return empty dict on error

        return pdf_counts

    def _get_local_pdf_counts_by_folder(self, base_local_path):
        """
        Gets a dictionary of {folder_name: pdf_count} for all top-level folders
        under the base_local_path by walking the local filesystem. Only walks
        one level deep to find folders, then recursively counts PDFs within each.
        """
        pdf_counts = {}
        log_message(f"NAS Sync Test: Getting local PDF counts under {base_local_path}...", level="info")
        try:
            if not os.path.isdir(base_local_path):
                log_message(f"NAS Sync Test: Base local path is not a directory: {base_local_path}", level="error")
                return {}

            top_level_folders = []
            for item in os.listdir(base_local_path):
                item_path = os.path.join(base_local_path, item)
                if os.path.isdir(item_path):
                    top_level_folders.append(item)

            for folder_name in top_level_folders:
                folder_path = os.path.join(base_local_path, folder_name)
                count = 0
                try:
                    for root, _, files in os.walk(folder_path):
                        for file in files:
                            if file.lower().endswith('.pdf'):
                                count += 1
                    pdf_counts[folder_name] = count
                except Exception as e:
                    log_message(f"NAS Sync Test: Error counting PDFs in local folder {folder_name}: {e}", level="error")
                    pdf_counts[folder_name] = 0 # Record 0 count on error for this folder

            log_message(f"NAS Sync Test: Finished getting local PDF counts: {len(pdf_counts)} folders processed.", level="info")

        except Exception as e:
            log_message(f"NAS Sync Test: Error getting local PDF counts by folder: {e}", level="error")
            return {} # Return empty dict on major error

        return pdf_counts

    # --- Synchronization Functions ---

    def sync_local_to_nas(self):
        """
        Synchronizes folders from the local case folder to the NAS case folder.
        Checks for existence and compares PDF counts before transferring.
        (Original version, iterates and counts individually)
        Relies on methods from NASConnectionBase and NASTransferMixin.
        """
        log_message("NAS Sync: Starting sync LOCAL -> NAS", level="info")
        # Relies on self.ensure_connection from NASConnectionBase
        self.ensure_connection()
        folders_processed = 0
        folders_transferred = 0

        try:
            local_folders = [
                f for f in os.listdir(local_case_folder)
                if os.path.isdir(os.path.join(local_case_folder, f))
            ]
        except FileNotFoundError:
            log_message(f"NAS Sync: Local case folder not found: {local_case_folder}", level="error")
            return
        except Exception as e:
            log_message(f"NAS Sync: Error listing local folders in {local_case_folder}: {e}", level="error")
            return

        for folder_name in local_folders:
            local_folder_path = os.path.join(local_case_folder, folder_name)
            remote_folder_path = f"{nas_case_folder}/{folder_name}" # Uses constant directly
            folders_processed += 1

            try:
                # Relies on self.ssh_exists from NASConnectionBase
                remote_exists = self.ssh_exists(remote_folder_path)

                if remote_exists:
                    local_pdf_count = self._count_pdfs_local(local_folder_path)
                    remote_pdf_count = self._count_pdfs_remote(remote_folder_path) # Relies on self._count_pdfs_remote

                    log_message(f"NAS Sync: Counts for {folder_name}: Local={local_pdf_count}, Remote={remote_pdf_count}", level="info")

                    if local_pdf_count > remote_pdf_count:
                        log_message(f"NAS Sync: Local PDF count ({local_pdf_count}) > Remote ({remote_pdf_count}). Transferring {folder_name} LOCAL -> NAS.", level="info")
                        # Relies on self.ssh_local_to_nas from this Mixin
                        self.ssh_local_to_nas(local_folder_path, remote_folder_path)
                        folders_transferred += 1
                    else:
                        log_message(f"NAS Sync: Skipping transfer for {folder_name}. Local PDF count ({local_pdf_count}) <= Remote ({remote_pdf_count}).", level="info")
                else:
                    log_message(f"NAS Sync: Remote folder does not exist: {remote_folder_path}. Transferring {folder_name} LOCAL -> NAS.", level="info")
                    # Check if local folder is empty before transferring
                    if not os.listdir(local_folder_path):
                         log_message(f"NAS Sync: Skipping transfer for empty local folder: {folder_name}", level="info")
                    else:
                        # Relies on self.ssh_local_to_nas from this Mixin
                        self.ssh_local_to_nas(local_folder_path, remote_folder_path)
                        folders_transferred += 1

            except Exception as e:
                log_message(f"NAS Sync: Error processing folder {folder_name} for LOCAL -> NAS sync: {e}", level="error")
                # Continue to the next folder

        log_message(f"NAS Sync: Finished sync LOCAL -> NAS. Processed: {folders_processed}, Transferred: {folders_transferred}", level="info")


    def sync_nas_to_local(self):
        """
        Synchronizes folders from the NAS case folder to the local case folder.
        Checks for existence and compares PDF counts before transferring.
        (Original version, iterates and counts individually)
        Relies on methods from NASConnectionBase and this Mixin.
        """
        log_message("NAS Sync: Starting sync NAS -> LOCAL", level="info")
        # Relies on self.ensure_connection from NASConnectionBase
        self.ensure_connection()
        folders_processed = 0
        folders_transferred = 0

        # Relies on self.list_remote_directories from this Mixin
        remote_folders = self.list_remote_directories(nas_case_folder)

        for folder_name in remote_folders:
            remote_folder_path = f"{nas_case_folder}/{folder_name}" # Uses constant directly
            local_folder_path = os.path.join(local_case_folder, folder_name)
            folders_processed += 1

            try:
                local_exists = os.path.exists(local_folder_path)

                if local_exists:
                    if not os.path.isdir(local_folder_path):
                        log_message(f"NAS Sync: Local path exists but is not a directory: {local_folder_path}. Skipping.", level="warning")
                        continue # Skip if it's a file

                    local_pdf_count = self._count_pdfs_local(local_folder_path)
                    remote_pdf_count = self._count_pdfs_remote(remote_folder_path) # Relies on self._count_pdfs_remote

                    log_message(f"NAS Sync: Counts for {folder_name}: Local={local_pdf_count}, Remote={remote_pdf_count}", level="info")

                    if remote_pdf_count > local_pdf_count:
                        log_message(f"NAS Sync: Remote PDF count ({remote_pdf_count}) > Local ({local_pdf_count}). Transferring {folder_name} NAS -> LOCAL.", level="info")
                        # Use ssh_nas_to_local which handles zipping/unzipping (from this Mixin)
                        if self.ssh_nas_to_local(remote_folder_path, local_folder_path):
                            folders_transferred += 1
                        else:
                            log_message(f"NAS Sync: Transfer failed for {folder_name} NAS -> LOCAL.", level="error")
                    else:
                        log_message(f"NAS Sync: Skipping transfer for {folder_name}. Remote PDF count ({remote_pdf_count}) <= Local ({local_pdf_count}).", level="info")
                else:
                    log_message(f"NAS Sync: Local folder does not exist: {local_folder_path}. Transferring {folder_name} NAS -> LOCAL.", level="info")
                    # Use ssh_nas_to_local which handles zipping/unzipping (from this Mixin)
                    # Need to ensure the base local directory exists before unzipping into it
                    os.makedirs(os.path.dirname(local_folder_path), exist_ok=True)
                    if self.ssh_nas_to_local(remote_folder_path, local_folder_path):
                         folders_transferred += 1
                    else:
                         log_message(f"NAS Sync: Transfer failed for {folder_name} NAS -> LOCAL.", level="error")

            except Exception as e:
                log_message(f"NAS Sync: Error processing folder {folder_name} for NAS -> LOCAL sync: {e}", level="error")
                # Continue to the next folder

        log_message(f"NAS Sync: Finished sync NAS -> LOCAL. Processed: {folders_processed}, Transferred: {folders_transferred}", level="info")


    def sync_local_to_nas_test(self):
        """
        Alternative sync LOCAL -> NAS using pre-fetched dictionaries of PDF counts
        and progress reporting.
        Relies on methods from NASConnectionBase and this Mixin.
        """
        log_message("NAS Sync Test: Starting sync LOCAL -> NAS (Test Version)", level="info")
        # Relies on self.ensure_connection from NASConnectionBase
        self.ensure_connection()
        folders_transferred = 0
        folders_to_transfer = []

        # 1. Fetch all local and remote PDF counts at once
        local_pdf_counts = self._get_local_pdf_counts_by_folder(local_case_folder)
        remote_pdf_counts = self._get_nas_pdf_counts_by_folder(nas_case_folder)

        log_message(f"NAS Sync Test: Local counts: {len(local_pdf_counts)} folders.", level="debug")
        log_message(f"NAS Sync Test: Remote counts: {len(remote_pdf_counts)} folders.", level="debug")

        # 2. Determine folders to transfer
        all_folder_names = set(local_pdf_counts.keys()) # Only consider folders present locally for local->nas sync

        for folder_name in all_folder_names:
            local_count = local_pdf_counts.get(folder_name, 0) # Default to 0 if somehow missing after listing
            remote_count = remote_pdf_counts.get(folder_name, -1) # Use -1 to indicate remote folder doesn't exist

            # Logic: Transfer if local exists AND (remote doesn't exist OR local > remote)
            should_transfer = False
            if remote_count == -1: # Remote folder doesn't exist
                if local_count > 0: # Transfer  if local count > 0
                   should_transfer = True
                   log_message(f"NAS Sync Test: Plan transfer {folder_name} (Local exists {local_count}, Remote doesn't)", level="debug")
            elif local_count > remote_count: # Remote exists, compare counts
                should_transfer = True
                log_message(f"NAS Sync Test: Plan transfer {folder_name} (Local {local_count} > Remote {remote_count})", level="debug")

            if should_transfer:
                folders_to_transfer.append(folder_name)

        total_to_transfer = len(folders_to_transfer)
        log_message(f"NAS Sync Test: Identified {total_to_transfer} folders to transfer LOCAL -> NAS.", level="info")

        # 3. Perform transfers with progress
        for index, folder_name in enumerate(folders_to_transfer):
            local_folder_path = os.path.join(local_case_folder, folder_name)
            remote_folder_path = f"{nas_case_folder}/{folder_name}" # For transfer target

            print(f"\n📥Transferring LOCAL->NAS ({index + 1}/{total_to_transfer}): {folder_name}")
            log_message(f"NAS Sync Test: Transferring LOCAL->NAS ({index + 1}/{total_to_transfer}): {folder_name}", level="info")

            try:
                # Relies on self.ssh_local_to_nas from this Mixin
                self.ssh_local_to_nas(local_folder_path, remote_folder_path)
                folders_transferred += 1
            except Exception as e:
                log_message(f"NAS Sync Test: FAILED Transfer LOCAL->NAS for {folder_name}: {e}", level="error")
                print(f"\033[91mERROR Transferring LOCAL->NAS ({index + 1}/{total_to_transfer}): {folder_name} - {e}\033[0m")
                # Decide whether to continue or stop on error. Let's continue for now.

        log_message(f"NAS Sync Test: Finished sync LOCAL -> NAS (Test Version). Attempted: {total_to_transfer}, Succeeded: {folders_transferred}", level="info")


    def sync_nas_to_local_test(self):
        """
        Alternative sync NAS -> LOCAL using pre-fetched dictionaries of PDF counts
        and progress reporting.
        Relies on methods from NASConnectionBase and this Mixin.
        """
        log_message("NAS Sync Test: Starting sync NAS -> LOCAL (Test Version)", level="info")
        # Relies on self.ensure_connection from NASConnectionBase
        self.ensure_connection()
        folders_transferred = 0
        folders_to_transfer = []

        # 1. Fetch all local and remote PDF counts at once
        local_pdf_counts = self._get_local_pdf_counts_by_folder(local_case_folder)
        remote_pdf_counts = self._get_nas_pdf_counts_by_folder(nas_case_folder)

        log_message(f"NAS Sync Test: Local counts: {len(local_pdf_counts)} folders.", level="debug")
        log_message(f"NAS Sync Test: Remote counts: {len(remote_pdf_counts)} folders.", level="debug")

        # 2. Determine folders to transfer
        all_folder_names = set(remote_pdf_counts.keys()) # Only consider folders present remotely for nas->local sync

        for folder_name in all_folder_names:
            local_count = local_pdf_counts.get(folder_name, -1) # Use -1 to indicate local folder doesn't exist
            remote_count = remote_pdf_counts.get(folder_name, 0) # Default to 0 if somehow missing after listing

            # Logic: Transfer if remote exists AND (local doesn't exist OR remote > local)
            should_transfer = False
            if local_count == -1: # Local folder doesn't exist
                if remote_count >= 0: # Transfer even if remote count is 0, as local doesn't exist
                    should_transfer = True
                    log_message(f"NAS Sync Test: Plan transfer {folder_name} (Remote exists {remote_count}, Local doesn't)", level="debug")
            elif remote_count > local_count: # Local exists, compare counts
                should_transfer = True
                log_message(f"NAS Sync Test: Plan transfer {folder_name} (Remote {remote_count} > Local {local_count})", level="debug")

            if should_transfer:
                folders_to_transfer.append(folder_name)

        total_to_transfer = len(folders_to_transfer)
        log_message(f"NAS Sync Test: Identified {total_to_transfer} folders to transfer NAS -> LOCAL.", level="info")

        # 3. Perform transfers with progress
        for index, folder_name in enumerate(folders_to_transfer):
            remote_folder_path = f"{nas_case_folder}/{folder_name}" # For transfer source
            local_folder_path = os.path.join(local_case_folder, folder_name)

            print(f"\n📥Transferring NAS->LOCAL ({index + 1}/{total_to_transfer}): {folder_name}")
            log_message(f"NAS Sync Test: Transferring NAS->LOCAL ({index + 1}/{total_to_transfer}): {folder_name}", level="info")

            try:
                # Ensure the base local directory exists before transfer/unzipping
                os.makedirs(os.path.dirname(local_folder_path), exist_ok=True)
                # Relies on self.ssh_nas_to_local from this Mixin
                if self.ssh_nas_to_local(remote_folder_path, local_folder_path):
                    folders_transferred += 1
                else:
                    # ssh_nas_to_local logs its own errors, just log failure here
                    log_message(f"NAS Sync Test: FAILED Transfer NAS->LOCAL for {folder_name} (see previous errors).", level="error")
                    print(f"\033[91mERROR Transferring NAS->LOCAL ({index + 1}/{total_to_transfer}): {folder_name} - Check logs\033[0m")
            except Exception as e:
                log_message(f"NAS Sync Test: FAILED Transfer NAS->LOCAL for {folder_name}: {e}", level="error")
                print(f"\033[91mERROR Transferring NAS->LOCAL ({index + 1}/{total_to_transfer}): {folder_name} - {e}\033[0m")
                # Decide whether to continue or stop on error. Let's continue for now.

        log_message(f"NAS Sync Test: Finished sync NAS -> LOCAL (Test Version). Attempted: {total_to_transfer}, Succeeded: {folders_transferred}", level="info")

    # --- Transfer Methods involving Zipping (Moved here as they are sync-related) ---

    def ssh_local_to_nas(self, local_folder, remote_folder):
        """
        Transfers a local folder to NAS by zipping locally, transferring, and unzipping remotely.
        Relies on methods from NASConnectionBase and NASTransferMixin.
        """
        max_retries = 3
        retry_delay = 2
        log_message(f"NAS Sync: Transferring LOCAL -> NAS: '{local_folder}' -> '{remote_folder}'", level="info")

        for attempt in range(max_retries):
            local_zip_path = None # Define outside try block for cleanup
            remote_zip_path = None
            try:
                self.ensure_connection() # From Base

                # Create zip locally
                zip_file_name = f"{os.path.basename(os.path.normpath(local_folder))}"
                # Place zip in a temporary location or parent dir? Using parent of local_case_folder for now.
                temp_zip_dir = os.path.dirname(local_case_folder)
                local_zip_base = os.path.join(temp_zip_dir, zip_file_name)
                shutil.make_archive(local_zip_base, 'zip', local_folder)
                local_zip_path = local_zip_base + '.zip'
                remote_zip_path = f"{nas_case_folder}/{zip_file_name}.zip" # Remote zip in base case folder

                if not os.path.exists(local_zip_path):
                    raise FileNotFoundError(f"Local zip file not found after creation: {local_zip_path}")

                # Transfer zip file (using SCP or Rsync from Rsync Mixin)
                if hasattr(self, 'transfer_file_with_scp'):
                     self.transfer_file_with_scp(local_path=local_zip_path, remote_path=remote_zip_path, to_nas=True)
                elif hasattr(self, 'transfer_file_with_rsync'):
                    self.transfer_file_with_rsync(local_path=local_zip_path, remote_path=remote_zip_path, to_nas=True)
                else:
                     raise NotImplementedError("No SCP or Rsync transfer method available.")


                # Verify transfer (from Base)
                if not self.ssh_exists(remote_zip_path):
                     time.sleep(1)
                     if not self.ssh_exists(remote_zip_path):
                         raise FileNotFoundError(f"Remote zip file not found on NAS after transfer: {remote_zip_path}")

                # Ensure target remote directory exists (from Base)
                self.create_remote_directory(remote_folder)

                # Unzip remotely using 7z (from Base)
                command = f'7z x "/volume1{remote_zip_path}" -o"/volume1{remote_folder}" -y'
                self.ssh_execute_command(command)

                # Cleanup (from Base and os)
                self.ssh_remove_file(remote_zip_path)
                os.remove(local_zip_path)

                log_message(f"NAS Sync: Transfer LOCAL -> NAS successful for '{local_folder}'", level="info")
                return  # Exit the function if successful

            except Exception as e:
                log_message(f"NAS Sync: Attempt {attempt + 1}/{max_retries} failed for ssh_local_to_nas ('{local_folder}' -> '{remote_folder}'): {e}", level="warning")
                # Attempt cleanup before retry
                if local_zip_path and os.path.exists(local_zip_path):
                    try: os.remove(local_zip_path)
                    except OSError: pass
                if remote_zip_path and hasattr(self, 'ssh_exists') and self.ssh_exists(remote_zip_path):
                     try: self.ssh_remove_file(remote_zip_path)
                     except Exception: pass

                if attempt < max_retries - 1:
                    time.sleep(retry_delay * (attempt + 1)) # Exponential backoff might be better
                else:
                    log_message(f"NAS Sync: FAILED to transfer LOCAL -> NAS '{local_folder}' after {max_retries} attempts.", level="error")
                    raise Exception(f"Failed to transfer and unzip {local_folder} to {remote_folder} after {max_retries} attempts") from e


    def ssh_nas_to_local(self, remote_folder, local_folder, file_type=None):
        """
        Transfers a folder from NAS to local by zipping remotely, downloading, and unzipping locally.
        Relies on methods from NASConnectionBase and NASTransferMixin.
        """
        # Relies on self.ensure_connection, _zip_remote_folder, ssh_remove_file, _unzip_local_file from Base
        # Relies on transfer_file_with_scp/rsync from Rsync Mixin
        log_message(f"NAS Sync: Transferring NAS -> LOCAL: '{remote_folder}' -> '{local_folder}'", level="info")
        self.ensure_connection()
        remote_zip_path = None
        local_zip_path = None
        try:
            # Zip remotely (from Base)
            remote_zip_path = self._zip_remote_folder(remote_folder, file_type)
            zip_file_name = os.path.basename(remote_zip_path)

            # Determine local zip path (e.g., in temp or parent of local_case_folder)
            temp_zip_dir = os.path.dirname(local_case_folder)
            local_zip_path = os.path.join(temp_zip_dir, zip_file_name)

            # Ensure local zip path directory exists
            os.makedirs(os.path.dirname(local_zip_path), exist_ok=True)

            # Download zip (from Rsync Mixin)
            if hasattr(self, 'transfer_file_with_scp'):
                 self.transfer_file_with_scp(local_path=local_zip_path, remote_path=remote_zip_path, to_nas=False)
            elif hasattr(self, 'transfer_file_with_rsync'):
                 self.transfer_file_with_rsync(local_path=local_zip_path, remote_path=remote_zip_path, to_nas=False)
            else:
                 raise NotImplementedError("No SCP or Rsync transfer method available.")

            # Ensure the target local *directory* exists before unzipping
            os.makedirs(local_folder, exist_ok=True)

            # Unzip locally (from Base)
            self._unzip_local_file(local_zip_path, local_folder)

            # Cleanup (from Base and os)
            os.remove(local_zip_path)
            self.ssh_remove_file(remote_zip_path)

            log_message(f"NAS Sync: Transfer NAS -> LOCAL successful for '{remote_folder}'", level="info")
            return True

        except Exception as e:
            log_message(f"NAS Sync: FAILED ssh_nas_to_local ('{remote_folder}' -> '{local_folder}'): {e}", level="error")
            # Attempt cleanup of partial files
            if local_zip_path and os.path.exists(local_zip_path):
                try: os.remove(local_zip_path)
                except OSError as cleanup_e: log_message(f"NAS Sync: Error cleaning up local zip '{local_zip_path}': {cleanup_e}", level="warning")
            if remote_zip_path and hasattr(self, 'ssh_exists') and self.ssh_exists(remote_zip_path):
                 try: self.ssh_remove_file(remote_zip_path)
                 except Exception as cleanup_e: log_message(f"NAS Sync: Error cleaning up remote zip '{remote_zip_path}': {cleanup_e}", level="warning")
            return False


    def ssh_nas_to_local_aggregated(self, remote_folders, local_base_folder, file_type=None):
        """
        Transfers multiple folders from NAS to local by creating a single 7z archive remotely,
        downloading it, and extracting locally.
        Relies on methods from NASConnectionBase and NASTransferMixin.
        """
        # Relies on ensure_connection, ssh_execute_command, ssh_remove_file, _unzip_local_file from Base
        # Relies on transfer_file_with_scp/rsync from Rsync Mixin
        log_message(f"NAS Sync: Starting aggregated transfer NAS -> LOCAL for {len(remote_folders)} folders.", level="info")
        self.ensure_connection()
        remote_zip_path = None
        local_zip_path = None

        if not remote_folders:
            log_message("NAS Sync: No remote folders provided for aggregated transfer.", level="warning")
            return False

        try:
            # Construct the zip command with multiple input folders
            zip_file_name = f"aggregated_{int(time.time())}.zip"
            # Place remote zip in nas_case_folder temporarily
            remote_zip_path = f"{nas_case_folder}/{zip_file_name}"
            # Place local zip in the target local_base_folder temporarily
            local_zip_path = os.path.join(local_base_folder, zip_file_name)

            # Ensure local base folder exists
            os.makedirs(local_base_folder, exist_ok=True)

            # Build the 7z command. Paths need /volume1 prefix.
            zip_command = f'7z a "/volume1{remote_zip_path}" '
            folder_paths_for_7z = []
            for folder in remote_folders:
                 # Basic validation
                 if not folder or not isinstance(folder, str):
                      log_message(f"NAS Sync: Invalid folder path skipped: {folder}", level="warning")
                      continue
                 # Check existence? Maybe too slow here. Assume paths are valid.
                 folder_paths_for_7z.append(f'"/volume1{folder}"')

            if not folder_paths_for_7z:
                 log_message("NAS Sync: No valid remote folders to aggregate.", level="error")
                 return False

            zip_command += ' '.join(folder_paths_for_7z)
            zip_command += ' -r -x!"@eaDir" -y' # Recursive, exclude Synology folders, assume yes

            # Optional file type inclusion (consider if needed, complex with multiple sources)
            # if file_type:
            #     zip_command += f' -ir!"{file_type}"'

            log_message(f"NAS Sync: Executing remote aggregation zip command...", level="debug") # Avoid logging full command if too long
            self.ssh_execute_command(zip_command) # From Base

            # Verify remote zip creation
            if not self.ssh_exists(remote_zip_path):
                 time.sleep(1)
                 if not self.ssh_exists(remote_zip_path):
                     raise FileNotFoundError(f"Aggregated remote zip file not found after zipping: {remote_zip_path}")

            # Transfer the zip file (from Rsync Mixin)
            if hasattr(self, 'transfer_file_with_scp'):
                 self.transfer_file_with_scp(local_path=local_zip_path, remote_path=remote_zip_path, to_nas=False)
            elif hasattr(self, 'transfer_file_with_rsync'):
                 self.transfer_file_with_rsync(local_path=local_zip_path, remote_path=remote_zip_path, to_nas=False)
            else:
                 raise NotImplementedError("No SCP or Rsync transfer method available.")

            # Unzip the local file (from Base)
            self._unzip_local_file(local_zip_path, local_base_folder)

            # Clean up (from Base and os)
            os.remove(local_zip_path)
            self.ssh_remove_file(remote_zip_path)

            log_message(f"NAS Sync: Aggregated transfer NAS -> LOCAL successful.", level="info")
            return True

        except Exception as e:
            log_message(f"NAS Sync: FAILED aggregated transfer NAS -> LOCAL: {e}", level="error")
            # Attempt cleanup
            if local_zip_path and os.path.exists(local_zip_path):
                try: os.remove(local_zip_path)
                except OSError as cleanup_e: log_message(f"NAS Sync: Error cleaning up local aggregated zip '{local_zip_path}': {cleanup_e}", level="warning")
            if remote_zip_path and hasattr(self, 'ssh_exists') and self.ssh_exists(remote_zip_path):
                 try: self.ssh_remove_file(remote_zip_path)
                 except Exception as cleanup_e: log_message(f"NAS Sync: Error cleaning up remote aggregated zip '{remote_zip_path}': {cleanup_e}", level="warning")
            return False