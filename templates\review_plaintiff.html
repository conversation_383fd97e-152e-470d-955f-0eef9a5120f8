<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Review Case Plaintiff</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='visualizer.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <style>
        .proposed-name-cell {
            max-width: 150px;
            width: 150px;
        }
        .method-info-cell {
            min-width: 250px;
        }
        .actions-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .left-buttons {
            display: flex;
            gap: 10px;
        }
        .right-buttons {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        /* Overlay styles */
        .overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }
        .overlay-content {
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            max-width: 80%;
            max-height: 80%;
            overflow-y: auto;
        }
        .overlay-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
        }
        .overlay-list {
            max-height: 400px;
            overflow-y: auto;
            margin-bottom: 15px;
        }
        .overlay-buttons {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <div class="main-menu">
            {% include 'common/menu.html' %}
            <script>
                document.getElementById('menu-review-plaintiff').classList.add('active');
            </script>
        </div>

        <div class="filters-container">
            <div class="actions-row">
                <div class="action-buttons left-buttons">
                    <button id="add-9-and-len2" class="btn secondary">Add #9 and len=2</button>
                    <button id="delete-plaintiff-without-cases" class="btn secondary">Delete plaintiff without cases</button>
                </div>
                <div class="action-buttons right-buttons">
                    <span id="total-results">0 results</span>
                    <button id="refresh-data" class="btn secondary">Refresh</button>
                </div>
            </div>
        </div>

        <div class="review-container">
            <div class="loading">Loading plaintiff review data...</div>
            <table class="review-table" id="review-table" style="display: none;">
                <thead>
                    <tr>
                        <th>Case ID</th>
                        <th>Filed Date</th>
                        <th>Docket</th>
                        <th>Current Plaintiff</th>
                        <th>Plaintiff Names List</th>
                        <th>Proposed Name</th>
                        <th>Method Info</th>
                        <th>Updated</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody id="review-table-body">
                    <!-- Data will be populated here -->
                </tbody>
            </table>

            <div class="submit-container" style="margin-top: 20px; text-align: right;">
                <button id="submit-reviews" class="btn primary">Submit Reviews</button>
            </div>
        </div>
    </div>

    <!-- Overlay for delete confirmation -->
    <div id="delete-overlay" class="overlay">
        <div class="overlay-content">
            <div class="overlay-title">Plaintiffs to be deleted</div>
            <div class="overlay-list" id="plaintiffs-to-delete-list">
                <!-- List will be populated here -->
            </div>
            <div class="overlay-buttons">
                <button id="cancel-delete" class="btn secondary">Cancel</button>
                <button id="confirm-delete" class="btn primary">Confirm Delete</button>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='review_plaintiff.js') }}"></script>
</body>
</html>