Scalable Vector Search System for IP Infringement Detection
1. Overall System Goal
To implement a scalable vector search solution utilizing Qdrant, optimized for SSD storage, to detect potential Intellectual Property (IP) infringements. The system will facilitate two primary workflows:
1.	Forward Check: Comparing user-submitted product images against a large database of IP assets (Trademarks, Copyrights, Patents).
2.	Reverse Check: Comparing newly added IP assets against the database of user-submitted product images.


Understanding the current folder structure:
All the new files go into /qdrant/ and /qdrant/api/ folders. This will run on SERVER1. The new API (fastapi) should go in /qdrant/api/
There is an existing app for Forward check (located in /check/) that runs on SERVER2 (GPU machine) and computes the embeddings and calls the new FASTAPI setup (running on SERVER1)
Reverse check is not implemented yet.


4. docker: make sure to also edit the /qdrant/docker-compose.yml to add the creating of the container for the SERVER2 FastAPI.

5. Edit Do_check_copyright.py function check_copyrights to have a parameter that allow to set it to use the new qdrant way instead. 

6. Test script
Please create a test script to test the RAG process (in the /check/ folder) using qdrant 
Use this product picture for copyright: D:\Win10User\Downloads\313dff300cbe725038eaaa7788413ed66ea9139385690b40bcf0961743c8bc9a_part_14.jpg
Use this product picture for patent
D:\Win10User\Downloads\71TBgWmo6mL._AC_SX679_.jpg
Use this product picture for trademark
D:\Win10User\Downloads\US_DIS_ILND_1_24cv8376_d324899171e432_ 0_Exhibit_1_page27_0.webp


Metadata associated with IP assets will primarily reside in a self-hosted PostgreSQL instance, linked to vector representations in Qdrant via unique UUIDs.
1.1. Server & Deployment Details
•	Server DNS: vectorstore1.maidalv.com
•	SSH User: root
•	SSH Password: gNuqeDpHdAcsax
•	OS: Ubuntu 24.04
•	Deployment: All services (Qdrant, PostgreSQL, API Layer) will run as Docker containers managed potentially via docker-compose. Cloudflared runs outside Docker, tunneling traffic for Qdrant.
•	Secrets Management: Credentials and API keys should be managed via a .env file used by docker-compose.
1.2. Core Technologies
•	Vector Database: Qdrant (via Docker)
•	Metadata Database: PostgreSQL (via Docker)
•	API Layer: Python FastAPI (via Docker)
•	Vector Embeddings: Pre-computed elsewhere using models like Jina CLIP v2 and EfficientNetB7.
________________________________________
2. Qdrant Configuration & Setup (setup_qdrant.py)
A Python script (setup_qdrant.py) must be created to programmatically configure the Qdrant instance and define the required collections. This script will use the qdrant-client library.
2.1. Qdrant Connection Details
•	API Endpoint URL: https://qdrant1.maidalv.com (Cloudflare tunnel to Docker container port 6333)
•	API Key: 2WFupSDRbBCCqz4L6o31unBJX5fVtIIJ
2.2. Qdrant Storage Configuration (Docker Volume Mounts)
•	Main Data & WAL: Host /docker/qdrant_storage mounted to Container /qdrant/storage (storage_path in Qdrant config)
•	Snapshots: Host /mnt/4tb/qdrant/snapshots mounted to Container /qdrant/snapshots (snapshots_path in Qdrant config)
•	Temporary Files: Host /mnt/4tb/qdrant/temp mounted to Container /qdrant/temp (temp_path in Qdrant config)
2.3. Collection Definition: IP_Assets
•	Purpose: Stores vector representations of all registered intellectual property assets (Trademarks, Copyrights, Patents).
•	Point ID Requirement: The id for each point MUST be the corresponding unique identifier (UUID) from the primary PostgreSQL metadata table (trademarks.id, patents_records.id, or copyrights.id) for that IP asset.
•	Vector Dimensions (Constants):
•	CLIP_DIMENSION = 1024
•	EFFNET_DIMENSION = 2560
CLIP_TEXT_DIMENSION = 1024

•	Collection Creation Parameters:
•	# Assuming 'client' is an initialized QdrantClient
•	# and 'models' is imported from qdrant_client.http import models
•	
•	client.recreate_collection( # Use recreate_collection for idempotency during setup
•	    collection_name="IP_Assets",
•	
•	    # --- Vector Parameters (Named Vectors) ---
•	    vectors_config={
•	        # Copyright vectors
•	        "copyright_clip": models.VectorParams(
•	            size=CLIP_DIMENSION, # Dimension of the Jina CLIP v2 image embedding.
•	            distance=models.Distance.COSINE, # Similarity metric. COSINE recommended for normalized embeddings. Optimized by Qdrant.
•	            on_disk=True, # Store raw vectors on disk using memory mapping. CRITICAL for SSDs & large datasets, reduces RAM usage.
•	            datatype=models.Datatype.FLOAT32 # Data type for vector elements. Default. Consider FLOAT16 later for storage savings if accuracy permits.
•	        ),
•	        "copyright_efficientnet": models.VectorParams(
•	            size=EFFNET_DIMENSION, # Dimension of the EfficientNetB7 image embedding.
•	            distance=models.Distance.COSINE, # Using COSINE for consistency.
•	            on_disk=True, # Store raw vectors on disk (SSD optimization).
•	            datatype=models.Datatype.FLOAT32 # Default precision.
•	        ),
•	        # Patent vectors
•	        "patent_clip_image": models.VectorParams(
•	            size=CLIP_DIMENSION, # Dimension of the Jina CLIP v2 image embedding for the patent image.
•	            distance=models.Distance.COSINE,
•	            on_disk=True,
•	            datatype=models.Datatype.FLOAT32
•	        ),
•	        "patent_clip_text": models.VectorParams(
•	            size=CLIP_TEXT_DIMENSION, # Dimension of the Jina CLIP v2 text embedding for the patent name/description.
•	            distance=models.Distance.COSINE,
•	            on_disk=True,
•	            datatype=models.Datatype.FLOAT32
•	        ),
•	        # Trademark vector
•	        "trademark_efficientnet": models.VectorParams(
•	            size=EFFNET_DIMENSION, # Dimension of the EfficientNetB7 image embedding for the trademark image.
•	            distance=models.Distance.COSINE,
•	            on_disk=True,
•	            datatype=models.Datatype.FLOAT32
•	        ),
•	    },
•	
•	    # --- Sparse Vector Parameters ---
•	    sparse_vectors_config=None, # No sparse vectors are required for this use case.
•	
•	    # --- HNSW Index Parameters (Global for collection) ---
•	    hnsw_config=models.HnswConfigDiff(
•	        m=32, # Max connections per node per HNSW layer. Higher = better recall, more RAM/disk, slower build. Default=16. Start with 32.
•	        ef_construct=200, # Size of the dynamic list for intermediate search results during index build. Higher = better index quality, slower build. Default=100. Start with 200.
•	        full_scan_threshold=10000, # (In KiloBytes) Segments larger than this threshold benefit from payload indexes during HNSW search planning. Default=10000. Keep default.
•	        on_disk=True # Store HNSW graph structure on disk using memory mapping. CRITICAL for SSDs & large indexes. Reduces RAM usage.
•	    ),
•	
•	    # --- Optimizer Parameters ---
•	    optimizers_config=models.OptimizersConfigDiff(
•	        deleted_threshold=0.2, # Minimum fraction of deleted points in a segment to trigger vacuum optimization. Default=0.2. Keep default.
•	        vacuum_min_vector_number=1000, # Minimum number of vectors in a segment required for vacuum optimization. Default=1000. Keep default.
•	        default_segment_number=0, # Target number of segments per shard. 0 = auto-configure based on CPU cores (recommended). Keep default.
•	        indexing_threshold_kb=20000, # (In KiloBytes) Minimum segment size to build a vector index (HNSW in this case). Default=20000. Keep default.
•	        memmap_threshold_kb=20000, # (In KiloBytes) Minimum segment size to store vectors in memory-mapped files *if* on_disk=False was used. With on_disk=True for vectors/HNSW, this is less critical but aligning with indexing_threshold_kb is safe. Explicitly set to 20000 (Default is 200000).
•	        # Note: Renamed memmap_threshold -> memmap_threshold_kb in recent qdrant-client versions. Verify correct parameter name.
•	    ),
•	
•	    # --- Write-Ahead-Log Parameters ---
•	    wal_config=models.WalConfigDiff(
•	        wal_capacity_mb=32 # Size of the Write-Ahead-Log file per shard. Default=32. Affects recovery speed and memory usage during recovery. Keep default.
•	    ),
•	
•	    # --- Quantization Parameters ---
•	    quantization_config=None, # Disable quantization initially. Scalar or Product Quantization can be explored later if RAM/storage becomes a critical constraint after other optimizations, trading some accuracy for compression.
•	
•	    # --- Payload Storage Parameters ---
•	    on_disk_payload=True # Store payload data primarily on disk (using RocksDB). Reduces RAM pressure as primary metadata is external. Indexed payload fields are still cached in RAM for fast filtering.
)
•	Payload Content: Initially, no payload fields are required within Qdrant for this collection. Metadata is linked via the Point ID (Postgres UUID).
2.4. Collection Definition: Product_Images
•	Purpose: Stores vector representations of all user-submitted product images for comparison against IP assets.
•	Point ID Requirement: The id for each point should be a unique identifier. It can be auto-generated by Qdrant during insertion or provided by the client application (e.g., using uuid.uuid4() in Python).
•	Vector Dimensions (Constants):
•	CLIP_DIMENSION = 1024
EFFNET_DIMENSION = 2560
•	Collection Creation Parameters:
•	# Assuming 'client' is an initialized QdrantClient
•	# and 'models' is imported from qdrant_client.http import models
•	
•	client.recreate_collection( # Use recreate_collection for idempotency
•	    collection_name="Product_Images",
•	
•	     # --- Vector Parameters (Named Vectors) ---
•	    vectors_config={
•	        # Vector corresponding to Jina CLIP v2 for comparison with Copyright and Patent images
•	        "image_clip": models.VectorParams(
•	            size=CLIP_DIMENSION,
•	            distance=models.Distance.COSINE,
•	            on_disk=True, # CRITICAL for SSD setup.
•	            datatype=models.Datatype.FLOAT32 # Default precision.
•	        ),
•	         # Vector corresponding to EfficientNetB7 for comparison with Copyright and Trademark images
•	        "image_efficientnet": models.VectorParams(
•	            size=EFFNET_DIMENSION,
•	            distance=models.Distance.COSINE,
•	            on_disk=True, # CRITICAL for SSD setup.
•	            datatype=models.Datatype.FLOAT32 # Default precision.
•	        ),
•	         # NO vector needed for Patent Text comparison - product image_clip is compared against patent_clip_text in IP_Assets.
•	    },
•	
•	    # --- Sparse Vector Parameters ---
•	    sparse_vectors_config=None, # No sparse vectors required.
•	
•	    # --- HNSW Index Parameters ---
•	    hnsw_config=models.HnswConfigDiff(
•	        m=32, # Max connections. Default=16. Start with 32.
•	        ef_construct=200, # Build-time search scope. Default=100. Start with 200.
•	        full_scan_threshold=10000, # KB threshold for HNSW planning optimization. Default=10000. Keep default.
•	        on_disk=True # Store HNSW index on disk. CRITICAL for SSD setup.
•	    ),
•	
•	    # --- Optimizer Parameters ---
•	    optimizers_config=models.OptimizersConfigDiff(
•	        deleted_threshold=0.2, # Default=0.2. Keep default.
•	        vacuum_min_vector_number=1000, # Default=1000. Keep default.
•	        default_segment_number=0, # 0 = auto based on CPU cores. Keep default.
•	        indexing_threshold_kb=20000, # Default=20000. Keep default.
•	        memmap_threshold_kb=20000, # Align with indexing_threshold_kb. Set explicitly to 20000 (Default is 200000). Verify param name.
•	    ),
•	
•	    # --- Write-Ahead-Log Parameters ---
•	    wal_config=models.WalConfigDiff(
•	        wal_capacity_mb=32 # Default=32. Keep default.
•	    ),
•	
•	     # --- Quantization Parameters ---
•	    quantization_config=None, # Disable quantization initially.
•	
•	    # --- Payload Storage Parameters ---
•	    on_disk_payload=True # Store payload on disk (RocksDB). Recommended.
)
•	Payload Content: Each point MUST contain the following payload fields:
o	client_id: (String) Identifier for the e-commerce seller/client associated with the product image.
o	check_id: (String) Identifier for the specific check or submission batch associated with this image.
________________________________________
3. Payload Index Definition (within setup_qdrant.py)
The setup_qdrant.py script must also define necessary payload indexes after the collections are created.
3.1. Index for Product_Images Collection
•	Field: client_id
•	Purpose: Enable efficient filtering of product images by client_id. This is essential for Reverse Checks (targeting specific clients if needed) and potentially useful for client-specific Forward Checks or data management tasks.
•	Configuration:
•	# Assuming 'client' is an initialized QdrantClient
•	# and 'models' is imported from qdrant_client.http import models
•	
•	client.create_payload_index(
•	    collection_name="Product_Images",
•	    field_name="client_id",
•	    # Specify the schema type for indexing. KEYWORD is suitable for string IDs.
•	    field_schema=models.PayloadSchemaType.KEYWORD,
•	    # Using the more specific IndexParams structure can offer more control if needed later,
•	    # but for basic keyword indexing, the above is sufficient. Example using IndexParams:
•	    # field_schema=models.KeywordIndexParams(
•	    #      type=models.PayloadSchemaType.KEYWORD,
•	    #      # on_disk=False # Default. Keep index in RAM initially for faster filtering.
•	    #                    # Can be set to True later via update_collection if RAM pressure is high
•	    #                    # AND filtering speed impact is acceptable.
•	    # ),
•	    wait=True # Ensure the index creation task is acknowledged by Qdrant before the script proceeds.
)
3.2. Index for IP_Assets Collection
•	Indexes: No payload indexes are required initially for the IP_Assets collection, as filtering is not a primary use case and metadata resides externally.
________________________________________
4. API Layer Functional Requirements (FastAPI Service) running on SERVER1, files should be in /qdrant/api
A dedicated API layer, implemented using Python FastAPI and running in a Docker container, will mediate all interactions between the core application logic (on SERVER2 in /check/ folder), Qdrant, and PostgreSQL.
4.1. General API Requirements
•	Framework: FastAPI
•	Dockerization: Provide a Dockerfile for the API service. Include necessary entries in the docker-compose.yml file to build and run the container.
•	Configuration:
o	Must read Qdrant connection details (URL, API Key) from environment variables (sourced from .env).
o	Must read PostgreSQL connection details (Host, Port, DB Name, User, Password) from environment variables (sourced from .env).
•	Authentication: Implement API security using a static Bearer Token.
o	Token Value: 2WFupSDRbBCCqz4L6o31unBJX5fVtIIJ (This is the same as the Qdrant API Key as requested).
o	Mechanism: Expect the token in the Authorization: Bearer <token> header on incoming requests and validate it.
•	Embedding Handling: The API layer DOES NOT generate embeddings. It receives pre-computed embeddings (vectors) as part of the request payloads from the calling service (which has GPU capabilities on SERVER2, e.g. for forward check for patent : /check/rag/rag_patent.py where you have query_embeddings = get_clipv2_embeddings(query_image_paths, "image") ), and similarly for copyright and trademark. /check/rag/rag_patent.py generates the enbeddings that are sent to the SERVER1 (fastAPI) that does the retrieval for Forward Check.

4.2. Forward Check Endpoint
•	Suggested Route: POST /forward_check
•	Input: JSON payload containing:
o	client_id: (String) The identifier for the client submitting the batch.
o	check_id: (String) The identifier for this specific check/batch.
o	products: (List[Object]) A list of product image data objects, where each object contains:
	id: (String, Optional) A unique identifier for the product image within this batch (client-generated, useful for correlating results).
	image_clip: (List[float]) The pre-computed Jina CLIP v2 vector (size CLIP_DIMENSION).
	image_efficientnet: (List[float]) The pre-computed EfficientNetB7 vector (size EFFNET_DIMENSION).
•	Processing:
1.	Receive the batch request payload. Validate authentication.
2.	Asynchronously (Side Effect): Initiate the upsertion of the received product image vectors and payloads into the Product_Images collection.
	Use a single batch client.upsert(...) call.
	Assign unique Point IDs (e.g., generate UUIDs server-side or use provided id if guaranteed unique across all products).
	Include client_id and check_id in the payload for each point.
3.	Concurrently with Upsert: Construct a single Qdrant query_batch_points request targeting the IP_Assets collection. This batch request will contain multiple QueryRequest objects. For each product image in the input batch, include QueryRequest objects for all relevant comparisons:
	Compare product.image_clip against IP_Assets vector copyright_clip.
	Compare product.image_efficientnet against IP_Assets vector copyright_efficientnet.
	Compare product.image_clip against IP_Assets vector patent_clip_image.
	Compare product.image_clip against IP_Assets vector patent_clip_text.
	Compare product.image_efficientnet against IP_Assets vector trademark_efficientnet.
4.	Each QueryRequest within the batch must specify:
	query_vector: The appropriate vector from the input product (image_clip or image_efficientnet).
	using: The name of the target vector in the IP_Assets collection (e.g., "copyright_clip", "patent_clip_text").
	search_params: models.SearchParams(hnsw_ef=128, exact=False) (Start with hnsw_ef=128, tune based on performance/accuracy). exact=False uses the HNSW index.
	limit: Retrieve top N candidates (e.g., limit=20). Adjust based on downstream filtering needs.
	with_payload: False (Payload is not stored in Qdrant for IP_Assets).
	with_vectors: False (Vectors are not needed in the response).
5.	Execute the query_batch_points request against Qdrant.
6.	Receive the batched results. Each result corresponds to a QueryRequest and contains a list of ScoredPoint objects (only id and score populated).
7.	Aggregate all unique candidate IP Asset Point IDs (Postgres UUIDs) across all results for the entire input batch.
8.	Query the PostgreSQL database to retrieve the full metadata for these candidate IP Asset UUIDs from the trademarks, patents, and copyrights tables.
9.	Apply the specific client-side similarity filtering logic within the FastAPI layer:
	Copyright: Load and execute the logic from the provided Python file (comparing CLIP and/or EfficientNet scores against thresholds).
	Patent: Load and execute the logic from the provided Python file (comparing CLIP image and/or text scores against thresholds).
	Trademark: Compare the EfficientNetB7 similarity score (product.image_efficientnet vs ip.trademark_efficientnet) against the threshold defined in the Copyright logic file. (Note: Update logic from ORB to use EfficientNetB7 score).
10.	Correlate the filtered IP assets back to the original input product images based on which query yielded the match.
11.	Format the final results, including the relevant PostgreSQL metadata for confirmed potential infringements.
•	Output: JSON response containing a structured list of potential infringements found for the input batch, linking input products to infringing IP assets and including IP metadata. Example structure:
•	{
•	  "results": [
•	    {
•	      "input_product_id": "client_prod_123",
•	      "potential_infringements": [
•	        { "ip_type": "Copyright", "ip_asset_id": "uuid-...", "score": 0.95, "metadata": { ... } },
•	        { "ip_type": "Trademark", "ip_asset_id": "uuid-...", "score": 0.92, "metadata": { ... } }
•	      ]
•	    },
•	    // ... more products
•	  ]
}
The SERVER2 (original app in /check/) should received the payload as per the payload definition, and adjust based on what it currently needs, e.g. convert the plaintiff_ids to plaintiff name using plaintiff_df. 

4.3. Reverse Check Endpoint
Note that Server2 does not have an app doing the reverse_check. So for this, there is nothing to integrate with.
•	Suggested Route: POST /reverse_check
•	Input: JSON payload containing:
o	ip_assets: (List[Object]) A list of new IP asset data objects, where each object contains:
	ip_type: (String) "Trademark", "Patent", or "Copyright".
	id: (String) The unique UUID generated for this asset (which will be used in Postgres and Qdrant).
	metadata: (Object) The full metadata structure for the asset, corresponding to the respective PostgreSQL table schema.
	vectors: (Object) An object containing the relevant pre-computed vectors (e.g., {"copyright_clip": [...], "copyright_efficientnet": [...]} or {"patent_clip_image": [...], "patent_clip_text": [...]} etc.).
•	Processing:
1.	Receive the batch request payload. Validate authentication.
2.	Asynchronously (Side Effect, Attempt Atomic): Initiate the upsertion of the IP asset data:
	(a) Upsert the metadata for each asset into the appropriate PostgreSQL table (trademarks, patents, or copyrights) using the provided id (UUID). Perform this as a single transaction if possible for the batch.
	(b) Upsert the vector embeddings for each asset into the IP_Assets collection using a single batch client.upsert(...) call. Use the provided id (UUID) as the Point ID. Ensure the correct named vectors are included based on ip_type.
3.	Concurrently with Upsert: Construct a single Qdrant query_batch_points request targeting the Product_Images collection. For each incoming IP asset, create appropriate QueryRequest objects:
	If ip_type is Copyright: Query using ip.vectors.copyright_clip against Product_Images vector image_clip. Query using ip.vectors.copyright_efficientnet against Product_Images vector image_efficientnet.
	If ip_type is Patent: Query using ip.vectors.patent_clip_image against image_clip. Query using ip.vectors.patent_clip_text against image_clip.
	If ip_type is Trademark: Query using ip.vectors.trademark_efficientnet against image_efficientnet.
4.	Each QueryRequest within the batch must specify:
	query_vector: The appropriate vector from the input IP asset.
	using: The name of the target vector in the Product_Images collection (e.g., "image_clip", "image_efficientnet").
	search_params: models.SearchParams(hnsw_ef=128, exact=False) (Start with hnsw_ef=128, tune).
	limit: Retrieve top N candidates (e.g., limit=20).
	query_filter: (Optional) models.Filter(...) condition if the check needs to be scoped to specific client_ids. If not specified, searches across all clients.
	with_payload: models.PayloadSelectorInclude(include=["client_id", "check_id"]) (Retrieve necessary identifiers).
	with_vectors: False.
5.	Execute the query_batch_points request against Qdrant.
6.	Receive the batched results. Each result contains a list of ScoredPoint objects (with id, score, and selected payload).
7.	Apply the relevant similarity filtering logic (similar structure to Forward Check, adapted for IP type vs. Product image comparisons, using the same thresholds).
8.	Aggregate and format the results, identifying potentially infringing product images (identified by their Point ID, client_id, check_id) for each input IP asset.
•	Output: JSON response containing a structured list of potential infringements found, linking input IP assets to infringing product images. Example structure:
•	{
•	  "results": [
•	    {
•	      "input_ip_asset_id": "uuid-...",
•	      "potential_infringements": [
•	        { "product_point_id": "qdrant-point-id...", "client_id": "client-A", "check_id": "check-1", "score": 0.96 },
•	        { "product_point_id": "qdrant-point-id...", "client_id": "client-B", "check_id": "check-5", "score": 0.91 }
•	      ]
•	    },
•	    // ... more IP assets
•	  ]
}
4.4. Deletion Endpoint
•	Suggested Route: POST /delete_points (or DELETE /points)
•	Input: JSON payload containing:
o	collection_name: (String) The name of the target collection ("IP_Assets" or "Product_Images").
o	point_ids: (List[String]) A list of Point IDs (UUIDs for IP_Assets, generated IDs for Product_Images) to delete.
•	Action:
1.	Validate authentication and input payload.
2.	Execute the deletion request using the Qdrant client:
3.	client.delete(
4.	    collection_name=payload["collection_name"],
5.	    points_selector=models.PointIdsList(points=payload["point_ids"])
)
•	Output: JSON response confirming the submission of the delete request. Example:
{ "status": "submitted", "operation_id": 123, "result": True } # Or similar based on client response
________________________________________
5. PostgreSQL Schema Definition (setup_postgres.py)
A Python script (setup_postgres.py, using e.g., psycopg2 or SQLAlchemy) must be created to initialize the required database schema within the self-hosted PostgreSQL instance.
5.1. PostgreSQL Connection Details
•	Database Host: vectorstore1.maidalv.com (or the Docker service name, e.g., postgres)
•	Database Port: 5432
•	Database Name: maidalv_db
•	Database User: maidalv
•	Database Password: QFfzPivrwiDqkDyW (Retrieve from environment/.env)
•	Docker Volume: Host /docker/postgresql mounted to Container /var/lib/postgresql/data
5.2. General Schema Requirements
•	UUID Extension: The script must ensure the uuid-ossp extension is enabled:
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
•	Primary Key Strategy: All metadata tables (trademarks, patents, copyrights) MUST use a UUID as their primary key, named id, with a default value generated by uuid_generate_v4(). This id links to the Qdrant Point ID for the IP_Assets collection.
•	Data Type Mapping: Use appropriate PostgreSQL types as specified (e.g., TEXT, DATE, TIMESTAMPTZ, BOOLEAN, SMALLINT, BIGINT, TEXT[], JSONB).
•	Timestamps:
o	create_time: TIMESTAMPTZ NOT NULL DEFAULT now()
o	update_time: TIMESTAMPTZ NOT NULL DEFAULT now()
•	Automatic update_time Trigger:
1.	Define a trigger function:
2.	CREATE OR REPLACE FUNCTION trigger_set_timestamp()
3.	RETURNS TRIGGER AS $$
4.	BEGIN
5.	  NEW.update_time = NOW();
6.	  RETURN NEW;
7.	END;
$$ LANGUAGE plpgsql;
8.	Apply this trigger to each table (trademarks, patents_records, copyrights):
9.	-- Example for 'trademarks' table (repeat for others)
10.	CREATE TRIGGER set_timestamp
11.	BEFORE UPDATE ON trademarks
12.	FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();
•	Indexing: Create indexes on specified columns for efficient lookups.
5.3. Table: trademarks
•	Purpose: Store metadata for trademark assets.
•	Schema:
•	CREATE TABLE IF NOT EXISTS trademarks (
•	    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
•	    reg_no TEXT,
•	    ser_no TEXT,
•	    TRO TEXT,
•	    applicant_name TEXT,
•	    mark_text TEXT,
•	    int_cls BIGINT[], -- Array of BigIntegers
•	    filing_date DATE, -- Renamed from 'date'
•	    plaintiff_ids BIGINT[], -- Array of BigIntegers (Confirm if TEXT[] is better if not numeric IDs)
•	    nb_suits BIGINT,
•	    country_codes TEXT[], -- Array of TEXT
•	    associated_marks TEXT[], -- Array of TEXT
•	    info_source TEXT,
•	    image_source TEXT,
•	    certificate_source TEXT,
•	    mark_current_status_code INTEGER,
•	    mark_feature_code INTEGER,
•	    mark_standard_character_indicator BOOLEAN,
•	    mark_disclaimer_text TEXT[], -- Array of TEXT
•	    mark_image_colour_claimed_text TEXT,
•	    mark_image_colour_part_claimed_text TEXT,
•	    national_design_code TEXT[], -- Array of TEXT
•	    goods_services JSONB, -- Store list of structured objects as JSONB
•	    mark_current_status_external_description_text TEXT,
•	    create_time TIMESTAMPTZ NOT NULL DEFAULT now(),
•	    update_time TIMESTAMPTZ NOT NULL DEFAULT now()
•	);
•	
•	-- Indexes
•	CREATE INDEX IF NOT EXISTS idx_trademarks_reg_no ON trademarks (reg_no);
•	CREATE INDEX IF NOT EXISTS idx_trademarks_ser_no ON trademarks (ser_no);
•	
•	-- Trigger (Apply after table creation)
•	DROP TRIGGER IF EXISTS set_timestamp ON trademarks; -- Ensure idempotency
•	CREATE TRIGGER set_timestamp
•	BEFORE UPDATE ON trademarks
•	FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();
5.4. Table: patents_records and Classification Tables
•	Purpose: Store core metadata for patent assets (`patents_records`) and their associated classifications (`patents_cpc_assignments`, `patents_uspc_assignments`, `patents_loc_assignments`). Also includes tables for classification definitions (`patents_cpc_definitions`, etc.).
•	Schema:
•	    -- =====================================================================
•	    -- Core Patent Table (Renamed from patents)
•	    -- =====================================================================
•	    CREATE TABLE IF NOT EXISTS patents_records (
•	        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(), -- Auto-generated UUID primary key
•	        reg_no TEXT,
•	        document_id TEXT UNIQUE,                        -- Document ID, used for FKs
•	        TRO TEXT,
•	        inventors TEXT,
•	        assignee TEXT,
•	        applicant TEXT,
•	        patent_title TEXT,
•	        date_published DATE,
•	        plaintiff_ids BIGINT[],                         -- Array of BigIntegers
•	        patent_type TEXT,                               -- e.g., 'utility' or 'design'
•	        abstract TEXT,
•	        associated_patents TEXT[],                      -- Array of TEXT
•	        design_page_numbers INTEGER[],                  -- Array of Integers
•	        pdf_source TEXT,
•	        image_source TEXT,
•	        certificate_source TEXT,
•	        create_time TIMESTAMPTZ NOT NULL DEFAULT now(), -- Timestamp of record creation
•	        update_time TIMESTAMPTZ NOT NULL DEFAULT now()  -- Timestamp of last record update
•	    );
•
•	    COMMENT ON COLUMN patents_records.document_id IS 'Document ID, used for foreign key references from assignment tables. Should be unique.';
•	    COMMENT ON COLUMN patents_records.plaintiff_ids IS 'Array storing BigInt IDs.';
•	    COMMENT ON COLUMN patents_records.associated_patents IS 'Array storing associated patent identifiers as text.';
•	    COMMENT ON COLUMN patents_records.design_page_numbers IS 'Array storing relevant page numbers for designs.';
•
•	    -- =====================================================================
•	    -- Classification Assignment Tables
•	    -- =====================================================================
•
•	    -- Table for CPC Assignments
•	    CREATE TABLE IF NOT EXISTS patents_cpc_assignments (
•	        cpc_assignment_id SERIAL PRIMARY KEY,             -- Auto-incrementing primary key
•	        document_id VARCHAR NOT NULL,                     -- Foreign key referencing patents_records table (document_id column)
•	        cpc_version_indicator VARCHAR,                    -- From <cpc-version-indicator>/<date>, e.g., 20130101
•	        section VARCHAR,                                  -- From <section>
•	        class VARCHAR,                                    -- From <class>
•	        subclass VARCHAR,                                 -- From <subclass>
•	        main_group VARCHAR,                               -- From <main-group>
•	        subgroup VARCHAR,                                 -- From <subgroup>
•	        created_at TIMESTAMPTZ NOT NULL DEFAULT now(),    -- Timestamp of assignment creation
•
•	        CONSTRAINT fk_patents_cpc
•	            FOREIGN KEY(document_id)
•	            REFERENCES patents_records(document_id)
•	            ON DELETE CASCADE,                             -- If patent is deleted, delete associated CPC assignments
•
•	        -- Prevent exact duplicate CPC assignments for the same patent based on all extracted fields
•	        CONSTRAINT unique_patents_cpc_assignment UNIQUE (document_id, cpc_version_indicator, section, class, subclass, main_group, subgroup)
•	    );
•
•	    COMMENT ON TABLE patents_cpc_assignments IS 'Stores CPC classification assignments for patents.';
•	    COMMENT ON COLUMN patents_cpc_assignments.document_id IS 'References the unique document_id in the patents_records table.';
•
•	    -- Table for USPC Assignments
•	    CREATE TABLE IF NOT EXISTS patents_uspc_assignments (
•	        uspc_assignment_id SERIAL PRIMARY KEY,             -- Auto-incrementing primary key
•	        document_id VARCHAR NOT NULL,                     -- Foreign key referencing patents_records table (document_id column)
•	        uspc_code VARCHAR,                                -- e.g., D13/103, 707/722
•	        created_at TIMESTAMPTZ NOT NULL DEFAULT now(),    -- Timestamp of assignment creation
•
•	        CONSTRAINT fk_patents_uspc
•	            FOREIGN KEY(document_id)
•	            REFERENCES patents_records(document_id)
•	            ON DELETE CASCADE,                             -- If patent is deleted, delete associated USPC assignments
•
•	        -- Prevent duplicate USPC assignments for the same patent
•	        CONSTRAINT unique_patents_uspc_assignment UNIQUE (document_id, uspc_code)
•	    );
•
•	    COMMENT ON TABLE patents_uspc_assignments IS 'Stores USPC classification assignments for patents.';
•	    COMMENT ON COLUMN patents_uspc_assignments.document_id IS 'References the unique document_id in the patents_records table.';
•
•	    -- Table for Locarno Assignments
•	    CREATE TABLE IF NOT EXISTS patents_loc_assignments (
•	        loc_assignment_id SERIAL PRIMARY KEY,              -- Auto-incrementing primary key
•	        document_id VARCHAR NOT NULL,                     -- Foreign key referencing patents_records table (document_id column)
•	        loc_code VARCHAR,                                 -- e.g., 13-02
•	        loc_edition VARCHAR,                              -- e.g., 14
•	        created_at TIMESTAMPTZ NOT NULL DEFAULT now(),    -- Timestamp of assignment creation
•
•	        CONSTRAINT fk_patents_loc
•	            FOREIGN KEY(document_id)
•	            REFERENCES patents_records(document_id)
•	            ON DELETE CASCADE,                             -- If patent is deleted, delete associated Locarno assignments
•
•	        -- Prevent duplicate Locarno assignments for the same patent
•	        CONSTRAINT unique_patents_loc_assignment UNIQUE (document_id, loc_code)
•	    );
•
•	    COMMENT ON TABLE patents_loc_assignments IS 'Stores Locarno classification assignments for patents.';
•	    COMMENT ON COLUMN patents_loc_assignments.document_id IS 'References the unique document_id in the patents_records table.';
•
•	    -- =====================================================================
•	    -- Classification Definition Tables (Structure Only)
•	    -- =====================================================================
•
•	    -- Table for CPC Definitions
•	    CREATE TABLE IF NOT EXISTS patents_cpc_definitions (
•	        version TEXT,                             -- The version of the CPC definition schema (e.g., date string)
•	        section TEXT,                             -- CPC classification section (e.g., A, B, C)
•	        class TEXT,                               -- CPC classification class
•	        subclass TEXT,                            -- CPC classification subclass
•	        main_group TEXT,                          -- CPC classification main group
•	        sub_group TEXT,                           -- CPC classification subgroup
•	        definition TEXT,                          -- The textual definition of the CPC classification entry
•	        PRIMARY KEY (version, section, class, subclass, main_group, sub_group)
•	    );
•
•	    COMMENT ON TABLE patents_cpc_definitions IS 'Stores CPC classification definitions, including their hierarchical structure and textual descriptions.';
•	    COMMENT ON COLUMN patents_cpc_definitions.version IS 'The version of the CPC definition schema (e.g., date string).';
•	    COMMENT ON COLUMN patents_cpc_definitions.section IS 'CPC classification section (e.g., A, B, C).';
•	    COMMENT ON COLUMN patents_cpc_definitions.class IS 'CPC classification class.';
•	    COMMENT ON COLUMN patents_cpc_definitions.subclass IS 'CPC classification subclass.';
•	    COMMENT ON COLUMN patents_cpc_definitions.main_group IS 'CPC classification main group.';
•	    COMMENT ON COLUMN patents_cpc_definitions.sub_group IS 'CPC classification subgroup.';
•	    COMMENT ON COLUMN patents_cpc_definitions.definition IS 'The textual definition of the CPC classification entry.';
•
•	    -- Table for USPC Definitions
•	    CREATE TABLE IF NOT EXISTS patents_uspc_definitions (
•	        uspc_code VARCHAR PRIMARY KEY                    -- The USPC code, e.g., D13/103, 707/722
•	    );
•
•	    COMMENT ON TABLE patents_uspc_definitions IS 'Stores unique USPC classification codes. Definitions to be added later.';
•	    COMMENT ON COLUMN patents_uspc_definitions.uspc_code IS 'Primary key: Unique USPC classification code.';
•
•	    -- Table for Locarno Definitions
•	    CREATE TABLE IF NOT EXISTS patents_loc_definitions (
•	        loc_edition TEXT,
•	        loc_code TEXT,
•	        class TEXT,
•	        class_defintion TEXT,
•	        subclass TEXT,
•	        subclass_defintion TEXT,
•	        PRIMARY KEY (loc_edition, loc_code)
•	    );
•
•	    COMMENT ON TABLE patents_loc_definitions IS 'Stores Locarno classification definitions, including their hierarchical structure and textual descriptions.';
•	    COMMENT ON COLUMN patents_loc_definitions.loc_edition IS 'The edition of the Locarno classification. Part of the composite primary key.';
•	    COMMENT ON COLUMN patents_loc_definitions.loc_code IS 'The Locarno classification code (e.g., 13-02). Part of the composite primary key.';
•	    COMMENT ON COLUMN patents_loc_definitions.class IS 'The Locarno class text/title.';
•	    COMMENT ON COLUMN patents_loc_definitions.class_defintion IS 'The detailed definition or scope of the Locarno class.';
•	    COMMENT ON COLUMN patents_loc_definitions.subclass IS 'The Locarno subclass text/title.';
•	    COMMENT ON COLUMN patents_loc_definitions.subclass_defintion IS 'The detailed definition or scope of the Locarno subclass.';
•
•	    -- =====================================================================
•	    -- Trigger for patents_records update_time
•	    -- =====================================================================
•	    DROP TRIGGER IF EXISTS set_timestamp ON patents_records; -- Ensure idempotency
•	    CREATE TRIGGER set_timestamp
•	    BEFORE UPDATE ON patents_records
•	    FOR EACH ROW
•	    EXECUTE FUNCTION trigger_set_timestamp();
5.5. Table: copyrights
•	Purpose: Store metadata for copyright assets.
•	Schema:
•	CREATE TABLE IF NOT EXISTS copyrights (
•	    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
•	    TRO TEXT,
•	    registration_number TEXT,
•	    registration_date DATE,
•	    type_of_work TEXT,
•	    title TEXT,
•	    date_of_creation INTEGER, -- Storing year only as INTEGER
•	    date_of_publication DATE,
•	    copyright_claimant TEXT, -- Consider TEXT[] if multiple structured claimants
•	    authorship_on_application TEXT,
•	    rights_and_permissions TEXT,
•	    description TEXT,
•	    nation_of_first_publication TEXT,
•	    names TEXT, -- Consider TEXT[] if multiple structured names
•	    create_time TIMESTAMPTZ NOT NULL DEFAULT now(),
•	    update_time TIMESTAMPTZ NOT NULL DEFAULT now()
•	);
•	
•	-- Indexes
•	CREATE INDEX IF NOT EXISTS idx_copyrights_reg_no ON copyrights (registration_number);
•	
•	-- Trigger (Apply after table creation)
•	DROP TRIGGER IF EXISTS set_timestamp ON copyrights; -- Ensure idempotency
•	CREATE TRIGGER set_timestamp
•	BEFORE UPDATE ON copyrights
•	FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();
________________________________________
6. Docker & Deployment Summary
•	/Qdrant/docker-compose.yml file should orchestrate the Qdrant, PostgreSQL, and FastAPI services. Please add the FastAPI service to the yml file
•	Appropriate volumes must be defined for data persistence (Qdrant storage/snapshots/temp, PostgreSQL data).
•	A .env (/Qdrant/.env) file stores all secrets (DB password, Qdrant API Key/API Bearer Token).
•	The FastAPI service requires a Dockerfile.
•	Network configuration within docker-compose must allow the FastAPI service to reach both the Qdrant service and the PostgreSQL service.