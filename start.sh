#!/bin/bash
### This is the start file for the Docker container

# Save environment variables for SSH sessions
echo "#!/bin/bash" > /etc/profile.d/docker_env.sh
env | grep -v "PATH" | grep -v "PWD" | grep -v "HOME" | grep -v "HOSTNAME" | sed 's/^\([^=]*\)=\(.*\)/export \1="\2"/' >> /etc/profile.d/docker_env.sh
chmod +x /etc/profile.d/docker_env.sh

# Start Redis server
redis-server --daemonize yes

# Start SSH server
# /usr/sbin/sshds
exec /usr/sbin/sshd -D

# Start the main application
# cd /app && python app.py