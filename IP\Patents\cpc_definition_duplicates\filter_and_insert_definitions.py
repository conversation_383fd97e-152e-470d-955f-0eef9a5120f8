import pandas as pd
import logging
from IP.Patents.patent_db_cpc import get_db_connection
from IP.Patents.patent_cpc_ipc_definition import upsert_patent_cpc_definitions # Assuming this function can handle a list of dicts from a dataframe

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
log = logging.getLogger(__name__)

def download_full_definitions_table() -> pd.DataFrame:
    """
    Downloads the entire patents_cpc_ipc_definitions_full table into a pandas DataFrame.
    """
    conn = None
    try:
        log.info("Connecting to the database to download patents_cpc_ipc_definitions_full.")
        conn = get_db_connection()
        query = "SELECT * FROM patents_cpc_ipc_definitions_full;"
        df = pd.read_sql(query, conn)
        log.info(f"Successfully downloaded {len(df)} records from patents_cpc_ipc_definitions_full.")
        return df
    except Exception as e:
        log.error(f"Error downloading data from patents_cpc_ipc_definitions_full: {e}")
        return pd.DataFrame()
    finally:
        if conn:
            conn.close()

def filter_definitions(df: pd.DataFrame) -> pd.DataFrame:
    """
    Filters the DataFrame to keep only the most recent CPC or IPC for a given
    (section, class, subclass, main_group, sub_group) combination,
    with CPC taking precedence over IPC.
    """
    if df.empty:
        log.warning("Input DataFrame is empty, skipping filtering.")
        return df

    log.info(f"Starting filtering process. Initial records: {len(df)}")

    # Ensure 'publish_date' is datetime for proper sorting
    df['publish_date'] = pd.to_datetime(df['publish_date'])

    # Create a numerical column for classification_type precedence (CPC=0, IPC=1)
    # This assumes 'CPC' is preferred over 'IPC'. Adjust if other types exist or preference changes.
    df['classification_precedence'] = df['classification_type'].apply(lambda x: 0 if x == 'CPC' else 1)

    # Sort by the grouping columns, then by classification_precedence (CPC first), then by publish_date (most recent first)
    df_sorted = df.sort_values(
        by=['section', 'class', 'subclass', 'main_group', 'sub_group', 'classification_precedence', 'publish_date'],
        ascending=[True, True, True, True, True, True, False]
    )

    # Drop duplicates, keeping the first occurrence (which is the most preferred due to sorting)
    # The subset defines the unique identifier for a classification entry.
    filtered_df = df_sorted.drop_duplicates(
        subset=['section', 'class', 'subclass', 'main_group', 'sub_group'],
        keep='first'
    )

    log.info(f"Filtering complete. Records after filtering: {len(filtered_df)}")
    return filtered_df

def insert_filtered_data(df: pd.DataFrame):
    """
    Inserts the filtered DataFrame into the patents_cpc_ipc_definitions table.
    This function assumes upsert_patent_cpc_definitions can handle a list of dictionaries.
    """
    if df.empty:
        log.warning("Filtered DataFrame is empty, skipping insertion.")
        return

    log.info(f"Attempting to insert {len(df)} filtered records into patents_cpc_ipc_definitions.")
    
    # Convert DataFrame to a list of dictionaries, which upsert_patent_cpc_definitions expects
    records_to_insert = df.to_dict(orient='records')
    
    try:
        upserted_count = upsert_patent_cpc_definitions(records_to_insert)
        log.info(f"Insertion completed. {upserted_count} records were processed for upsertion.")
    except Exception as e:
        log.error(f"Error inserting filtered data into patents_cpc_ipc_definitions: {e}")

def main():
    """
    Main function to orchestrate the download, filtering, and insertion process.
    """
    log.info("Starting the CPC/IPC definition filtering and insertion script.")

    # 1. Download the entire table patents_cpc_ipc_definitions_full into a dataframe
    full_df = download_full_definitions_table()
    if full_df.empty:
        log.error("Failed to download data or no data available. Exiting.")
        return

    # 2. Only keeps the most recent CPC or IPC for a given (section, class, subclass, main_group, sub_group) combination (CPC takes precedence over IPC)
    filtered_df = filter_definitions(full_df)
    
    # 3. Insert this filtered datafram into patents_cpc_ipc_definitions
    insert_filtered_data(filtered_df)

    log.info("Script finished.")

if __name__ == "__main__":
    main()