# This script contain functions that will make USPTO API call and extract the trademark image from the website
import os
import re
import time
import asyncio

import cv2
import numpy as np
import requests
from bs4 import BeautifulSoup
import aiohttp
import random

# 60 requests per API Key per minute. Max 2 key per account.

REG_LENGTH = 7
SER_LENGTH = 8
USPTO_API_KEYS = [os.getenv("USPTO_API_KEY"), os.getenv("USPTO_API_KEY_2")]


def making_API_url(is_reg: bool, reg_no: str) -> str:
    """Creating API request URL of a tradeMark using its registration number

    Args:
        reg_no (str): the registration number of the tradeMark, must be 7 characters long

    Return:
        an API request URL for the tradeMark
    """
    if len(reg_no) != 7 and len(reg_no) != 8:
        raise ValueError(
            "The registration number must be 7 characters long. Please add padding 0s in the front."
        )

    if is_reg:
        key = "rn"
    else:
        key = "sn"

    return f"https://tsdrapi.uspto.gov/ts/cd/casestatus/{key}{reg_no}/info"


def making_img_filename(img_address: str) -> str:
    """Creates an image filename URL to access the image in the USPTO database.

    Args:
        img_address (str): Part of the image address.

    Returns:
        The full address of the image.
    """
    return f"https://tsdr.uspto.gov/img/{img_address}/large"


async def USPTO_get_trademark_info_without_session(is_reg: bool, id_no: str):
    async with aiohttp.ClientSession() as session:
        return await USPTO_get_trademark_info(session, is_reg, id_no, {})

async def USPTO_get_trademark_info(session: aiohttp.ClientSession, is_reg: bool, id_no: str, metadata: dict):
    """Takes a registration/serial number and uses the API to extract trademark info from USPTO.

    Args:
        session: The aiohttp.ClientSession to use for the request.
        is_reg: Boolean indicating if registration number (True) or serial number (False)
        id_no: Registration/serial number string
        metadata: Dictionary containing processing context

    Returns:
        Tuple: (result_data, metadata, error) where:
            - result_data: (image_cv2, text, class_list) on success
            - metadata: Original metadata dictionary
            - error: String error message or None
    """
    try:
        current_api_index = random.randint(0, len(USPTO_API_KEYS) - 1)


        pure_id_no = re.sub("[^0-9]", "", id_no)
        if is_reg:
            pure_id_no = pure_id_no.zfill(REG_LENGTH)
        else:
            pure_id_no = pure_id_no.zfill(SER_LENGTH)

        # Async request to USPTO API

        retry_attempts = 0
        max_retry = len(USPTO_API_KEYS) * 3
        start_time = time.time()
        while True:
            try:
                request_headers = {"USPTO-API-KEY": USPTO_API_KEYS[current_api_index]}
                async with session.get(making_API_url(is_reg, pure_id_no), headers=request_headers, ssl=True) as response:
                    if response.status != 200:
                        response.raise_for_status()  # Raise HTTPError for bad responses (4xx or 5xx)
                    content = await response.read()
                    print(f"✅ USPTO API KEY {current_api_index}: URL request successful after {retry_attempts+1} / {max_retry} attempts")
                    break  # Exit loop if successful
            except (aiohttp.ClientError, aiohttp.http_exceptions.HttpProcessingError) as e:
                if retry_attempts >= max_retry:
                        raise RuntimeError(f"⚠️⚠️⚠️ USPTO API URL request failed {retry_attempts} attempts. GAME OVER")
                
                if "429" in str(e) or "rate limit" in str(e).lower():
                    print(f"\033[91mRate limit exceeded on API KEY {current_api_index}, trying with API KEY {current_api_index + 1}\033[0m")
                    if retry_attempts == len(USPTO_API_KEYS):
                        print(f"\033[91m ⚠️ USPTO API KEY {current_api_index}: Rate limit exceeded of all API KEYS, waiting 60 seconds\033[0m")
                        elapsed_time = time.time() - start_time
                        await asyncio.sleep(60 - elapsed_time)
                        start_time = time.time()
                    current_api_index = (current_api_index + 1) % len(USPTO_API_KEYS)
                    retry_attempts += 1
                elif response.status == 404:
                    raise RuntimeError(f"⚠️⚠️⚠️ USPTO API returned 404 Not Found for ID {id_no}. This usually means the ID is invalid. The error is: {e}")
                else:
                    print(f"⚠️⚠️ USPTO API URL request failed. Network error => Retrying....: {e}")
                    retry_attempts += 1
                    await asyncio.sleep(3)
            
            except Exception as e:
                raise RuntimeError(f"⚠️⚠️⚠️ USPTO API URL request failed with an unexpected error: {e}")
                

        # Parse response (same as before)
        soup = BeautifulSoup(content, "xml")
        img_location = soup.find("ns2:MarkImageBag").find("ns1:FileName").text
        pure_img_location = re.sub("[^0-9]", "", img_location)
        associate_text = (
            soup.find("ns2:MarkReproduction").find("ns2:MarkVerbalElementText").text
        )

        # find all related classification number
        classification_obj = [
            cl_set.find("ns2:ClassNumber")
            for cl_set in soup.find_all("ns2:GoodsServicesClassification")
        ]
        int_cls = set()
        for cl_set in classification_obj:
            if cl_set:
                int_cls.add(int(cl_set.text))

        # Async request for image download
        img_url = making_img_filename(pure_img_location)
        request_headers = {
            "User-Agent": (
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
                "AppleWebKit/537.36 (KHTML, like Gecko) "
                "Chrome/91.0.4472.124 Safari/537.36"
            )
        }
        max_retries = 3
        for attempt in range(max_retries):
            try:
                async with session.get(img_url, headers=request_headers) as response:
                    if response.status != 200:
                        response.raise_for_status()
                    image_data = await response.read()
                    break # Exit loop if successful
            except (aiohttp.ClientError, aiohttp.http_exceptions.HttpProcessingError) as e:
                print(f"⚠️⚠️⚠️ USPTO image extraction failed after {attempt+1} / {max_retries} attempts for this URL {img_url}. The error is: {e}")
                if attempt == max_retries -1:
                    raise RuntimeError(f"⚠️⚠️⚠️ USPTO image extraction is unsuccessful after multiple retries: {e}")
                await asyncio.sleep(2**attempt)  # Exponential backoff

        
        # transform the image to cv2 representation and return the result
        img_InNumpy = np.frombuffer(image_data, np.uint8)
        image_cv2 = cv2.imdecode(img_InNumpy, cv2.IMREAD_UNCHANGED)
        return (image_cv2, associate_text, list(int_cls)), metadata, None
        
    except Exception as e:
        return None, metadata, str(e)


async def test_USPTO_get_trademark_info():
    # reg_no = "1,018,836" # error image
    reg_no = "1871376"  # logo => no text
    # reg_no = "5004543"
    async with aiohttp.ClientSession() as session:
        data, metadata, error = await USPTO_get_trademark_info(session, True, reg_no, {})
    # breakpoint()
    print(f"text: {data[1]}")
    print(f"class_list: {data[2]}")

if __name__ == "__main__":
    asyncio.run(test_USPTO_get_trademark_info())

