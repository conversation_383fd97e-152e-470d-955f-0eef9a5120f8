import os
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from Scheduler.celery_app import celery
from datetime import datetime
from email_sender import send_email_report
import sqlite3
import asyncio
from IP.Patents.weekly_patent import run_weekly_patent_grants_pipeline, get_last_tuesday
from logdata import db_path
from celery.signals import worker_init
from celery.utils.log import get_task_logger

@celery.task(bind=True)
def task_test(self):
    print(f"Task ID: {self.request.id}")
    print(f"now the time is {datetime.now()}")
    print("Email report sent!")

    conn = sqlite3.connect(db_path)
    c = conn.cursor()

    # Get the most recent completed run
    c.execute('''
        SELECT id
        FROM runs
        WHERE status = 'Completed'
        ORDER BY RANDOM()
        LIMIT 1
    ''')

    result = c.fetchone()
    conn.close()

    if result:
        run_id = result[0]
        print(f"Sending email report for run ID: {run_id}")
        send_email_report(run_id)
        print("Email sent successfully!")
    else:
        print("No completed runs found in the database")

@worker_init.connect
def print_worker_info(**kwargs):
    logger = get_task_logger(__name__)
    logger.info(f"Worker pool type: {kwargs['sender'].pool.__class__.__name__}")
    logger.info(f"Worker configuration: {kwargs['sender'].app.conf}")

@celery.task(bind=True)
def task_run_weekly_patent_grants(self):
    """
    Celery task to run the weekly patent grant processing pipeline.
    This task fetches the latest weekly patent data from USPTO,
    processes it, and loads it into the database.
    """
    logger = get_task_logger(__name__)
    logger.info(f"Task ID: {self.request.id} - Starting weekly patent grants task.")

    try:
        # Get the date of the last Tuesday for processing
        target_date = get_last_tuesday()
        logger.info(f"Processing grants for the week ending: {target_date}")

        # Run the weekly patent grants pipeline
        # NOTE: Running asyncio.run() inside a synchronous Celery task.
        # This is acceptable for simple cases but can have implications
        # for resource utilization and blocking the worker if not managed carefully,
        # especially with higher worker concurrency.
        processed_files, total_records = asyncio.run(run_weekly_patent_grants_pipeline())

        logger.info(f"Task ID: {self.request.id} - Weekly patent grants task completed successfully.")
        logger.info(f"Processed {processed_files} files, committed {total_records} records.")

    except Exception as e:
        logger.error(f"Task ID: {self.request.id} - Error during weekly patent grants task: {e}", exc_info=True)
        # Depending on requirements, you might want to retry the task here
        # raise self.retry(exc=e, countdown=60, max_retries=3)
