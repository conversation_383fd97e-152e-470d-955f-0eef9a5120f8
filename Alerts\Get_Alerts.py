import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))) # Add the project root directory to the Python path
from datetime import datetime, timedelta, date
# 3 seconds
from DatabaseManagement.ImportExport import insert_and_update_df_to_GZ_id,  insert_and_update_df_to_GZ_batch, get_table_from_GZ
# 1 second
from Alerts.LexisNexis.Get_Cases import lexis_get_cases, lexis_get_a_case
from Alerts.LexisNexis.Login import get_logged_in_browser
from Alerts.LexisNexis.Search_Cases import lexis_search_cases_by_date, lexis_search_a_case_by_docket
from Alerts.LexisNexis.Get_Case_Steps import fill_missing_step_numbers
from Alerts.PicturesProcessing.ExtractPictures import find_complaints
from Alerts.PicturesProcessing.ProcessPictures import process_pictures_multiple_cases
from FileManagement.Tencent_COS import send_pictures_to_cos
from FileManagement.NAS import NASConnection
# 3 second
from Alerts.Plaintiff import add_plaintiff_id, get_plaintiff_overview
# from FileManagement.SendToNAS import connect_to_nas
from AI.Translation import get_ai_summary_translations, translate_steps
from Frontend.frontend import refresh_frontend
import pandas as pd
import re
import traceback
from logdata import log_message, update_step_status, finish_run, get_context, task_context
import time
from decimal import Decimal, ROUND_HALF_UP
from Check.Data_Cache import update_dataframe_cache
from langfuse.decorators import observe, langfuse_context
import asyncio
import zlib
import base64
import json
from DatabaseManagement.ImportExport import get_gz_connection


# The main task
def get_alerts(run_id, selected_date, loop_back_days, force_redo=False):
    try:
        # Step 1: Search Cases & Get Cases Details
        start_time = time.time()
        with task_context(run_id, 'Get Cases'):
            update_step_status(run_id, 'Get Cases', 'Running')
            log_message(f'{time.strftime("%Y-%m-%d %H:%M:%S")}: Starting Job Id {run_id}...')
            log_message(f'Get existing info from Database.')
            df_db = get_table_from_GZ("tb_case", force_refresh=True)
            # df_db['date_filed'] = pd.to_datetime(df_db['date_filed']).dt.date
            plaintiff_df = get_table_from_GZ("tb_plaintiff", force_refresh=True)

            df_details, df_steps, plaintiff_df, df_lexis_stats = search_and_get_cases(df_db, plaintiff_df, selected_date, loop_back_days, redo=force_redo)
            # df_details.to_pickle(f'df_details.pkl')
            update_step_status(run_id, 'Get Cases', 'Completed')

        if not df_details.empty:
            # Step 2: Process Pictures
            with task_context(run_id, 'Process Pictures'):
                update_step_status(run_id, 'Process Pictures', 'Running')
                df_with_picture_details = process_files(df_db, df_details, plaintiff_df, force=True)
                update_step_status(run_id, 'Process Pictures', 'Completed')

            # Step 3: Upload to Database
            with task_context(run_id, 'Upload to Database'):
                update_step_status(run_id, 'Upload to Database', 'Running')
                df_with_id, df_steps = upload_to_database(df_with_picture_details, df_steps, df_lexis_stats)
                update_step_status(run_id, 'Upload to Database', 'Completed')

            # Step 4: Upload Pictures
            with task_context(run_id, 'Upload Pictures'):
                update_step_status(run_id, 'Upload Pictures', 'Running')
                upload_files(df_with_id, plaintiff_df)
                update_step_status(run_id, 'Upload Pictures', 'Completed')


            # Step 5: AI Tasks
            with task_context(run_id, 'AI Tasks'):
                update_step_status(run_id, 'AI Tasks', 'Running')
                do_ai_tasks(df_with_id, df_steps, plaintiff_df)
                duration = time.time() - start_time
                log_message(f"Job Id {run_id} completed successfully in {duration:.1f} seconds")
                update_step_status(run_id, 'AI Tasks', 'Completed')

        finish_run(run_id, 'Completed')

    except Exception as e:
        error_msg = traceback.format_exc()
        current_run_id, current_step_name = get_context()
        log_message(f'Error: {error_msg}', level='ERROR')
        if current_run_id is not None and current_step_name is not None:
            update_step_status(current_run_id, current_step_name, 'Failed')
        else:
            # Fallback if context is not available
            log_message(f'Error occurred outside of task context: {error_msg}', level='ERROR')
        finish_run(run_id, 'Failed')




# 1. LexisNexis to search and get cases
def search_and_get_cases(df_db, plaintiff_df, date_end_str, look_back_days=20, file_type="1+free", redo=False):
    try:
        # last_date_filed = get_last_timestamp_from_US("tb_case", "date_filed")
        # date_start = last_date_filed.date() - timedelta(days=(int(look_back_days)-1))
        if isinstance(date_end_str, str):  
            date_end = datetime.strptime(date_end_str, "%Y-%m-%d").date()
        elif isinstance(date_end_str, datetime):
            date_end = date_end_str.date()
        elif isinstance(date_end_str, date):
            date_end = date_end_str
        else:
            raise ValueError("date_end_str must be a datetime, datetime.date or a string in the format YYYY-MM-DD")
        
        date_start = date_end - timedelta(days=(int(look_back_days)-1))

        log_message(f'Searching cases from {date_start} to {date_end}.')

        df = df_db[(df_db['date_filed'] >= date_start) & (df_db['date_filed'] <= date_end)]

        log_message(f'Found {len(df)} cases in Database')
        for index, row in df.iterrows():
            log_message(f'  - {row["date_filed"]} - {row["docket"]} - With status: {row["file_status"]}.')

        df_steps = pd.DataFrame(columns=['step_nb', 'step_date_filed', 'proceeding_text', 'docket', 'court', 'download_required', 'files_number', 'files_downloaded', 'files_failed'])
        df_lexis_stats = pd.DataFrame(columns=['date_filed', 'docket', 'court', 'pacer_refresh', 'pacer_file', 'total_file'])

        driver = get_logged_in_browser()
        with NASConnection() as nas:
            for case_date in pd.date_range(date_start, date_end, freq='D'):
                case_date = case_date.date()
                log_message(f'Searching cases filed on {case_date} in LexisNexis.')
                lexis_search_cases_by_date(driver, case_date)
                df, df_steps, df_lexis_stats, success = lexis_get_cases(
                    driver, case_date, df, df_steps, df_lexis_stats, 
                    nas, file_type, 1000000, redo=redo
                )
                if not success:
                    log_message(f'Error in lexis_search_and_get_cases for date {case_date}. Trying a second time...', level='ERROR')
                    lexis_search_cases_by_date(driver, case_date)
                    df, df_steps, df_lexis_stats, success = lexis_get_cases(
                        driver, case_date, df, df_steps, df_lexis_stats,
                        nas, file_type, 1000000, redo=redo
                    )


        driver.quit()

        log_message(f'Cases retrieved successfully. {df_lexis_stats["pacer_refresh"].sum()} pacer refreshes & {df_lexis_stats["pacer_file"].sum()} pacer files downloaded.')
        log_message('Adding plaintiff IDs to cases...')
        add_plaintiff_id(df, plaintiff_df)
        # df["date_filed"] = pd.to_datetime(df["date_filed"]).dt.date
        log_message('All Done!')
        

        return df, df_steps, plaintiff_df, df_lexis_stats


    except Exception as e:
        error_msg = traceback.format_exc()
        log_message(f'Error in search_and_get_cases: {error_msg}', level='ERROR')
        raise  # Re-raise the exception to be caught in the main try-except block




# 2. Process files downloaded from LexisNexis
def process_files(df_db, df_with_lexis_data, plaintiff_df, force=False):
    # df_with_lexis_data is the dataframe with the cases to be processed
    
    try:
        log_message(f'{time.strftime("%Y-%m-%d %H:%M:%S")}: Processing files downloaded from LexisNexis')

        # Your existing code
        log_message('Finding complaints...')
        find_complaints(df_with_lexis_data)  # In case the complaint is not in step1
        log_message('Processing pictures...')
        df_with_picture_details = process_pictures_multiple_cases(df_db, df_with_lexis_data, plaintiff_df, force=force)  # Identify what is what, crop and send to database

        log_message('Pictures processed successfully')
        return df_with_picture_details

    except Exception as e:
        error_msg = traceback.format_exc()
        log_message(f'Error in process_files: {error_msg}', level='ERROR')
        raise


# 3. Upload to database
def upload_to_database(df_with_picture_details, df_steps, df_lexis_stats):
    try:
        log_message(f'{time.strftime("%Y-%m-%d %H:%M:%S")}: Uploading Cases to GZ Mysql database')
        # df_with_picture_details["date_filed"] = pd.to_datetime(df_with_picture_details["date_filed"]).dt.date
        df_with_id = insert_and_update_df_to_GZ_id(df_with_picture_details, "tb_case", "docket", "court")  # Insert or update depending on if the case is already in the database
        
        log_message('Uploading Steps to GZ Mysql database')
        df_steps = pd.merge(df_steps, df_with_id[['id', 'docket', 'court']], on=['docket', 'court'], how='left')  # Add cases ID to df_steps
        df_steps.rename(columns={'id': 'case_id'}, inplace=True)
        df_steps = fill_missing_step_numbers(df_steps)
        df_steps.drop(columns=['docket', 'court'], inplace=True)
        # df_steps["step_date_filed"] = pd.to_datetime(df_steps["step_date_filed"]).dt.date
        df_steps_with_id = insert_and_update_df_to_GZ_id(df_steps, "tb_case_steps", "case_id", "step_nb")

        df_lexis_stats = pd.merge(df_lexis_stats, df_with_id[['id', 'docket', 'court']], on=['docket', 'court'], how='left')  # Add cases ID to df_lexis_stats
        df_lexis_stats.rename(columns={'id': 'case_id'}, inplace=True)
        df_lexis_stats.drop(columns=['date_filed', 'docket', 'court'], inplace=True)
        df_lexis_stats["event_date"] = datetime.now().date()
        df_lexis_stats_with_id = insert_and_update_df_to_GZ_id(df_lexis_stats, "lexis", "event_date", "case_id")

        log_message('Database uploaded successfully')
        return df_with_id, df_steps

    except Exception as e:
        error_msg = traceback.format_exc()
        log_message(f'Error in upload_to_database: {error_msg}', level='ERROR')
        raise


# 4. Upload to GZ COS and NAS
def upload_files(df_with_id, plaintiff_df):
    try:
        log_message(f'{time.strftime("%Y-%m-%d %H:%M:%S")}: Uploading pictures to GZ COS...')
        send_pictures_to_cos(df_with_id)
        log_message(f'{time.strftime("%Y-%m-%d %H:%M:%S")}: Pictures uploaded successfully to GZ COS')
        
        log_message('\n')
        log_message(f'{time.strftime("%Y-%m-%d %H:%M:%S")}: Uploading files to Synology NAS...')
        
        with NASConnection() as nas:
            nas.send_files_to_nas(df_with_id, plaintiff_df)
            
        log_message(f'{time.strftime("%Y-%m-%d %H:%M:%S")}: Files uploaded successfully to Synology NAS in NY')

    except Exception as e:
        error_msg = traceback.format_exc()
        log_message(f'Error in upload_pictures: {error_msg}', level='ERROR')
        raise


# 5. Do AI tasks & Trigger frontend refresh
# Why not async? Most of the transaltion use Gemini Pro, which is expensive, so best to use the experimental free model, limited at 10 queries per minute
@observe(capture_input=False, capture_output=False)
def do_ai_tasks(df_with_id, df_steps, plaintiff_df):
    # Langfuse setup
    first_date = df_with_id["date_filed"].min()
    last_date = df_with_id["date_filed"].max()
    if first_date == last_date:
        session_id = f"{first_date}"
    else:
        session_id = f"{first_date}-{last_date}"
    langfuse_context.update_current_observation(
        name="Daily Alert",
        session_id=session_id,
    )
    
    
    log_message(f'{time.strftime("%Y-%m-%d %H:%M:%S")}: Doing AI tasks')
    log_message(f'{time.strftime("%Y-%m-%d %H:%M:%S")}: Getting plaintiff overview...')
    get_plaintiff_overview(df_with_id, plaintiff_df[plaintiff_df["id"].isin(df_with_id["plaintiff_id"])])
    log_message(f'{time.strftime("%Y-%m-%d %H:%M:%S")}: Getting AI summary translations...')
    df_with_id = get_ai_summary_translations(df_with_id)
    log_message(f'{time.strftime("%Y-%m-%d %H:%M:%S")}: Translating steps...')
    df_steps = asyncio.run(translate_steps(df_steps))
    log_message(f'{time.strftime("%Y-%m-%d %H:%M:%S")}: AI summary translations now going into the database')
    insert_and_update_df_to_GZ_batch(df_with_id, "tb_case", "id")
    insert_and_update_df_to_GZ_batch(df_steps, "tb_case_steps", "id")
    log_message(f'{time.strftime("%Y-%m-%d %H:%M:%S")}: AI tasks done, refreshing chached dataframe and frontend')
    update_dataframe_cache() # Update the cached DataFrames for TRO Check
    refresh_frontend()
    log_message(f'{time.strftime("%Y-%m-%d %H:%M:%S")}: All done!')




# A1. Local run for testing: get cases from LexisNexis and put in database, one day at a time
def local_run_get_cases_and_upload_to_database(df_cases, df_plaintiffs, date_start, date_end, file_type="1+free+evidence", redo=False):
    with NASConnection() as nas:
        driver = get_logged_in_browser()
        # date_start = datetime.strptime(date_start, "%Y-%m-%d").date()
        # date_end = datetime.strptime(date_end, "%Y-%m-%d").date()

        df_cases.drop(columns=['id'], inplace=True)
        # df_cases['date_filed'] = pd.to_datetime(df_cases['date_filed']).dt.date
        df_cases = df_cases[df_cases['date_filed'] >= date_start.date()]
        df_cases = df_cases[df_cases['date_filed'] <= date_end.date()]

        df_steps = pd.DataFrame(columns=['step_nb', 'step_date_filed', 'proceeding_text', 'docket', 'court', 'download_required', 'files_number', 'files_downloaded', 'files_failed'])
        df_lexis_stats = pd.DataFrame(columns=['date_filed', 'docket', 'court', 'pacer_refresh', 'pacer_file', 'total_file'])

        for case_date in pd.date_range(date_start, date_end, freq='D'):
            # if case_date.weekday() >= 5: # Skip Saturday and Sunday  (monday == 0)
            #     continue
            case_date = case_date.date()
            lexis_search_cases_by_date(driver, case_date)
            df_db, df_steps, df_lexis_stats, success = lexis_get_cases(
                driver, case_date, df_db, df_steps, df_lexis_stats,
                nas, file_type, 1000000, redo=redo
            )
            if not success:
                log_message(f'Error in lexis_search_and_get_cases for date {case_date}. Trying a second time...', level='ERROR')
                lexis_search_cases_by_date(driver, case_date)
                df_cases, df_steps, df_lexis_stats, success = lexis_get_cases(driver, case_date, df_cases, df_steps, df_lexis_stats,nas, file_type, 1000000, redo=redo)
            
            print("------------------------------------------------------------------------\n")

        driver.quit()

        print("===================================================================\n")

        if not df_cases.empty:
            add_plaintiff_id(df_cases, df_plaintiffs)

            print("Cases updated with plaintiff IDs being uploaded to database....")
            # df_cases["date_filed"] = pd.to_datetime(df_cases["date_filed"]).dt.date
            df_with_id = insert_and_update_df_to_GZ_id(df_cases, "tb_case", "docket", "court")  # Insert or update depending on if the case is already in the database
            print("Cases uploaded to database")
            
            df_steps = pd.merge(df_steps, df_with_id[['id', 'docket', 'court']], on=['docket', 'court'], how='left')  # Add cases ID to df_steps
            df_steps.rename(columns={'id': 'case_id'}, inplace=True)
            df_steps = fill_missing_step_numbers(df_steps)
            df_steps.drop(columns=['docket', 'court'], inplace=True)
            # df_steps["step_date_filed"] = pd.to_datetime(df_steps["step_date_filed"]).dt.date
            print("Steps filled with step numbers, uploading to database....")
            df_steps_with_id = insert_and_update_df_to_GZ_id(df_steps, "tb_case_steps", "case_id", "step_nb")
            print("Steps uploaded to database")
            
            df_lexis_stats = pd.merge(df_lexis_stats, df_with_id[['id', 'docket', 'court']], on=['docket', 'court'], how='left')  # Add cases ID to df_lexis_stats
            df_lexis_stats.rename(columns={'id': 'case_id'}, inplace=True)
            df_lexis_stats.drop(columns=['date_filed', 'docket', 'court'], inplace=True)
            df_lexis_stats["event_date"] = datetime.now().date()
            df_lexis_stats_with_id = insert_and_update_df_to_GZ_id(df_lexis_stats, "lexis", "event_date", "case_id")
            print("Lexis stats uploaded to database")

            return df_with_id, df_steps_with_id


# A2. Local run for testing: get cases from LexisNexis and put in database, all days at once
def local_run_get_cases_and_upload_to_database_all_at_once(df_cases, df_plaintiffs, date_start, date_end, file_type="1+free", redo=False):
    with NASConnection() as nas:
        driver = get_logged_in_browser()
        # date_start = datetime.strptime(date_start, "%Y-%m-%d").date()
        # date_end = datetime.strptime(date_end, "%Y-%m-%d").date()
        date_start = date_start.date()
        date_end = date_end.date()

        df_cases.drop(columns=['id'], inplace=True)
        # df_cases['date_filed'] = pd.to_datetime(df_cases['date_filed']).dt.date
        df_cases = df_cases[df_cases['date_filed'] >= date_start]
        df_cases = df_cases[df_cases['date_filed'] <= date_end]

        df_steps = pd.DataFrame(columns=['step_nb', 'step_date_filed', 'proceeding_text', 'docket', 'court', 'download_required', 'files_number', 'files_downloaded', 'files_failed'])
        df_lexis_stats = pd.DataFrame(columns=['date_filed', 'docket', 'court', 'pacer_refresh', 'pacer_file', 'total_file'])

        lexis_search_cases_by_date(driver, date_start, date_end)
        df_cases, df_steps, df_lexis_stats, success = lexis_get_cases(driver, f"{date_start}-{date_end}", df_cases, df_steps, df_lexis_stats, nas, file_type, 1000000, redo=redo)
        if not success:
            log_message(f'Error in lexis_search_and_get_cases for date {date_start} - {date_end}. Trying a second time...')
            lexis_search_cases_by_date(driver, date_start, date_end)
            df_cases, df_steps, df_lexis_stats, success = lexis_get_cases(driver, f"{date_start}-{date_end}", df_cases, df_steps, df_lexis_stats, nas, file_type, 1000000, redo=redo)
        
        print("------------------------------------------------------------------------\n")

        driver.quit()

        print("===================================================================\n")

        if not df_cases.empty:
            add_plaintiff_id(df_cases, df_plaintiffs)

            print("Cases updated with plaintiff IDs being uploaded to database....")
            # df_db["date_filed"] = pd.to_datetime(df_db["date_filed"]).dt.date
            df_with_id = insert_and_update_df_to_GZ_id(df_cases, "tb_case", "docket", "court")  # Insert or update depending on if the case is already in the database
            print("Cases uploaded to database")
            
            df_steps = pd.merge(df_steps, df_with_id[['id', 'docket', 'court']], on=['docket', 'court'], how='left')  # Add cases ID to df_steps
            df_steps.rename(columns={'id': 'case_id'}, inplace=True)
            df_steps = fill_missing_step_numbers(df_steps)
            df_steps.drop(columns=['docket', 'court'], inplace=True)
            # df_steps["step_date_filed"] = pd.to_datetime(df_steps["step_date_filed"]).dt.date
            print("Steps filled with step numbers, uploading to database....")
            df_steps_with_id = insert_and_update_df_to_GZ_id(df_steps, "tb_case_steps", "case_id", "step_nb")
            print("Steps uploaded to database")
            
            df_lexis_stats = pd.merge(df_lexis_stats, df_with_id[['id', 'docket', 'court']], on=['docket', 'court'], how='left')  # Add cases ID to df_lexis_stats
            df_lexis_stats.rename(columns={'id': 'case_id'}, inplace=True)
            df_lexis_stats.drop(columns=['date_filed', 'docket', 'court'], inplace=True)
            df_lexis_stats["event_date"] = datetime.now().date()
            df_lexis_stats_with_id = insert_and_update_df_to_GZ_id(df_lexis_stats, "lexis", "event_date", "case_id")
            print("Lexis stats uploaded to database")

            return df_with_id, df_steps_with_id, df_plaintiffs


# A3. Local run for testing: get a list of cases from LexisNexis and put in database, one day at a time
def local_run_get_cases_and_upload_to_database_using_dockets(df_cases, df_plaintiffs, file_type="1+free", redo=False):
    with NASConnection() as nas:
        driver = get_logged_in_browser()

        df_steps = pd.DataFrame(columns=['step_nb', 'step_date_filed', 'proceeding_text', 'docket', 'court', 'download_required', 'files_number', 'files_downloaded', 'files_failed'])
        df_lexis_stats = pd.DataFrame(columns=['date_filed', 'docket', 'court', 'pacer_refresh', 'pacer_file', 'total_file'])

        for i, (index, row) in enumerate(df_cases.iterrows()):
            try:
                found = lexis_search_a_case_by_docket(driver, row["docket"], row["court"], row["title"])
                if found:
                    df_cases, df_steps, df_lexis_stats = lexis_get_a_case(
                        i, driver, df_cases, df_steps, df_lexis_stats,
                        index, row["docket"], row["court"], row["date_filed"],
                        file_type, nas, 10000000
                    )
            except Exception as e:
                log_message(f'Error in lexis_search_a_case_by_docket or lexis_get_a_case for docket {row["docket"]}. Trying a second time... The error was: {e}', level='ERROR')
                try: 
                    found = lexis_search_a_case_by_docket(driver, row["docket"], row["court"], row["title"])
                    if found:
                        df_cases, df_steps, df_lexis_stats = lexis_get_a_case(
                            i, driver, df_cases, df_steps, df_lexis_stats,
                            index, row["docket"], row["court"], row["date_filed"],
                            file_type, nas, 10000000
                        )
                except Exception as e:
                    log_message(f'Error in lexis_search_a_case_by_docket or lexis_get_a_case for docket {row["docket"]}. Giving up... The error was: {e}', level='ERROR')
                    return df_cases, df_lexis_stats, df_plaintiffs
            
            print("------------------------------------------------------------------------\n")

        driver.quit()

        print("===================================================================\n")

        if not df_cases.empty:
            add_plaintiff_id(df_cases, df_plaintiffs)

            print("Cases updated with plaintiff IDs being uploaded to database....")
            # df_db["date_filed"] = pd.to_datetime(df_db["date_filed"]).dt.date
            df_with_id = insert_and_update_df_to_GZ_id(df_cases, "tb_case", "docket", "court")  # Insert or update depending on if the case is already in the database
            print("Cases uploaded to database")
            
            df_steps = pd.merge(df_steps, df_with_id[['id', 'docket', 'court']], on=['docket', 'court'], how='left')  # Add cases ID to df_steps
            df_steps.rename(columns={'id': 'case_id'}, inplace=True)
            df_steps = fill_missing_step_numbers(df_steps)
            df_steps.drop(columns=['docket', 'court'], inplace=True)
            # df_steps["step_date_filed"] = pd.to_datetime(df_steps["step_date_filed"]).dt.date
            print("Steps filled with step numbers, uploading to database....")
            df_steps_with_id = insert_and_update_df_to_GZ_id(df_steps, "tb_case_steps", "case_id", "step_nb")
            print("Steps uploaded to database")
            
            df_lexis_stats = pd.merge(df_lexis_stats, df_with_id[['id', 'docket', 'court']], on=['docket', 'court'], how='left')  # Add cases ID to df_lexis_stats
            df_lexis_stats.rename(columns={'id': 'case_id'}, inplace=True)
            df_lexis_stats.drop(columns=['date_filed', 'docket', 'court'], inplace=True)
            df_lexis_stats["event_date"] = datetime.now().date()
            df_lexis_stats_with_id = insert_and_update_df_to_GZ_id(df_lexis_stats, "lexis", "event_date", "case_id")
            print("Lexis stats uploaded to database")

        return df_with_id, df_steps_with_id, df_plaintiffs




# B. Local run for testing: process files and upload to COS and NAS
def local_run_process_files(df_db, plaintiff_df, date_start=None, date_end=None, docket_nb=None, df=None, force=False):
    # if os.name == "nt":
    #     pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'
    # else:
    #     pytesseract.pytesseract.tesseract_cmd = 'tesseract'

    if df is None:
        df = df_db.copy()
        if date_start is not None:
            date_start = date_start.date()
            df = df[df['date_filed'] >= date_start]
        if date_end is not None:
            date_end = date_end.date()
            df = df[df['date_filed'] <= date_end]
        if docket_nb is not None:
            df = df[df['docket'].str.contains(docket_nb)]

    df = process_pictures_multiple_cases(df_db, df, plaintiff_df, force=force)
    insert_and_update_df_to_GZ_batch(df, "tb_case", "id") # Allows to use the ID, and does not retrieve the ID
    send_pictures_to_cos(df)
    with NASConnection() as nas:
        nas.send_files_to_nas(df, plaintiff_df)
    get_plaintiff_overview(df, plaintiff_df[plaintiff_df["id"].isin(df["plaintiff_id"])])
    df = get_ai_summary_translations(df)
    
    # Deal with the steps translations
    with get_gz_connection() as gz_connection:
        case_ids = ','.join(map(str, df['id'].tolist()))
        query = f"SELECT * FROM tb_case_steps WHERE case_id IN ({case_ids})"
        df_steps = pd.read_sql_query(query, gz_connection)
    df_steps = asyncio.run(translate_steps(df_steps))

    print(f"AI summary translations now going into the database")
    insert_and_update_df_to_GZ_batch(df, "tb_case", "id")
    insert_and_update_df_to_GZ_batch(df_steps, "tb_case_steps", "id")
    refresh_frontend()
    return df



if __name__ == "__main__":    
    # refresh_frontend()

    df_cases = get_table_from_GZ("tb_case", force_refresh=True)
    df_plaintiffs = get_table_from_GZ("tb_plaintiff", force_refresh=True)


    ## 1. Fix missing plaintiffs
    # from DatabaseManagement.Fixes import missing_plaintiff_ip
    # df_missing_plaintiff_ip = missing_plaintiff_ip()

    # for index, row in df_missing_plaintiff_ip.iterrows():
    #     print(f"Row {index}: Processing {row['date_filed']}, with {row['plaintiff_count']} missing cases")
    #     date_start = row['date_filed'].strftime("%Y-%m-%d")
    #     date_end = row['date_filed'].strftime("%Y-%m-%d")
    #     df_with_id, df_steps = local_run_get_cases_and_upload_to_database_all_at_once(df_cases, df_plaintiffs, date_start=date_start, date_end=date_end, file_type="1+Free", redo=True)
    #     local_run_process_files(df_cases, df_plaintiffs, date_start=date_start, date_end=date_end, force=True)



    ## 2. Capture a specific date or date range
    # date_start = pd.to_datetime('2025-01-21')
    # date_end = pd.to_datetime('2025-01-21')


    # Go all days at once
    # df_with_id, df_steps = local_run_get_cases_and_upload_to_database_all_at_once(df_cases, df_plaintiffs, date_start=date_start, date_end=date_end, file_type="0", redo=False)
    # local_run_process_files(df_cases,  df_plaintiffs, date_start=date_start, date_end=date_end, force=True)

    # Go xx days at a time
    # for i in range(0, 1000, 30):
    #     date_start_i = date_start + pd.Timedelta(days=i)
    #     date_end_i = min(date_start_i + pd.Timedelta(days=29), date_end)
    #     df_with_id, df_steps = local_run_get_cases_and_upload_to_database_all_at_once(df_cases, df_plaintiffs, date_start=date_start_i, date_end=date_end_i, file_type="1+free", redo=True)
    #     local_run_process_files(df_cases,  df_plaintiffs, date_start=date_start_i, date_end=date_end_i, force=True)

    # Go 1 days at a time
    # for date in pd.date_range(date_start, date_end, freq='D'):
    #     df_with_id, df_steps = local_run_get_cases_and_upload_to_database_all_at_once(df_cases, df_plaintiffs, date_start=date, date_end=date, file_type="1+free", redo=True)
    #     local_run_process_files(df_cases, df_plaintiffs, date_start=date, date_end=date, force=True)
    # local_run_process_files(docket_nb='10948', force=True)




    ## 3. Get a list of dockets from existing cases
    df_db = get_table_from_GZ("tb_case", force_refresh=True)
    df_db.drop(columns=['id'], inplace=True)
    dockets = ["1:24-cv-03459"]
    df_cases = df_db[df_db['docket'].isin(dockets)]
    df_with_id, df_steps = local_run_get_cases_and_upload_to_database_using_dockets(df_cases, df_plaintiffs, file_type="1+deep", redo=True)
    # local_run_process_files(df_cases,  df_plaintiffs, date_start=date_start, date_end=date_end, force=True)


    ##3. Get a case that is not in the database
    new_df = pd.DataFrame({'date_filed': [pd.to_datetime("2025-02-03").date()],'docket': ["1:25-cv-00971"],'court': ["New York Southern District Court"],'title': ["Zuru Inc. vs NEWWANDE and XpenGeny (Private Client)"], 'file_status': [None], 'images': ['{}']})
    df_with_id, df_steps = local_run_get_cases_and_upload_to_database_using_dockets(new_df, df_plaintiffs, file_type="1", redo=True)
    local_run_process_files(df_cases,  df_plaintiffs, docket_nb="1:25-cv-00971", force=True)



    ## 4. Get new case by docket
    # df_db = get_table_from_GZ("tb_case", force_refresh=False)
    # df_db.drop(columns=['id'], inplace=True)
    # dockets = [
    #     {"docket":"1:25-cv-01480", "court":"USDC", "title":"Li", "date_filed":"2025-01-21"},
    #     {"docket":"1:25-cv-01481", "court":"USDC",  "date_filed":"2025-01-21"},
    #     {"docket":"1:25-cv-01482",  "title":"Schedule"}
    # ]

    # df_cases = pd.DataFrame()
    # for docket in dockets:
    #     df_case = df_db[df_db['docket'].str.contains(docket['docket'])]
    #     if len(df_case) > 1:
    #         if 'date_filed' in docket:
    #             df_case = df_case[df_case['date_filed'] == pd.to_datetime(docket['date_filed']).date()]
    #     if len(df_case) > 1:
    #         if 'title' in docket:
    #             df_case = df_case[df_case['title'].str.contains(docket['title'])]
    #     if len(df_case) > 1:
    #         if 'court' in docket:
    #             df_case = df_case[df_case['court'].str.contains(docket['court'])]
    
    #     if len(df_case) == 1:
    #         df_cases = pd.concat([df_cases, df_case], ignore_index=True)
    #     else:
    #         new_entry = {'docket': docket['docket']}
    #         if 'date_filed' in docket:
    #             new_entry['date_filed'] = docket['date_filed']
    #         if 'title' in docket:
    #             new_entry['title'] = docket['title']
    #         if 'court' in docket:
    #             new_entry['court'] = docket['court']
    #         df_cases = pd.concat([df_cases, pd.DataFrame([new_entry])], ignore_index=True)


    # df_with_id, df_steps = local_run_get_cases_and_upload_to_database_using_dockets(df_cases, df_plaintiffs, file_type="1", redo=True)
    # local_run_process_files(df_cases,  df_plaintiffs, date_start=date_start, date_end=date_end, force=True)