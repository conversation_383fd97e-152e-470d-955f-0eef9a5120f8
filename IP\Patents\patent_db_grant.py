 # IP/Patents/patent_db.py
"""
Patent Database Operations Module

This module provides functions to interact with the PostgreSQL database
for patent grant and CPC data.
"""

import os
import psycopg2
import psycopg2.extras
import logging
from dotenv import load_dotenv
import json # For handling complex fields like lists of inventors/applicants if needed
import re
import pandas as pd
import sys
from tqdm import tqdm
from concurrent.futures import ProcessPoolExecutor
import multiprocessing
import numpy as np

# Load environment variables
load_dotenv()

# Configure logging
log_file_path = os.path.join(os.path.dirname(__file__), '..', '..', 'logs', 'patent_db.log')
os.makedirs(os.path.dirname(log_file_path), exist_ok=True)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file_path),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


def get_db_connection():
    """
    Create a connection to the PostgreSQL database.

    Returns:
        connection: PostgreSQL database connection object or None if connection fails.
    """
    conn = None
    try:
        conn = psycopg2.connect(
            host=os.getenv("POSTGRES_HOST"),
            port=os.getenv("POSTGRES_PORT"),
            user=os.getenv("POSTGRES_USER"),
            password=os.getenv("POSTGRES_PASSWORD"),
            dbname=os.getenv("POSTGRES_DB")
        )
        # logger.info("Database connection established successfully.")
        return conn
    except psycopg2.OperationalError as e:
        logger.error(f"Database connection failed: {e}")
        # Depending on the application's needs, you might want to retry or handle this differently.
        # For now, we'll return None to indicate failure.
        return None
    except Exception as e:
        logger.error(f"An unexpected error occurred during database connection: {e}")
        # Ensure connection is closed if partially opened before the error
        if conn:
            conn.close()
        return None


def upsert_patent_grants(patent_grants, mode):
    """
    Insert or update patent grant records in the database using UPSERT logic.
    Filters out records without a 'reg_no'.

    Args:
        patent_grants (list): List of patent grant dictionaries (record_type='grant').

    Returns:
        int: Number of records successfully processed.
    """
    if not patent_grants:
        logger.info("No patent grants provided for upsert.")
        return 0

    # Filter out grants without the primary identifier and ensure correct record_type
    valid_grants = [
        grant for grant in patent_grants
        if grant.get('record_type') == 'grant' and grant.get('reg_no')
    ]
    original_count = len(patent_grants)
    valid_count = len(valid_grants)
    skipped_count = original_count - valid_count
    logger.info(f"Received {original_count} records. Processing {valid_count} valid grant records (skipped {skipped_count}).")

    if not valid_grants:
        logger.info("No valid patent grant records left to upsert after filtering.")
        return 0

    conn = None
    processed_count = 0
    cursor = None  # Initialize cursor to None
    try:
        conn = get_db_connection()
        if conn is None:
            logger.error("Failed to get database connection. Aborting upsert.")
            return 0  # Indicate failure or inability to process

        cursor = conn.cursor()

        # Define the columns for INSERT/UPDATE (excluding id, CPC fields, create_time, update_time)
        columns = [
            'id', 'document_id', 'reg_no', 'TRO', 'inventors', 'assignee', 'applicant',
            'patent_title', 'date_published', 'plaintiff_id', 'patent_type',
            'abstract', 'associated_patents', 'design_page_numbers', 'pdf_source',
            'image_source', 'certificate_source',
            'loc_code', 'loc_edition', 'uspc_class', 'uspc_subclass'
        ]
        column_names = ', '.join(columns)
        placeholders_list = ["TO_DATE(%s::TEXT, 'YYYY-MM-DD')" if col == 'date_published' else "%s" for col in columns]
        placeholders = ', '.join(placeholders_list)

        update_set_parts = [
            f"{col} = TO_DATE(EXCLUDED.{col}::TEXT, 'YYYY-MM-DD')" if col == 'date_published' else f"{col} = EXCLUDED.{col}"
            for col in columns if col != 'id' # Exclude  'id' from update
        ]
        update_set = ', '.join(update_set_parts)
        
        table_name = "patents"
        if mode == "all":
            table_name += "_all"
            

        sql = f"""
        INSERT INTO {table_name} ({column_names})
        VALUES ({placeholders})
        ON CONFLICT (id)
        DO UPDATE SET {update_set}, update_time = NOW()
        """
        logger.debug(f"Upsert SQL: {sql}")

        batch_values = []
        logger.info(f"Processing {len(valid_grants)} patent grants for database upsert...")
        for grant in tqdm(valid_grants, desc="Processing patent grants", unit="grant"):
            # Prepare data - handle potential missing keys and data types
            # Prepare data for inventors
            inventors_list = grant.get('inventors', [])
            inventors_str = "[" + ", ".join([
                f"{inv.get('first_name', '')} {inv.get('last_name', '')}".strip() +
                (f" ({inv.get('orgname', '')})" if inv.get('orgname') else "")
                for inv in inventors_list
                if inv.get('first_name') or inv.get('last_name') or inv.get('orgname')
            ]) + "]"

            # Prepare data for applicants
            applicants_list = grant.get('applicants', [])
            applicants_str = "[" + ", ".join([
                f"{app.get('first_name', '')} {app.get('last_name', '')}".strip() +
                (f" ({app.get('orgname', '')})" if app.get('orgname') else "")
                for app in applicants_list
                if app.get('first_name') or app.get('last_name') or app.get('orgname')
            ]) + "]"

            # Prepare data for assignees
            assignees_list = grant.get('assignees', [])
            assignees_str = "[" + ", ".join([
                f"{assignee.get('first_name', '')} {assignee.get('last_name', '')}".strip() +
                (f" ({assignee.get('orgname', '')})" if assignee.get('orgname') else "")
                for assignee in assignees_list
                if assignee.get('first_name') or assignee.get('last_name') or assignee.get('orgname')
            ]) + "]"

            # Map grant data to the order of 'columns'
            row_values = (
                grant.get('id'),  # This is your generated UUID, now correctly mapped to the 'id' column
                grant.get('document_id'),  # document_id
                grant.get('reg_no'),  # reg_no
                grant.get('TRO'),  # TRO
                inventors_str or None,  # inventors
                assignees_str or None,  # assignee
                applicants_str or None,  # applicant
                grant.get('patent_title'),  # patent_title
                grant.get('date_published'),  # date_published
                grant.get('plaintiff_id', None),  # plaintiff_id
                grant.get('patent_type'),  # patent_type
                grant.get('abstract'),  # abstract -> first_claim_text (temporary mapping)
                grant.get('associated_patents', []),  # associated_patents
                grant.get('design_page_numbers', []),  # design_page_numbers
                grant.get('pdf_source'),  # pdf_source
                grant.get('image_source'),  # image_source
                grant.get('certificate_source'), # certificate_source
                grant.get('locarno', {}).get('code', None),             # loc_code
                grant.get('locarno', {}).get('edition', None),          # loc_edition
                (grant.get('uspc') or [None, None])[0],                 # uspc_class
                (grant.get('uspc') or [None, None])[1]                  # uspc_subclass
            )
            batch_values.append(row_values)

        # Execute batch upsert
        if batch_values:
            psycopg2.extras.execute_batch(cursor, sql, batch_values, page_size=500)
            conn.commit()
            processed_count = len(batch_values)
            logger.info(f"Successfully upserted {processed_count} patent grant records.")
        else:
            logger.info("No data prepared for batch upsert.")

    except psycopg2.DatabaseError as db_err:
        logger.error(f"Database error during patent grant upsert: {db_err}")
        if conn:
            conn.rollback()
    except Exception as e:
        logger.error(f"Unexpected error during patent grant upsert: {e}", exc_info=True)
        if conn:
            conn.rollback()
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()
            logger.info("Database connection closed.")

    return processed_count