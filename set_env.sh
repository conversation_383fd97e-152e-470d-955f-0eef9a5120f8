#!/bin/bash
# File to load .env variables into the environment in a linux server
# copy the file on the server, then chmod +x set_env.sh, then source ./set_env.sh

#!/bin/bash
ENV_FILE=".env"

if [ ! -f "$ENV_FILE" ]; then
  echo "Error: .env file not found at $ENV_FILE"
  exit 1
fi

while IFS='=' read -r key value; do
  # Trim leading/trailing whitespace from key and value
  key=$(echo "$key" | tr -d '[:space:]')
  value=$(echo "$value" | tr -d '[:space:]\r\n' | sed -e "s/^['\"]//g" -e "s/['\"]$//g")

  # Skip empty lines and comment lines
  if [[ -z "$key" ]] || [[ "$key" == \#* ]]; then
    continue
  fi
    # Handle special characters that should NOT be escaped
  if [[ "$key" == "MYSQL_PASSWORD" ]]; then
        value=$(echo "$value" | sed 's/\\\\\([;<]\)/\\\1/g')
  fi

  # Export the variable
  export "$key=$value"
  echo "Exported: $key"
done < "$ENV_FILE"


export PYTHONPATH="${PYTHONPATH}:$(pwd)"
echo ".env variables loaded into the environment and PYTHONPATH set."