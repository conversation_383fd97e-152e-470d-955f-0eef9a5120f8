#!/bin/python
"""
NAS Patent Unzip Script

This script is designed to be run on the NAS. It extracts USPTO patent archives
(.tar files containing nested .zip files for PTGRDT grants) to a specified
extraction directory on the NAS.

It replicates the core extraction logic from the local patent_file.py.
"""

import os
import sys
import argparse
import zipfile
import tarfile
import re
import logging
import shutil
from pathlib import Path

# Configure basic logging to stdout
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - NAS_Patent_Unzip - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger(__name__)

def extract_archive_on_nas(archive_path_on_nas, extract_base_dir_on_nas):
    """
    Extracts USPTO patent archives on the NAS.

    Args:
        archive_path_on_nas (str): Absolute path to the downloaded USPTO archive file (.tar) on the NAS.
        extract_base_dir_on_nas (str): Absolute path to the base directory on the NAS where files will be extracted.

    Returns:
        bool: True if extraction was successful (or at least attempted without critical failure), False otherwise.
    """
    all_extracted_files_count = 0
    archive_path_obj = Path(archive_path_on_nas)
    file_extension = archive_path_obj.suffix.lower()

    logger.info(f"Starting extraction for NAS archive: {archive_path_on_nas}")
    logger.info(f"Target NAS extraction base directory: {extract_base_dir_on_nas}")

    try:
        # Ensure the base extraction directory exists on the NAS
        Path(extract_base_dir_on_nas).mkdir(parents=True, exist_ok=True)
        logger.info(f"Ensured NAS extraction base directory exists: {extract_base_dir_on_nas}")

        if file_extension == '.tar':
            logger.info(f"Processing PTGRDT tar file: {archive_path_on_nas}")
            with tarfile.open(archive_path_obj, 'r') as tar_ref:
                for member in tar_ref.getmembers():
                    path_parts = member.name.split('/')
                    if len(path_parts) > 1 and path_parts[0].endswith('-SUPP'):
                        logger.debug(f"Skipping member in -SUPP directory: {member.name}")
                        continue

                    if member.isfile() and \
                       (re.search(r'/DESIGN/', member.name, re.IGNORECASE) or re.search(r'/UTIL\d+/', member.name, re.IGNORECASE)) and \
                       member.name.lower().endswith('.zip'):

                        logger.debug(f"Found inner zip in tar: {member.name}")
                        inner_zip_path_in_tar = member.name
                        inner_zip_basename = Path(inner_zip_path_in_tar).name

                        # Determine directory structure (last 2 digits, then 2 digits before that)
                        # Example filename: **********-20240312.ZIP or ********-20240305.ZIP
                        patent_number_part = inner_zip_basename.split('-')[0]
                        
                        # Try to extract numbers for directory structure. Handles 'US' prefix and 'D' prefix.
                        numeric_part_match = re.search(r'(\d+)', patent_number_part)
                        if not numeric_part_match:
                            logger.warning(f"Could not extract numeric part from patent number: {patent_number_part} in {inner_zip_path_in_tar}")
                            continue
                        
                        patent_number_digits = numeric_part_match.group(1)

                        if len(patent_number_digits) >= 4:
                            # Take last four digits: e.g., from '11928532', take '8532' -> xx='32', yy='85'
                            # from '1017000', take '7000' -> xx='00', yy='70'
                            yy = patent_number_digits[-4:-2] # Digits for the 'yy' folder (e.g., 85 from 8532)
                            xx = patent_number_digits[-2:]  # Digits for the 'xx' folder (e.g., 32 from 8532)
                        elif len(patent_number_digits) >= 2: # For numbers like '123' -> xx='23', yy='01' (padded) or '12' -> xx='12', yy='00'
                            yy = patent_number_digits[-len(patent_number_digits):-2] if len(patent_number_digits) > 2 else "00"
                            xx = patent_number_digits[-2:]
                            yy = yy.zfill(2) # Ensure two digits
                        else: # For numbers like '1' -> xx='01', yy='00'
                            yy = "00"
                            xx = patent_number_digits.zfill(2)
                            
                        logger.debug(f"Patent number part: {patent_number_digits}, yy: {yy}, xx: {xx}")

                        final_sub_dir = Path(extract_base_dir_on_nas) / xx / yy
                        final_sub_dir.mkdir(parents=True, exist_ok=True)
                        logger.debug(f"Created NAS subdirectory for inner zip: {final_sub_dir}")

                        # Path to temporarily save the inner zip before extracting its contents
                        temp_inner_zip_extract_path = final_sub_dir / inner_zip_basename

                        try:
                            member_fileobj = tar_ref.extractfile(member)
                            if member_fileobj:
                                with open(temp_inner_zip_extract_path, 'wb') as f_out:
                                    shutil.copyfileobj(member_fileobj, f_out)
                                logger.debug(f"Extracted inner zip from tar to: {temp_inner_zip_extract_path}")
                            else:
                                logger.error(f"Failed to get file object for inner zip member {member.name} from tar.")
                                continue

                            # Process the extracted inner zip file
                            with zipfile.ZipFile(temp_inner_zip_extract_path, 'r') as inner_zip_ref:
                                for item_name_in_zip in inner_zip_ref.namelist():
                                    try:
                                        # Extract directly into final_sub_dir
                                        inner_zip_ref.extract(item_name_in_zip, path=final_sub_dir)
                                        all_extracted_files_count += 1
                                        logger.debug(f"Extracted from inner zip: {final_sub_dir / item_name_in_zip}")
                                    except Exception as extract_err:
                                        logger.error(f"Error extracting file '{item_name_in_zip}' from inner zip {temp_inner_zip_extract_path}: {extract_err}", exc_info=True)
                        except Exception as inner_err:
                            logger.error(f"Error processing inner zip {inner_zip_path_in_tar}: {inner_err}", exc_info=True)
                        finally:
                            # Clean up the temporarily extracted inner zip file
                            if temp_inner_zip_extract_path.exists():
                                try:
                                    temp_inner_zip_extract_path.unlink()
                                    logger.debug(f"Cleaned up intermediate inner zip: {temp_inner_zip_extract_path}")
                                except OSError as del_err:
                                    logger.error(f"Failed to delete intermediate inner zip {temp_inner_zip_extract_path}: {del_err}")
                    elif member.isfile() and member.name.lower().endswith('.xml') and not (re.search(r'/DESIGN/', member.name, re.IGNORECASE) or re.search(r'/UTIL\d+/', member.name, re.IGNORECASE)):
                        # Handle XML files at the root of the tar, if any (not typical for PTGRDT but good to be aware)
                        # This case might not be strictly necessary if PTGRDT always nests zips.
                        try:
                            tar_ref.extract(member, path=extract_base_dir_on_nas)
                            all_extracted_files_count += 1
                            logger.info(f"Extracted top-level file from tar: {Path(extract_base_dir_on_nas) / member.name}")
                        except Exception as e:
                            logger.error(f"Error extracting top-level file {member.name} from tar: {e}", exc_info=True)


        elif file_extension == '.zip': # Primarily for CPC files, or if grants change format
            logger.info(f"Processing direct ZIP file: {archive_path_on_nas}")
            with zipfile.ZipFile(archive_path_obj, 'r') as zip_ref:
                for member_name in zip_ref.namelist():
                    try:
                        # For direct zip, extract into a subdirectory named after the zip itself (without extension)
                        # to keep things organized, similar to how local extraction might behave.
                        zip_stem = archive_path_obj.stem
                        specific_extract_dir = Path(extract_base_dir_on_nas) / zip_stem
                        specific_extract_dir.mkdir(parents=True, exist_ok=True)

                        zip_ref.extract(member_name, path=specific_extract_dir)
                        all_extracted_files_count += 1
                        logger.debug(f"Extracted from zip: {specific_extract_dir / member_name}")
                    except Exception as extract_err:
                        logger.error(f"Error extracting file '{member_name}' from zip {archive_path_on_nas}: {extract_err}", exc_info=True)
        else:
            logger.error(f"Unsupported file type for extraction on NAS: {archive_path_on_nas}. Only .tar and .zip are supported by this script.")
            return False

        if all_extracted_files_count > 0:
            logger.info(f"Successfully extracted {all_extracted_files_count} file(s) from {archive_path_on_nas} to {extract_base_dir_on_nas} and its subdirectories.")
        else:
            logger.warning(f"No files were extracted from {archive_path_on_nas}. This might be normal if the archive was empty or contained no relevant content.")
        return True

    except (zipfile.BadZipFile, tarfile.ReadError, tarfile.TarError) as archive_err:
        logger.error(f"Failed to open or read archive file {archive_path_on_nas} on NAS: {archive_err}", exc_info=True)
        return False
    except FileNotFoundError:
        logger.error(f"Archive file not found on NAS: {archive_path_on_nas}", exc_info=True)
        return False
    except Exception as e:
        logger.error(f"General error extracting patent archive {archive_path_on_nas} on NAS: {e}", exc_info=True)
        return False

def main():
    parser = argparse.ArgumentParser(description="Extracts USPTO patent archives on the NAS.")
    parser.add_argument("source_archive_path_on_nas", type=str,
                        help="Absolute path to the downloaded USPTO archive file (.tar or .zip) on the NAS.")
    parser.add_argument("target_extract_base_dir_on_nas", type=str,
                        help="Absolute path to the base directory on the NAS where files will be extracted.")

    args = parser.parse_args()

    logger.info("NAS Patent Unzip Script started.")
    logger.info(f"Received source archive path: {args.source_archive_path_on_nas}")
    logger.info(f"Received target extraction base directory: {args.target_extract_base_dir_on_nas}")

    success = extract_archive_on_nas(args.source_archive_path_on_nas, args.target_extract_base_dir_on_nas)

    if success:
        logger.info("NAS Patent Unzip Script completed successfully.")
        sys.exit(0)
    else:
        logger.error("NAS Patent Unzip Script completed with errors.")
        sys.exit(1)

if __name__ == "__main__":
    main()