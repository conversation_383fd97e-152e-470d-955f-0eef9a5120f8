import os, sys, imagehash, shutil, pytesseract, re, json, fitz, pickle, cv2, asyncio, getpass
sys.path.append(os.getcwd())
import numpy as np
from PIL import Image
import pandas as pd
import tempfile
from logdata import log_message
from Common.Constants import local_case_folder, local_plaintiff_folder, sanitize_name
from Alerts.PicturesProcessing.ProcessPicturesTrademarks import process_trademark_exhibit, process_trademark_regno, get_trademark_data_by_name
from Alerts.PicturesProcessing.ProcessPicturesPatents import process_patent_exhibit, get_and_process_patents_from_uspto_using_regnos, get_and_process_patents_from_uspto_using_plaintiff_name
from Alerts.PicturesProcessing.ProcessPicturesCopyrights import process_copyright_exhibit, process_copyright_regno, process_copyright_images_from_google
from IP.Patent import clean_reg_no
from IP.Copyright_UCO import scrape_USCO

from Alerts.PicturesProcessing.GetFilesProcessedBefore import get_file_processed_before
from Alerts.PicturesProcessing.ProcessPicturesShared import keywords
from FileManagement.SendToNAS import connect_to_nas
from DatabaseManagement.ImportExport import get_table_from_GZ, insert_and_update_df_to_GZ_batch
from Alerts.PicturesProcessing.OCRProcessor import OCRProcessor
from AI.GC_VertexAI import vertex_genai_multi
from AI.LLM_shared import get_json
from langfuse.decorators import observe, langfuse_context
from Alerts.Plaintiff import remove_acronyms

# ❌⚠️📥🔥✅☑️✔️💯💧⛏🔨🗝🔑 🔒💡⛔🚫❗


def delete_directory(directory):
    if os.path.exists(directory):
        shutil.rmtree(directory)


def process_pictures_multiple_cases(df_db, df, plaintiff_df, force=False):
    #Create templates hash:
    copyright_template1_hash, copyright_template1_size = get_template(os.path.join(os.getcwd(), 'data', 'ImageTemplates', 'Copyright1'))

    for i, (index, row) in enumerate(df.iterrows()):
        split_pattern = r'\s+v\.|\s+V\.'  # Raw string for regex pattern
        log_message(f"{i+1}/{len(df)}: Processing pictures for {pd.to_datetime(row['date_filed'], errors='coerce').strftime('%Y-%m-%d')} - {row['docket']} : \"{re.split(split_pattern, row['title'], 1)[0]}\", a {row['nos_description']} case")
        process_pictures(df_db, df, plaintiff_df, index, copyright_template1_hash, copyright_template1_size, force=force)   # Why? Log 1 case at a time

    return df


# @profile
@observe(capture_input=False, capture_output=False)
def process_pictures(df_db, df, plaintiff_df, index, copyright_template1_hash, copyright_template1_size, force=False):
    row = df.loc[index]
    if not row['nos_description']:
        print(f"\033[91m!!!!! process_pictures ending early: NO nos_description for {row['date_filed']} - {row['docket']}\033[0m")
        return df

    langfuse_context.update_current_trace(name="Process Pictures", session_id=f"{row['date_filed']} - {row['docket']}", user_id=getpass.getuser())
    

    if langfuse_context.get_current_trace_url():
        trace_url = "https://langfuse.sergedc.com/" + "/".join(langfuse_context.get_current_trace_url().replace("://", "").split("/")[1:])
    else:
        trace_url = None

    # Add the column if it doesn't exist
    # To check how this matches with the code, search for:  df.at[index, 'images_status']['
    df.at[index, 'images_status'] = {
        'steps_downloaded': [],
        'number_of_pdfs': 0,
        'need_more_pdfs': False,
        'scheduled_next_fetch': None,
        'sealed_or_redacted': False,
        'trace_url': trace_url,
        'copyright_status': {
            'exhibit': {'count': 0, 'steps': []},
            'byregno': {'ai_reg_nos': [], 'count': 0},
            'bygoogle': {'count': 0, 'search_term': ''},
            'manual': {'count': 0, 'comment': ''}
        },
        'trademark_status': {
            'exhibit': {'count': 0, 'steps': [], 'sources': {'USPTO_URL': 0, 'USPTO_STATUSZIP': 0, 'USPTO_DOCZIP': 0, 'OCR': 0}},
            'byregno': {'ai_reg_nos': [], 'count': 0, 'sources': {'USPTO_URL': 0, 'USPTO_STATUSZIP': 0, 'USPTO_DOCZIP': 0}},
            'byname': {'count': 0, 'search_term_used': '', 'sources': {'USPTO_URL': 0, 'USPTO_STATUSZIP': 0, 'USPTO_DOCZIP': 0}},
            'manual': {'count': 0, 'comment': ''}
        },
        'patent_status': {
            'exhibit': {'count': 0, 'steps': []},
            'byregno': {'ai_reg_nos': [], 'count': 0},
            'byname': {'count': 0, 'search_term_used': '', 'ai_search_term': ''},
            'manual': {'count': 0, 'comment': ''}
        }
    }
        
    # Reset images fields to make sure it has the right structure
    df.at[index, 'images'] = {'trademarks': {}, 'patents': {}, 'copyrights': {}}
    
    # Initialize validation_status field
    df.at[index, 'validation_status'] = ''
    
    case_directory = os.path.join(local_case_folder, sanitize_name(f"{pd.to_datetime(row['date_filed'], errors='coerce').strftime('%Y-%m-%d')} - {row['docket']}"))
    case_images_directory = os.path.join(case_directory, 'images')
    if os.path.exists(case_images_directory):
        shutil.rmtree(case_images_directory) # create_plaintiff_images_view uses shutil.copytree() which requires the folder to be gone
    
    # Calculate the total number of PDFs in the case directory and subdirectories
    if os.path.exists(case_directory):
        for root, dirs, files in os.walk(case_directory):
            df.at[index, 'images_status']['number_of_pdfs'] += sum(1 for file in files if file.lower().endswith('.pdf'))
    
    # Check if we want to reuse an existing case
    if not force:
        sftp, transport = connect_to_nas()
        log_message("   + Connected to NAS successfully.")
        if get_file_processed_before(df_db, df, index, sftp):
            create_plaintiff_images_view(plaintiff_df, case_directory, row['plaintiff_id'])
            sftp.close()
            transport.close()
            return df
        else:
            sftp.close()
            transport.close()
    
    log_message(f"   + Plaintiff's case not processed before, processing it now from scratch")

    os.makedirs(case_images_directory, exist_ok=True)

    is_trademark = is_copyright = is_patent = found_IP = has_copyright_pictures = False
    reg_numbers_pictures_not_found = {"trademark": [], "copyright": [], "patent": []}
    directory_list = os.listdir(case_directory)
    df.at[index, 'images_status']['steps_downloaded'] = [folder for folder in directory_list if folder != "images"]
    
    good_pdfs = []
    pdf_ocr_data = {}
    for j, step_directory in enumerate(directory_list):
        log_message(f"🔨 Processing step {j+1}/{len(directory_list)}: {step_directory}")            
        if step_directory == "images":
            continue
        
        step_directory_path = os.path.join(case_directory, step_directory)
        pdf_files = [f for f in os.listdir(step_directory_path) if f.lower().endswith('.pdf')]
        pdf_files.sort()
        for i, pdf_file in enumerate(pdf_files):
            if "civil_cover_sheet" in pdf_file.lower() or "schedule_a" in pdf_file.lower():
                continue
            
            pdf_file_path = os.path.join(step_directory_path, pdf_file)
            pdf_document = fitz.open(pdf_file_path)
            if pdf_document.page_count == 0: # the file is corrupted
                continue
            pdf_file_no_ext = os.path.splitext(pdf_file)[0]
            os.makedirs(os.path.join(step_directory_path, pdf_file_no_ext), exist_ok=True)

            # Look for rubish report using the 1st 4 pages
            match = re.findall(r'(?i)(?<=Exhibit)[^\d]*(\d)', pdf_file)
            exhibit_number = int(match[-1] if match else '0')
            if (found_IP or exhibit_number > 1): # and (pdf_document.page_count in [28, 45, 55, 9, 37, ]):
                full_text, pages_text_json, page_ocr_data, page_images = OCRProcessor.process_pdf(pdf_document, [0, 1, 2, 3])
                if any(keyword in full_text.lower() for keyword in ["Security Imperative", "illicit trade of goods", "heavy recruitment of chinese", "counterfeit and pirated goods", "office of strategy", "counterfeiting in the age of the internet", "accreditation", "department of commerce", "white paper", "chamber of commerce", "office of trade", "border protection", "hague convention", "civil procedure", "convention on the service", "seizure statistics", "notorious markets", " inta ", "traffic report", "silk road", "state of the", "briefing papers"]):
                    log_message(f"  🚫 Rubish report found after OCR of only 4 p.a.g.e.s.")
                    shutil.rmtree(os.path.join(step_directory_path, pdf_file_no_ext))
                    for pdf_file in pdf_files[i+1:]:
                        if "complaint" in pdf_file.lower():
                            good_pdfs.append(pdf_file_path)  # before break, add complaint to good_pdfs
                    break # stop looking for IPs in other pdfs

            good_pdfs.append(pdf_file_path)  # Always includes the complaint, but excludes the rubish report, civil cover sheet, and schedule A

            if found_IP and "complaint" in pdf_file.lower():
                break # stop looking for IPs in other pdfs

            full_text, pages_text_json, page_ocr_data, page_images = OCRProcessor.process_pdf(pdf_document, range(pdf_document.page_count))
            
            # If the top of page 2 has multiple different case numbers, it means that the document has documents from another case. It should be ignored, or we could include the IP from those cases.
            if 2 in pages_text_json:
                top_of_page_2 = pages_text_json[2][:150]
                case_numbers = re.findall(r"-cv-(\d+)", top_of_page_2 )
                if len(set(case_numbers)) > 1: 
                    log_message(f"  🚫 Found a document that has documents from another case => excluding it")
                    shutil.rmtree(os.path.join(step_directory_path, pdf_file_no_ext))
                    good_pdfs.remove(pdf_file_path)
                    continue

            pdf_ocr_data[pdf_file_path] = [page_ocr_data, pages_text_json, page_images]

            if "principal register" in full_text.lower():
                log_message(f"  💡 Trademark keyword found.")
                is_trademark = process_trademark_exhibit(df, index, case_images_directory, step_directory_path, pdf_document, pdf_file, pages_text_json, page_ocr_data, page_images)
                print("")
            if any(all(keyword in full_text.lower() for keyword in keywordset) for keywordset in keywords["Patent"]):
                log_message(f"  💡 Patent keyword found.")
                # is_patent = process_patent_images_using_ocr(df, index, case_images_directory, step_directory_path, pdf_document, pdf_file, pages_text_json, page_ocr_data, page_images)
                is_patent = asyncio.run(process_patent_exhibit(df, index, case_directory, pdf_file_path))
                print("")

            keyword_required = 4 if (pdf_document.page_count == 28 or pdf_document.page_count == 45 or pdf_document.page_count == 55) else 3  # avoid the rubish report
            if any(sum(keyword in full_text.lower() for keyword in keyword_set) >= keyword_required for keyword_set in keywords["Copyright"]):
                log_message(f"  💡 Copyright keyword found.")
                is_copyright, has_copyright_pictures = process_copyright_exhibit(df, index, copyright_template1_hash, copyright_template1_size, case_images_directory, step_directory_path, pdf_document, pdf_file, pages_text_json, page_ocr_data, page_images)
                print("")

            if is_trademark or is_copyright or is_patent:
                found_IP = True

            # If we found an IP, and we hit one of the reports, we can stop looking for other IPs
            # !!! The complaint can easily be confused with one of these reports
            if found_IP and any(keyword in full_text for keyword in ["heavy recruitment of chinese", "counterfeit and pirated goods", "office of strategy", "counterfeiting in the age of the internet", "accreditation", "department of commerce", "white paper", "chamber of commerce", "office of trade", "border protection", "hague convention", "civil procedure", "convention on the service", "seizure statistics", "notorious markets", " inta ", "traffic report", "silk road", "state of the", "briefing papers"]):
                log_message(f"  🚫 Rubish report found.")
                shutil.rmtree(os.path.join(step_directory_path, pdf_file_no_ext))
                break # stop looking for IPs in other pdfs


        if is_trademark or (is_copyright and has_copyright_pictures) or is_patent:
            break # stop looking for IPs in other steps
        else:
            print(f"   + No IP found in step {step_directory}, going to {directory_list[j+1] if j+1 < len(directory_list) else 'end'}")

    
    ### Get reg numbers and images from PDFs: for Trademarks, Patents, and Copyrights
    if len(good_pdfs) > 0 and need_more_ip(df, index, has_copyright_pictures):
        log_message(f"🔨 Need more IP, going to ask LLM to find reg numbers")       
        find_pictures_in_PDFs_using_regno_from_llm(df, index, case_images_directory, step_directory_path, pdf_ocr_data, good_pdfs, reg_numbers_pictures_not_found)


    ### Copyrights: if reg numbers were found in PDFs but not the images => search on google
    if len(reg_numbers_pictures_not_found["copyright"]) > 0:
        is_copyright = True  # i.e. we have reg numbers, not need to scrape USCO for the plaintiff names
        # Create DataFrame from reg numbers found in PDFs
        df_missing_copyright_images = pd.DataFrame(columns=["reg_no", "Copyright Claimant", "Title", "best_google_image"])
        for reg_no, artist_name, artwork_name in reg_numbers_pictures_not_found["copyright"]:
            df_missing_copyright_images.loc[len(df_missing_copyright_images)] = {"reg_no": reg_no, "Copyright Claimant": artist_name, "Title": artwork_name, "best_google_image": None}
        process_copyright_images_from_google(df, index, df_missing_copyright_images, case_images_directory)

    if df.at[index, "plaintiff_id"] != 9: # Searchby plaintiff name if not Unidentified Claimant
        ### Copyrights: if reg numbers were not found in PDFs => search for plaintiff names on USCO
        if not df.at[index, 'images']["copyrights"] and ("copyright" in row['nos_description'].lower()):
            copyrights_from_usco = None
            # Get copyright registrations from USCO based on plaintiff names
            for claimant_name in json.loads(row['plaintiff_names']):
                search_name = remove_acronyms(claimant_name)
                if len(search_name) > 2 and "schedule" not in search_name.lower() and not search_name.lower().startswith(("abc", "xyz", "claimant", "suppressed", "unknown")):
                    copyrights_from_usco = scrape_USCO(claimant_name)
                    
            if copyrights_from_usco is not None and len(copyrights_from_usco) > 0:
                # Prepare data for Google search
                copyrights_from_usco["reg_no"] = copyrights_from_usco["Registration Number / Date"].apply(lambda x: x.split("/")[0])
                copyrights_from_usco["best_google_image"] = None
                
                is_copyright = process_copyright_images_from_google(df, index, copyrights_from_usco, case_images_directory)
        
        
        ### Patents: if reg numbers were not found in PDFs => search for plaintiff names on USCO
        if not df.at[index, 'images']["patents"] and ("patent" in row['nos_description'].lower()):
            asyncio.run(get_and_process_patents_from_uspto_using_plaintiff_name(df, index, case_directory, plaintiff_df))
        
        ### Trademarks: if reg numbers were not found in PDFs => search for plaintiff names on USCO
        if not df.at[index, 'images']["trademarks"] and ("trademark" in row['nos_description'].lower()):
            # search based on plaintiff name
            asyncio.run(get_trademark_data_by_name(df, index, case_images_directory, plaintiff_df))
        

    # Create resized images using existing OCRProcessor pool
    image_files = [   
        image_filename for image_filename in os.listdir(case_images_directory)
        if image_filename.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.gif', '.tiff', '.webp', '.jpx'))
    ]
    
    # Get the existing pool from OCRProcessor
    args = [(case_images_directory, filename, case_directory) for filename in image_files]
    results = OCRProcessor.get_pool().starmap(OCRProcessor.create_resized_image, args)
    
    for image_filename, success_resize in zip(image_files, results): # Remove failed images from df
        if not success_resize:
            df.at[index, 'images']['trademarks'].pop(image_filename, None)
            df.at[index, 'images']['patents'].pop(image_filename, None)
            df.at[index, 'images']['copyrights'].pop(image_filename, None)
            print(f"\033[91m!!!!! COULD NOT RESIZE {image_filename}\033[0m")


    create_plaintiff_images_view(plaintiff_df, case_directory, row['plaintiff_id'])

    langfuse_context.update_current_observation( metadata={"IP": ("T" if is_trademark else "") + ("P" if is_copyright else "") + ("C" if is_patent else "")} )
    
    # df.at[index, 'images'] = json.dumps(df.at[index, 'images'])
    # df.at[index, 'images_status'] = json.dumps(df.at[index, 'images_status'])

    return df


@observe(capture_input=False, capture_output=False)
def find_pictures_in_PDFs_using_regno_from_llm(df, index, case_images_directory, step_directory_path, pdf_ocr_data, good_pdfs, reg_numbers_pictures_not_found):
    # Count total pages across all PDFs
    with tempfile.TemporaryDirectory(prefix="merged_pdfs_") as temp_dir:
        if len(good_pdfs) > 8:
            total_pages = 0
            pdf_index = 1
            pdf_to_merge = []
            for pdf_file_path in good_pdfs:
                pdf_document = fitz.open(pdf_file_path)
                total_pages += pdf_document.page_count
                pdf_document.close()
                if total_pages > 100:
                    OCRProcessor.merge_pdfs(pdf_to_merge, os.path.join(temp_dir, f"merged_{pdf_index}.pdf"))
                    pdf_index += 1
                    pdf_to_merge = [pdf_file_path]
                else:
                    pdf_to_merge.append(pdf_file_path)
            if len(pdf_to_merge) > 0:
                OCRProcessor.merge_pdfs(pdf_to_merge, os.path.join(temp_dir, f"merged_{pdf_index}.pdf"))
                pdf_index += 1
            pdfs_for_llm = [os.path.join(temp_dir, f"merged_{i}.pdf") for i in range(1, pdf_index)]
        else:
            pdfs_for_llm = good_pdfs
    
        prompt = """I am looking for trademark registration numbers, copyright registration numbers, and patent registration numbers in these legal filings. Please help me find them.
        - The trademark registration numbers are in the format of 6 to 8 numbers.
        - The copyright registration numbers are in the format of VAx123456 or TXx123456 or PAx123456
        - The patent registration numbers are in the format of US D? xxx,xxx S? Bx? usually with 6 numbers
        You return your answer in a JSON format with the following keys: {"trademark_registration_numbers": [], "copyright_registration_numbers": [], "patent_registration_numbers": []}. Leave each list empty if you don't find any.
        Do not include REDACTED registration numbers in your answer.
        """
        prompt_list = [("text", prompt)]
        for pdf_file_path in pdfs_for_llm:
            prompt_list.append(("pdf_path", pdf_file_path))

        ai_answer = vertex_genai_multi(prompt_list, model_name="gemini-2.0-flash-thinking-exp-01-21")
        ai_answer_json = get_json(ai_answer)

    if not ai_answer_json:
        print(f"\033[91m!!!!! find_pictures_in_PDFs_using_llm: NO ANSWER FROM LLM: ai_answer = {ai_answer}\033[0m")
    else: 
        if len(ai_answer_json.get("trademark_registration_numbers", [])) == 0 and len(ai_answer_json.get("copyright_registration_numbers", [])) == 0 and len(ai_answer_json.get("patent_registration_numbers", [])) == 0:
            print(f"\033[91m!!!!!find_pictures_in_PDFs_using_llm: NO IP FOUND: ai_answer = {ai_answer_json}\033[0m")
        else: # Fill up any missing pdf_ocr_data (e.g. when we added the complaint to the good_pdfs list, we did not do OCR)
            print(f"\033[32m!!!!! find_pictures_in_PDFs_using_llm: AI found Reg Nos {ai_answer_json} => Filling up any missing OCR data\033[0m")
            for pdf_file_path in good_pdfs:
                if pdf_file_path not in pdf_ocr_data:
                    full_text, pages_text_json, page_ocr_data, page_images = OCRProcessor.process_pdf(pdf_file_path)
                    pdf_ocr_data[pdf_file_path] = [page_ocr_data, pages_text_json, page_images]
        
        images = df.at[index, 'images']
            
        # Process trademark registration numbers
        trademark_reg_nos = ai_answer_json.get("trademark_registration_numbers", [])
        if len(trademark_reg_nos) > 0:
            df.at[index, 'images_status']['trademark_status']['byregno']['ai_reg_nos'] = trademark_reg_nos
            reg_nos = [str(reg_no) for reg_no in trademark_reg_nos if not str(reg_no).startswith("3736-0624")]
            already_found_trademarks = [value_json["reg_no"] for value_json in images["trademarks"].values()]
            reg_nos = [reg_no for reg_no in reg_nos if reg_no not in already_found_trademarks]
            if len(reg_nos) > 0:
                process_trademark_regno(df, index, case_images_directory, good_pdfs, pdf_ocr_data, reg_nos)
            else:
                print(f"\033[32m!!!!! find_pictures_in_PDFs_using_llm: But the trademarks were already found in exhibits\033[0m")

        # Process copyright registration numbers
        copyright_reg_nos = ai_answer_json.get("copyright_registration_numbers", [])
        if len(copyright_reg_nos) > 0:
            df.at[index, 'images_status']['copyright_status']['byregno']['ai_reg_nos'] = copyright_reg_nos
            reg_nos = [str(reg_no) for reg_no in copyright_reg_nos if not str(reg_no).startswith("3736-0624")]
            already_found_copyrights = [value_json["reg_no"] for value_json in images["copyrights"].values()]
            reg_nos = [reg_no for reg_no in reg_nos if reg_no not in already_found_copyrights]
            if len(reg_nos) > 0:
                process_copyright_regno(df, index, case_images_directory, good_pdfs, pdf_ocr_data, reg_nos, reg_numbers_pictures_not_found)
            else:
                print(f"\033[32m!!!!! find_pictures_in_PDFs_using_llm: But the copyrights were already found in exhibits\033[0m")

        # Process patent registration numbers
        patent_reg_nos = ai_answer_json.get("patent_registration_numbers", [])
        if len(patent_reg_nos) > 0:
            df.at[index, 'images_status']['patent_status']['byregno']['ai_reg_nos'] = patent_reg_nos
            already_found_patents = [value_json["patent_number"] for value_json in images["patents"].values()]
            reg_nos = [
                str(reg_no) for reg_no in patent_reg_nos
                if not any(clean_reg_no(reg_no) in found_patent for found_patent in already_found_patents)
            ]

            if len(reg_nos) > 0:
                asyncio.run(get_and_process_patents_from_uspto_using_regnos(df, index, os.path.dirname(case_images_directory), reg_nos))
            else:
                print(f"\033[32m!!!!! find_pictures_in_PDFs_using_llm: But the patents were already found in exhibits\033[0m")




def create_plaintiff_images_view(plaintiff_df, case_directory, plaintiff_id):
    try:
        if len(os.listdir(os.path.join(case_directory, 'images'))) != 0:
            os.makedirs(local_plaintiff_folder, exist_ok=True)
            plaintiff_name = plaintiff_df[plaintiff_df['id'] == plaintiff_id]['plaintiff_name'].values[0]
            shutil.copytree(os.path.join(case_directory, 'images'), os.path.join(local_plaintiff_folder, sanitize_name(plaintiff_name)), dirs_exist_ok=True)
    except Exception as e:
        print(f"\033[91mError creating plaintiff images view: {e}\033[0m")

# Function to load and process images
def load_and_process_image(filepath, size):
    with Image.open(filepath) as img:
        img = img.convert('L')  # Convert to grayscale
        img = img.resize(size, Image.Resampling.LANCZOS)
        return np.array(img, dtype=np.float32)
    

def get_template(template_folder):
    template_name = os.path.basename(template_folder)
    hash_file = os.path.join(os.getcwd(), 'data', 'ImageTemplates', f"{template_name}_hash.pkl")
    size_file = os.path.join(os.getcwd(), 'data', 'ImageTemplates', f"{template_name}_size.pkl")

    if os.name == "nt":
        force_update = False
    else:
        force_update = False

    if not force_update and os.path.exists(hash_file) and os.path.exists(size_file):
        with open(hash_file, 'rb') as f:
            template_hash = pickle.load(f)
        with open(size_file, 'rb') as f:
            template_size = pickle.load(f)
    else:
        template_hash, template_size = create_template(template_folder)
        with open(hash_file, 'wb') as f:
            pickle.dump(template_hash, f)
        with open(size_file, 'wb') as f:
            pickle.dump(template_size, f)
    
    return template_hash, template_size
    

def create_template(template_folder):
   # Approach 1: Load template images and compute the average image
    template_images = []
    size = None

    # Approach 2: Compute a Representative Hash
    template_hashes = []
    hash_size = 8  # Default hash size for phash

    for filename in os.listdir(template_folder):
        if filename.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.gif', '.tiff', '.webp', '.jpx')):
            filepath = os.path.join(template_folder, filename)
            with Image.open(filepath) as img:
                if size is None:
                    size = img.size  # Set the size based on the first image
                else:
                    img = img.resize(size, Image.Resampling.LANCZOS)
                img_array = load_and_process_image(filepath, size)
                template_images.append(img_array)
                
                hash = imagehash.phash(img, hash_size=hash_size)
                template_hashes.append(hash.hash.flatten())

    # Approach 1: Compute the hash of the average image
    average_image_array = np.mean(template_images, axis=0)
    average_image = Image.fromarray(average_image_array.astype('uint8'))
    average_hash = imagehash.phash(average_image)
    
    # Approach 2: Compute the representative hash (median hash)
    # template_hashes_array = np.array(template_hashes)
    # median_hash_array = np.median(template_hashes_array, axis=0)
    # median_hash = median_hash_array > 0.5  # Convert median values to boolean
    # median_hash = imagehash.ImageHash(median_hash.reshape((hash_size, hash_size)))
    # clasify_images(df, median_hash, size)

    return average_hash, size

def need_more_ip(df, index, has_copyright_pictures):
    row = df.loc[index]

    if row["images"]["copyrights"] != {} and not has_copyright_pictures: 
        return True
    if row["images"]["patents"] == {} and  "patent" in row['nos_description'].lower():
        return True
    if row["images"]["copyrights"] == {} and "copyright" in row['nos_description'].lower():
        return True
    if row["images"]["trademarks"] == {} and "trademark" in row['nos_description'].lower():
        return True
    return False


if __name__ == "__main__":
    import zlib
    import base64
    df = get_table_from_GZ("tb_case", force_refresh=False)
    # insert_and_update_df_to_GZ_batch(df, "tb_case", "id")
    df_db = df.copy()
    plaintiff_df = get_table_from_GZ("tb_plaintiff", force_refresh=False)

    # Patent testing
    # df = df[df["docket"].str.contains("24-cv-08816")] # Patent in exhibit
    # df = df[df["docket"].str.contains("24-cv-04152")] # Patent description in Complaint, not Reg_No => using name
    # df = df[df["docket"].str.contains("24-cv-01358")]  # Patent reg no in complaint
    # df = df[df["docket"].str.contains("1:24-cv-02884")]  # trademark in exhibit to amened complaint  & has a patent too!
    # df = df[df["docket"].str.contains("1:24-cv-02592")]  # patent case but some trademarks reg no found in files
    # df = df[df["docket"].str.contains("1:24-cv-08709")]  # patent by name -fixed
    # df = df[df["docket"].str.contains("1:24-cv-07257")]  # patent ?  -fixed
    # df = df[df["docket"].str.contains("1:23-cv-15701")]   # patent case and AI found reg no in files for ... and it has copyright!  
    # df = df[df["docket"].str.contains("1:24-cv-08816")]
    # df = df[df["docket"].str.contains("1:24-cv-07257")]  # by name (Yang many answers!)
    # df = df[df["docket"].str.contains("1:23-cv-16802")]  # in exhibit

    # Trademark testing
    # df = df[df["docket"].str.contains("1:25-cv-20593")]  # trademark : AI found reg numbers
    # df = df[df["docket"].str.contains("1:25-cv-01865")]  # trademark : AI found reg numbers
    # df = df[df["docket"].str.contains("1:25-cv-20585")]  # trademark : using name
    # df = df[df["docket"].str.contains("1:24-cv-11969")]  # trademark : ???
    # df = df[df["docket"].str.contains("1:24-cv-08809")]  # trademark : ???
    # df = df[df["docket"].str.contains("1:24-cv-08747")]  # trademark : ???
    # df = df[df["docket"].str.contains("1:24-cv-08742")]  # trademark : ???
    # df = df[df["docket"].str.contains("1:24-cv-08648")]  # trademark : ???

    # Copyright testing
    # df = df[df["docket"].str.contains("1:23-cv-15701")]  # copyright : ???

    # Case where one of the appendix has documents from another case
    # df = df[df["docket"].str.contains("1:23-cv-15894")]  # trademark : ???

    df = df[df["docket"].str.contains("1:23-cv-22812")]

    df = process_pictures_multiple_cases(df_db, df, plaintiff_df, force=True)


    df_with_trademark_copyright_and_patent = df[df["images"].apply(lambda x: (x.get('trademarks', {})) != {} and (x.get('copyrights', {})) != {})]
    df_with_trademark_copyright_and_patent = process_pictures_multiple_cases(df_db, df_with_trademark_copyright_and_patent, plaintiff_df, force=True)
    
    # All without IP
    df = df[(df["images"] == {'trademarks': {}, 'patents': {}, 'copyrights': {}}) & (df["cause"].notna())]
    df = df[df["nos_description"].str.contains("Copyrights")]
    df = df[1:]
    df = process_pictures_multiple_cases(df_db, df, plaintiff_df, force=True)

    